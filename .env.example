# GameFlex Mobile Environment Variables

# API Configuration
# Local development: http://127.0.0.1:3000
# Remote AWS API Gateway: https://wc19zcdkec.execute-api.us-west-2.amazonaws.com/v1
# Custom domain (staging/production): https://staging.api.gameflex.io or https://api.gameflex.io
API_BASE_URL=

# CloudFlare R2 Configuration
R2_PUBLIC_URL=

# Sign in with Apple Configuration
# The sign_in_with_apple package handles the authentication flow automatically
# No environment variables needed for frontend - backend handles verification

# Environment
ENVIRONMENT=development

# New Relic Configuration
# Development Environment
NEWRELIC_DEV_IOS_TOKEN=AA96c1da664ba74a448aa2c020c1bf8ad841c04160-NRMA
NEWRELIC_DEV_ANDROID_TOKEN=AA42b50187871c6e0aa947555fce1f84dd20d34b4b-NRMA

# Staging Environment
NEWRELIC_STAGING_IOS_TOKEN=AA91b40f75c22aba235b3284c9dc8bdf5aa45ce645-NRMA
NEWRELIC_STAGING_ANDROID_TOKEN=AA106d8c72378fc49a6b17b63d575cd641bb94a129-NRMA

# Production Environment
NEWRELIC_PROD_IOS_TOKEN=YOUR_PROD_IOS_TOKEN
NEWRELIC_PROD_ANDROID_TOKEN=YOUR_PROD_ANDROID_TOKEN