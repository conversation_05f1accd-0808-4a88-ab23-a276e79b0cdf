# GameFlex Mockup Server
# Serves the HTML/CSS mockup website locally for Figma import

Write-Host "🎮 Starting GameFlex Mockup Server..." -ForegroundColor Green
Write-Host "📁 Serving from: mockup/" -ForegroundColor Yellow
Write-Host "🔗 URL: http://localhost:8081" -ForegroundColor Cyan
Write-Host ""
Write-Host "📱 This mockup includes all GameFlex app screens:" -ForegroundColor Yellow
Write-Host "   • Splash Screen" -ForegroundColor White
Write-Host "   • Login/Signup Screen" -ForegroundColor White
Write-Host "   • Username Selection" -ForegroundColor White
Write-Host "   • Home Feed" -ForegroundColor White
Write-Host "   • Upload Screen" -ForegroundColor White
Write-Host "   • Profile Screen" -ForegroundColor White
Write-Host "   • Xbox Media Browser" -ForegroundColor White
Write-Host ""
Write-Host "🎨 Perfect for importing into Figma!" -ForegroundColor Green
Write-Host "Press Ctrl+C to stop the server" -ForegroundColor Yellow
Write-Host ""

# Check if mockup directory exists
if (-not (Test-Path "mockup")) {
    Write-Host "❌ mockup directory not found!" -ForegroundColor Red
    Write-Host "Please make sure you're running this from the project root" -ForegroundColor Yellow
    exit 1
}

# Change to mockup directory
Set-Location "mockup"

# Try different methods to start a web server
if (Get-Command python3 -ErrorAction SilentlyContinue) {
    Write-Host "Using Python 3 HTTP server..." -ForegroundColor Green
    python3 -m http.server 8081
}
elseif (Get-Command python -ErrorAction SilentlyContinue) {
    Write-Host "Using Python HTTP server..." -ForegroundColor Green
    python -m http.server 8081
}
elseif (Get-Command php -ErrorAction SilentlyContinue) {
    Write-Host "Using PHP built-in server..." -ForegroundColor Green
    php -S localhost:8081
}
elseif (Get-Command node -ErrorAction SilentlyContinue) {
    Write-Host "Using Node.js http-server (if available)..." -ForegroundColor Green
    if (Get-Command npx -ErrorAction SilentlyContinue) {
        npx http-server -p 8081
    } else {
        Write-Host "❌ http-server not found. Please install with: npm install -g http-server" -ForegroundColor Red
        exit 1
    }
}
else {
    Write-Host "❌ No suitable web server found!" -ForegroundColor Red
    Write-Host "Please install one of the following:" -ForegroundColor Yellow
    Write-Host "  • Python 3: python3 -m http.server" -ForegroundColor White
    Write-Host "  • Python 2: python -m SimpleHTTPServer" -ForegroundColor White
    Write-Host "  • PHP: php -S localhost:8081" -ForegroundColor White
    Write-Host "  • Node.js: npm install -g http-server" -ForegroundColor White
    exit 1
}
