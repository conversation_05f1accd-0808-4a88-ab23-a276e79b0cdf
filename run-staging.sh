#!/bin/bash

# GameFlex Mobile - Staging Run Script for macOS
# This script runs the app in staging mode

set -e  # Exit on any error

# Default values
PLATFORM="ios"
HELP=false
FORCE=false

# Parse command line arguments
while [[ $# -gt 0 ]]; do
    case $1 in
        -p|--platform)
            PLATFORM="$2"
            shift 2
            ;;
        -f|--force)
            FORCE=true
            shift
            ;;
        -h|--help)
            HELP=true
            shift
            ;;
        *)
            echo "Unknown option: $1"
            exit 1
            ;;
    esac
done

# Show help
if [ "$HELP" = true ]; then
    echo "GameFlex Mobile - Staging Run Script"
    echo ""
    echo "Usage: ./run-staging.sh [OPTIONS]"
    echo ""
    echo "Options:"
    echo "  -p, --platform PLATFORM  Target platform (ios, android, web, macos, linux)"
    echo "  -f, --force              Skip confirmation prompt"
    echo "  -h, --help               Show this help message"
    echo ""
    echo "Examples:"
    echo "  ./run-staging.sh                    # Run iOS staging"
    echo "  ./run-staging.sh -p android         # Run Android staging"
    echo "  ./run-staging.sh -p web             # Run Web staging"
    echo "  ./run-staging.sh -p macos           # Run macOS staging"
    echo "  ./run-staging.sh -p linux           # Run Linux staging"
    echo "  ./run-staging.sh -f                 # Run without confirmation"
    exit 0
fi

echo "🚀 Running GameFlex Mobile in STAGING mode"
echo "Platform: $PLATFORM"
echo ""

# Safety confirmation (unless forced)
if [ "$FORCE" != true ]; then
    echo "⚠️  WARNING: You are about to run the app in STAGING mode."
    echo "This will connect to the staging backend: https://staging.api.gameflex.io/v1"
    echo ""
    read -p "Are you sure you want to continue? (y/N): " -n 1 -r
    echo ""
    if [[ ! $REPLY =~ ^[Yy]$ ]]; then
        echo "❌ Cancelled by user"
        exit 1
    fi
fi

# Ensure we're in the correct directory
if [ ! -f "pubspec.yaml" ]; then
    echo "❌ Error: pubspec.yaml not found. Please run this script from the Flutter project root."
    exit 1
fi

# Run based on platform
PLATFORM_LOWER=$(echo "$PLATFORM" | tr '[:upper:]' '[:lower:]')
case "$PLATFORM_LOWER" in
    "ios")
        echo "🏗️ Running on iOS (staging)..."
        flutter run --release --dart-define=STAGING=true
        ;;
    "android")
        echo "🏗️ Running on Android (staging)..."
        flutter run --release --dart-define=STAGING=true --flavor staging
        ;;
    "web")
        echo "🏗️ Running on Web (staging)..."
        flutter run -d web-server --release --dart-define=STAGING=true --web-port 3000
        ;;
    "macos")
        echo "🏗️ Running on macOS (staging)..."
        flutter run -d macos --release --dart-define=STAGING=true
        ;;
    "linux")
        echo "🏗️ Running on Linux (staging)..."
        # Check if release build exists
        if [ -f "build/linux/x64/release/bundle/gameflex_mobile" ]; then
            echo "Running existing release build..."
            cd build/linux/x64/release/bundle/
            ./gameflex_mobile
        else
            echo "Release build not found. Building and running in release mode..."
            flutter run -d linux --release --dart-define=STAGING=true
        fi
        ;;
    *)
        echo "❌ Unsupported platform: $PLATFORM"
        echo "Supported platforms: ios, android, web, macos, linux"
        exit 1
        ;;
esac

if [ $? -ne 0 ]; then
    echo "❌ Run failed"
    exit 1
fi
