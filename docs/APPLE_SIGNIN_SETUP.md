# Apple Sign In Setup Guide

This guide will walk you through setting up Apple Sign In for the GameFlex app.

## Prerequisites

1. Apple Developer Account (paid membership required)
2. iOS app configured in App Store Connect
3. Access to Apple Developer Console

## Step 1: Configure App ID

1. Go to [Apple Developer Console](https://developer.apple.com/account/)
2. Navigate to **Certificates, Identifiers & Profiles**
3. Click on **Identifiers** in the sidebar
4. Find your app's App ID or create a new one
5. Edit the App ID and enable **Sign In with Apple** capability
6. Save the changes

## Step 2: Create a Services ID

1. In the **Identifiers** section, click the **+** button
2. Select **Services IDs** and click **Continue**
3. Fill in the details:
   - **Description**: GameFlex Apple Sign In
   - **Identifier**: `com.yourcompany.gameflex.signin` (use your own domain)
4. Click **Continue** and then **Register**
5. Select your newly created Services ID
6. Enable **Sign In with Apple**
7. Click **Configure** next to Sign In with Apple
8. Configure the settings:
   - **Primary App ID**: Select your app's App ID
   - **Web Domain**: Your backend domain (e.g., `api.gameflex.com`)
   - **Return URLs**: Add your callback URLs:
     - `https://your-api-domain.com/auth/apple/callback`
     - For development: `https://localhost:3000/auth/apple/callback`
9. Click **Save** and then **Continue** and **Register**

## Step 3: Create a Private Key

1. In the Apple Developer Console, go to **Keys** section
2. Click the **+** button to create a new key
3. Fill in the details:
   - **Key Name**: GameFlex Apple Sign In Key
   - **Enable**: Sign In with Apple
4. Click **Configure** next to Sign In with Apple
5. Select your **Primary App ID**
6. Click **Save**
7. Click **Continue** and then **Register**
8. **Download the key file** (.p8 file) - you can only download this once!
9. Note down the **Key ID** (10-character string)

## Step 4: Get Your Team ID

1. In the Apple Developer Console, go to **Membership** section
2. Your **Team ID** is displayed at the top of the page (10-character string)

## Step 5: Configure Environment Variables

Add the following environment variables to both `.env` files (root and backend):

```bash
# Apple Sign In Configuration
APPLE_TEAM_ID=YOUR_TEAM_ID_HERE
APPLE_CLIENT_ID=com.yourcompany.gameflex.signin
APPLE_KEY_ID=YOUR_KEY_ID_HERE
APPLE_PRIVATE_KEY="-----BEGIN PRIVATE KEY-----
YOUR_PRIVATE_KEY_CONTENT_HERE
-----END PRIVATE KEY-----"
```

### Getting the Private Key Content

1. Open the downloaded .p8 file in a text editor
2. Copy the entire content including the BEGIN and END lines
3. Replace newlines with `\n` or use the multiline format shown above
4. Make sure to wrap it in quotes

Example:
```bash
APPLE_PRIVATE_KEY="-----BEGIN PRIVATE KEY-----\nMIGTAgEAMBMGByqGSM49AgEGCCqGSM49AwEHBHkwdwIBAQQg...\n-----END PRIVATE KEY-----"
```

## Step 6: iOS App Configuration

### Add Capability to iOS Project

1. Open your iOS project in Xcode
2. Select your app target
3. Go to **Signing & Capabilities** tab
4. Click **+ Capability**
5. Add **Sign In with Apple**

### Update Info.plist (if needed)

The `sign_in_with_apple` Flutter plugin should handle this automatically, but if needed:

```xml
<key>com.apple.developer.applesignin</key>
<array>
    <string>Default</string>
</array>
```

## Step 7: Test the Implementation

1. Deploy your backend with the new environment variables
2. Run your Flutter app on a physical iOS device (Apple Sign In doesn't work in simulator)
3. Test the Apple Sign In flow

## Important Notes

### Security Considerations

1. **Never commit private keys to version control**
2. Use environment variables or secure secret management
3. The private key can only be downloaded once from Apple
4. Store the private key securely and back it up

### Testing

1. Apple Sign In only works on physical iOS devices
2. The iOS Simulator will show the button but authentication will fail
3. Test with different Apple IDs to ensure proper user creation/linking

### Production Deployment

1. Update the Services ID configuration with your production domain
2. Ensure all environment variables are properly set in production
3. Test the complete flow in production environment

## Troubleshooting

### Common Issues

1. **"Invalid client" error**: Check your APPLE_CLIENT_ID matches your Services ID
2. **"Invalid key" error**: Verify your private key format and Key ID
3. **Button not showing**: Apple Sign In is only available on iOS 13+ and macOS 10.15+
4. **Authentication fails**: Ensure you're testing on a physical device, not simulator

### Debug Steps

1. Check backend logs for detailed error messages
2. Verify all environment variables are set correctly
3. Ensure your Services ID is properly configured with correct domains
4. Test with a fresh Apple ID that hasn't been used with your app before

## Support

For additional help:
- [Apple Sign In Documentation](https://developer.apple.com/documentation/sign_in_with_apple)
- [Flutter sign_in_with_apple Plugin](https://pub.dev/packages/sign_in_with_apple)
