# Xbox Integration Testing Guide

This guide provides step-by-step instructions for testing the direct Xbox Live API integration after migrating from OpenXBL.

## Prerequisites

Before testing, ensure you have completed the setup steps in `XBOX_INTEGRATION_SETUP.md`:

1. ✅ Created Azure app registration
2. ✅ Configured Azure app settings and permissions
3. ✅ Updated GameFlex backend configuration
4. ✅ Deployed updated backend code

## Testing Steps

### Step 1: Verify Azure Configuration

1. **Check Azure App Registration**:
   ```bash
   # Verify your Azure app exists and has correct settings
   # Go to https://portal.azure.com → App registrations
   # Find your "GameFlex Xbox Integration" app
   # Verify redirect URI: https://dev.api.gameflex.io/xbox/callback
   ```

2. **Test OAuth URL Generation**:
   ```bash
   # Test that the backend generates correct Microsoft OAuth URLs
   curl -X GET "https://dev.api.gameflex.io/xbox/auth" \
     -H "Authorization: Bearer YOUR_JWT_TOKEN"
   
   # Expected response:
   # {
   #   "authUrl": "https://login.microsoftonline.com/consumers/oauth2/v2.0/authorize?client_id=...",
   #   "message": "Redirect user to this URL to authenticate with Xbox Live"
   # }
   ```

### Step 2: Test OAuth Flow

1. **Start Xbox Authentication**:
   - Open GameFlex mobile app
   - Navigate to Settings → Link Xbox Account
   - Tap "Link Xbox Account" button

2. **Complete Microsoft OAuth**:
   - Browser should open to Microsoft login page
   - Sign in with your Microsoft/Xbox account
   - Grant permissions when prompted
   - Should redirect back to GameFlex app

3. **Verify Account Linking**:
   ```bash
   # Check if Xbox account was linked successfully
   curl -X GET "https://dev.api.gameflex.io/xbox/account" \
     -H "Authorization: Bearer YOUR_JWT_TOKEN"
   
   # Expected response:
   # {
   #   "xboxAccount": {
   #     "id": "...",
   #     "gamertag": "YourGamertag",
   #     "xboxUserId": "...",
   #     "profilePictureUrl": "...",
   #     "linkedAt": "2024-01-01T00:00:00.000Z"
   #   }
   # }
   ```

### Step 3: Test Xbox Media Retrieval

1. **Test Screenshots Endpoint**:
   ```bash
   # Fetch user's Xbox screenshots
   curl -X GET "https://dev.api.gameflex.io/xbox/screenshots?numItems=5" \
     -H "Authorization: Bearer YOUR_JWT_TOKEN"
   
   # Expected response:
   # {
   #   "screenshots": [
   #     {
   #       "id": "...",
   #       "type": "screenshot",
   #       "title": "Screenshot",
   #       "thumbnailUrl": "https://...",
   #       "downloadUrl": "https://...",
   #       "gameTitle": "Game Name",
   #       "dateTaken": "2024-01-01T00:00:00.000Z",
   #       "platform": "Xbox"
   #     }
   #   ],
   #   "pagination": {
   #     "hasMore": false,
   #     "contToken": null
   #   }
   # }
   ```

2. **Test Game Clips Endpoint**:
   ```bash
   # Fetch user's Xbox game clips
   curl -X GET "https://dev.api.gameflex.io/xbox/gameclips?numItems=5" \
     -H "Authorization: Bearer YOUR_JWT_TOKEN"
   
   # Expected response similar to screenshots but with "gameclip" type
   ```

### Step 4: Test Mobile App Integration

1. **Test Xbox Media Browser**:
   - Open GameFlex app
   - Navigate to Create Post → Xbox Media
   - Should see your Xbox screenshots and game clips
   - Try selecting and importing media

2. **Test Media Import**:
   - Select an Xbox screenshot or game clip
   - Confirm import
   - Verify media appears in your post draft

### Step 5: Error Handling Tests

1. **Test Expired Token**:
   - Wait for Xbox token to expire (typically 24 hours)
   - Try accessing Xbox media
   - Should receive error prompting to re-link account

2. **Test Unlink Account**:
   ```bash
   # Unlink Xbox account
   curl -X DELETE "https://dev.api.gameflex.io/xbox/account" \
     -H "Authorization: Bearer YOUR_JWT_TOKEN"
   
   # Expected response:
   # {
   #   "message": "Xbox account unlinked successfully"
   # }
   ```

3. **Test Access After Unlinking**:
   ```bash
   # Try accessing Xbox media after unlinking
   curl -X GET "https://dev.api.gameflex.io/xbox/screenshots" \
     -H "Authorization: Bearer YOUR_JWT_TOKEN"
   
   # Expected response:
   # {
   #   "error": "No Xbox account linked"
   # }
   ```

## Troubleshooting

### Common Issues and Solutions

1. **"Invalid client" error**:
   - Verify Azure client ID and secret are correct in AWS Secrets Manager
   - Check redirect URI matches exactly: `https://dev.api.gameflex.io/xbox/callback`

2. **"Xbox token expired" error**:
   - Normal behavior - Xbox tokens expire after 24 hours
   - User needs to re-link their Xbox account

3. **"No Xbox account linked" error**:
   - User hasn't completed OAuth flow
   - Check that callback endpoint is working properly

4. **Empty screenshots/clips response**:
   - User may not have any Xbox media
   - Try with a different Xbox account that has screenshots/clips

### Debug Commands

```bash
# Check AWS Secrets Manager configuration
aws secretsmanager get-secret-value --secret-id 'gameflex-xbox-config-development'

# Check backend logs
aws logs tail /aws/lambda/gameflex-development-XboxFunction --follow

# Test Xbox Live API directly (replace tokens)
curl -X GET "https://screenshotmetadata.xboxlive.com/users/me/screenshots?maxItems=5" \
  -H "Authorization: XBL3.0 x=USER_HASH;XSTS_TOKEN" \
  -H "x-xbl-contract-version: 5"
```

## Performance Testing

1. **Load Testing**:
   - Test with multiple concurrent users
   - Verify rate limiting works correctly
   - Monitor AWS Lambda performance

2. **Media Loading**:
   - Test with users who have many screenshots/clips
   - Verify pagination works correctly
   - Test download speeds for large media files

## Success Criteria

✅ **OAuth Flow**: Users can successfully link Xbox accounts via Microsoft OAuth  
✅ **Media Retrieval**: Screenshots and game clips load correctly  
✅ **Mobile Integration**: Xbox media appears in GameFlex app  
✅ **Error Handling**: Appropriate errors for expired tokens and unlinked accounts  
✅ **Performance**: Reasonable response times for all endpoints  

## Next Steps

After successful testing:

1. **Deploy to Staging**: Test with staging environment
2. **User Acceptance Testing**: Have beta users test the integration
3. **Production Deployment**: Deploy to production environment
4. **Monitor Usage**: Set up monitoring and alerts for Xbox integration

## Support

If you encounter issues during testing:

1. Check the troubleshooting section above
2. Review backend logs in AWS CloudWatch
3. Verify Azure app configuration in Azure Portal
4. Test Xbox Live API endpoints directly
