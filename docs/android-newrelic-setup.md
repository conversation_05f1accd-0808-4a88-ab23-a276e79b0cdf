# Android New Relic Setup Guide

This document outlines the Android-specific configuration for New Relic integration in the GameFlex Flutter app.

## ✅ Configuration Complete

The following Android configuration has been applied to your project:

### 1. Plugin DSL Configuration (Modern Gradle)

#### settings.gradle.kts
```kotlin
plugins {
    id("dev.flutter.flutter-plugin-loader") version "1.0.0"
    id("com.android.application") version "8.7.0" apply false
    id("org.jetbrains.kotlin.android") version "1.8.22" apply false
    id("com.newrelic.agent.android") version "7.6.7" apply false  // ✅ Added
}
```

#### app/build.gradle.kts
```kotlin
plugins {
    id("com.android.application")
    id("kotlin-android")
    id("dev.flutter.flutter-gradle-plugin")
    id("com.newrelic.agent.android")  // ✅ Added
}
```

### 2. Required Permissions

#### AndroidManifest.xml
```xml
<!-- Network permissions for New Relic -->
<uses-permission android:name="android.permission.INTERNET" />
<uses-permission android:name="android.permission.ACCESS_NETWORK_STATE" />
```

✅ **Already present** in your `android/app/src/main/AndroidManifest.xml`

## Build Configuration Details

### Plugin Version
- **New Relic Android Agent**: `7.6.7` (latest stable version)
- **Gradle Plugin**: Uses modern Plugin DSL syntax
- **Compatibility**: Android API 24+ (matches your minSdk)

### Environment Support
Your existing product flavors will work seamlessly with New Relic:
- **Development**: `com.gameflex.app.dev`
- **Staging**: `com.gameflex.app.staging`
- **Production**: `com.gameflex.app` (default)

Each environment will use its respective New Relic tokens as configured in your `.env` files.

## Build Commands

### Development Build
```bash
flutter build apk --flavor development
```

### Staging Build
```bash
flutter build apk --flavor staging
```

### Production Build
```bash
flutter build apk --release
```

## Verification

After building your app, you can verify the New Relic integration:

1. **Build the app** with any of the above commands
2. **Check build logs** for New Relic plugin messages
3. **Run the app** and check New Relic dashboard for data
4. **Look for** automatic instrumentation of network requests

## Troubleshooting

### Build Issues

If you encounter build issues:

1. **Clean the project**:
   ```bash
   flutter clean
   cd android && ./gradlew clean && cd ..
   flutter pub get
   ```

2. **Check Gradle version compatibility**:
   - Ensure your Gradle version supports the New Relic plugin
   - Current setup uses Gradle 8.7.0 which is compatible

3. **Verify plugin application**:
   - The New Relic plugin should be applied after the Android plugin
   - Order in `build.gradle.kts` is correct

### Runtime Issues

If New Relic data doesn't appear:

1. **Check network permissions** (already configured)
2. **Verify tokens** in your `.env` files
3. **Check app logs** for New Relic initialization messages
4. **Ensure internet connectivity** during testing

## ProGuard Configuration

If you enable ProGuard/R8 in release builds, add these rules to `android/app/proguard-rules.pro`:

```proguard
# New Relic
-keep class com.newrelic.** { *; }
-dontwarn com.newrelic.**
-keepattributes Exceptions,InnerClasses,Signature,Deprecated,SourceFile,LineNumberTable,*Annotation*,EnclosingMethod
```

## Next Steps

1. **Build your app** using the commands above
2. **Test on device/emulator** to verify integration
3. **Check New Relic dashboard** for incoming data
4. **Monitor build logs** for any New Relic plugin messages

The Android configuration is now complete and ready for use with your New Relic integration!
