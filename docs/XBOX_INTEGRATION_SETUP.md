# Xbox Integration Setup Guide

This guide walks you through setting up Xbox Live integration for GameFlex using direct Xbox Live API access via Azure app registration.

## Prerequisites

- AWS CLI configured with appropriate permissions
- GameFlex backend deployed
- Microsoft Azure account (for app registration)

## Overview

The Xbox integration uses direct Microsoft OAuth 2.0 authentication with Xbox Live API, allowing each GameFlex user to authorize their own Xbox account. This provides:

- ✅ **Direct Xbox Live API access** - No third-party intermediary required
- ✅ **Individual user authorization** - Each user links their own Xbox account
- ✅ **Full Xbox data access** - Screenshots, game clips, and profile data
- ✅ **Scalable** - Supports unlimited users
- ✅ **Free** - No API usage costs or rate limits beyond Xbox Live's standard limits

## Step 1: Create Microsoft Azure App Registration

1. **Go to Azure Portal**: Visit [portal.azure.com](https://portal.azure.com)
2. **Navigate to App Registrations**: Search for "App Registrations" in the top search bar and select it
3. **Create New Registration**: Click "New Registration"
4. **Configure the registration**:
   - **Name**: `GameFlex Xbox Integration`
   - **Supported account types**: Select "Personal Microsoft accounts only" (this is crucial for Xbox Live access)
   - **Redirect URI**:
     - Platform: `Web`
     - URI: `https://dev.api.gameflex.io/xbox/callback` (for development)
     - **Note**: You'll need to add additional redirect URIs for staging and production later
5. **Click "Register"** to create the app

## Step 2: Configure Azure App Settings

### 2.1 Copy Application (Client) ID
1. **Go to Overview** tab of your newly created app registration
2. **Copy the Application (Client) ID** - you'll need this for GameFlex backend configuration

### 2.2 Create Client Secret
1. **Go to "Certificates & secrets"** in the left sidebar
2. **Click "New client secret"**
3. **Configure the secret**:
   - **Description**: `GameFlex Xbox Integration Secret`
   - **Expires**: Choose 24 months (recommended)
4. **Click "Add"** and immediately **copy the secret value** - you won't be able to see it again

### 2.3 Configure Authentication Settings
1. **Go to "Authentication"** in the left sidebar
2. **Add additional redirect URIs** for all environments:
   - Click "Add URI" and add:
     - `https://staging.api.gameflex.io/xbox/callback` (for staging)
     - `https://api.gameflex.io/xbox/callback` (for production)
   - You should now have all three redirect URIs configured:
     - `https://dev.api.gameflex.io/xbox/callback`
     - `https://staging.api.gameflex.io/xbox/callback`
     - `https://api.gameflex.io/xbox/callback`
3. **Under "Implicit grant and hybrid flows"**, check:
   - ✅ **Access tokens**
   - ✅ **ID tokens**
4. **Under "Advanced settings"**, set:
   - **Allow public client flows**: Yes
5. **Click "Save"**

### 2.4 Configure API Permissions
1. **Go to "API permissions"** in the left sidebar
2. **Click "Add a permission"**
3. **Select "Microsoft Graph"**
4. **Select "Delegated permissions"**
5. **Add these permissions**:
   - `XboxLive.signin` - Sign in to Xbox Live
   - `XboxLive.offline_access` - Maintain access to Xbox Live data
   - `User.Read` - Read user profile
6. **Click "Add permissions"**
7. **Click "Grant admin consent"** (if you have admin rights) or ask your admin to grant consent
## Step 3: Configure GameFlex Backend

Update the app configuration secret with your Azure app credentials:

```bash
aws secretsmanager put-secret-value \
  --secret-id 'gameflex-app-config-development' \
  --secret-string '{
    "cloudflareApiToken":"YOUR_CLOUDFLARE_TOKEN",
    "testUserEmail":"<EMAIL>",
    "testUserPassword":"Test123!",
    "debugMode":"development",
    "apiBaseUrl":"YOUR_API_URL",
    "userPoolId":"YOUR_USER_POOL_ID",
    "userPoolClientId":"YOUR_USER_POOL_CLIENT_ID",
    "azureClientId":"YOUR_AZURE_CLIENT_ID",
    "azureClientSecret":"YOUR_AZURE_CLIENT_SECRET"
  }'
```

Replace:
- `YOUR_AZURE_CLIENT_ID` with the Application (Client) ID from Azure
- `YOUR_AZURE_CLIENT_SECRET` with the Client Secret from Azure

## Step 4: How the Direct Xbox API Integration Works

### 4.1 OAuth 2.0 Flow Overview

The integration uses the standard OAuth 2.0 authorization code flow with Microsoft identity platform:

1. **User clicks "Link Xbox Account"** in GameFlex
2. **Redirect to Microsoft OAuth**: `https://login.microsoftonline.com/consumers/oauth2/v2.0/authorize`
3. **User logs into Microsoft/Xbox** (handled by Microsoft)
4. **Redirect back to GameFlex**: `https://dev.api.gameflex.io/xbox/callback?code=ABC123`
5. **GameFlex backend exchanges code for tokens**: POST to Microsoft token endpoint
6. **Get Xbox Live tokens**: Use Microsoft access token to get Xbox Live authentication tokens
7. **Make Xbox API calls**: Use Xbox Live tokens to access user's data

### 4.2 Xbox Live API Authentication Steps

The backend performs these steps to authenticate with Xbox Live:

1. **Exchange OAuth code for Microsoft access token**
2. **Authenticate with Xbox Live using Microsoft token**:
   - POST to `https://user.auth.xboxlive.com/user/authenticate`
   - Receive Xbox Live token
3. **Get XSTS (Xbox Secure Token Service) token**:
   - POST to `https://xsts.auth.xboxlive.com/xsts/authorize`
   - Receive XSTS token for API access
4. **Use XSTS token for Xbox Live API calls**

### 4.3 Available Xbox Live API Endpoints

Once authenticated, the backend can access these Xbox Live APIs:

- **Screenshots**: `https://screenshotmetadata.xboxlive.com/users/me/screenshots`
- **Game Clips**: `https://gameclipsmetadata.xboxlive.com/users/me/clips`
- **Profile**: `https://profile.xboxlive.com/users/me/profile/settings`
- **Friends**: `https://social.xboxlive.com/users/me/people`

### 4.4 GameFlex API Endpoints

GameFlex will provide these endpoints to the frontend:

- `GET /xbox/auth` - Start Xbox authentication flow
- `GET /xbox/callback` - Handle OAuth callback
- `POST /xbox/link` - Complete account linking
- `GET /xbox/profile` - Get user's Xbox profile
- `GET /xbox/screenshots` - Get user's screenshots
- `GET /xbox/gameclips` - Get user's game clips
- `GET /xbox/search/{gamertag}` - Search for Xbox profiles

## Step 5: Deploy Updated Backend

```bash
cd backend
./deploy.sh -y development
```

## Step 6: Testing the Integration

### 6.1 Test Azure App Registration

1. **Test the OAuth URL**: Visit the Microsoft OAuth URL with your client ID:
   ```
   https://login.microsoftonline.com/consumers/oauth2/v2.0/authorize?client_id=YOUR_CLIENT_ID&response_type=code&redirect_uri=https://dev.api.gameflex.io/xbox/callback&scope=XboxLive.signin%20XboxLive.offline_access
   ```
2. **Login with Xbox account**: Complete the Microsoft OAuth flow
3. **Verify redirect**: Should redirect to your callback URL with a code parameter
4. **Check Azure logs**: Verify authentication requests in Azure portal

### 6.2 Test GameFlex Integration

Once the backend is updated:

```bash
# Test Xbox authentication start
curl -X GET "https://dev.api.gameflex.io/xbox/auth" \
  -H "Authorization: Bearer YOUR_JWT_TOKEN"

# Test gamertag search (public lookup)
curl -X GET "https://dev.api.gameflex.io/xbox/search/MajorNelson" \
  -H "Authorization: Bearer YOUR_JWT_TOKEN"

# Test user's Xbox profile (after linking)
curl -X GET "https://dev.api.gameflex.io/xbox/profile" \
  -H "Authorization: Bearer YOUR_JWT_TOKEN"

# Test user's screenshots
curl -X GET "https://dev.api.gameflex.io/xbox/screenshots" \
  -H "Authorization: Bearer YOUR_JWT_TOKEN"

# Test user's game clips
curl -X GET "https://dev.api.gameflex.io/xbox/gameclips" \
  -H "Authorization: Bearer YOUR_JWT_TOKEN"
```

## Step 7: Advantages of Direct Xbox API vs Third-Party Services

| Feature | Direct Xbox API | Third-Party Services |
|---------|-----------------|---------------------|
| **No Intermediary** | ✅ Direct Microsoft integration | ❌ Relies on third-party service |
| **User Authorization** | ✅ Each user authorizes individually | ✅ Individual authorization |
| **Scalability** | ✅ Unlimited users | ⚠️ Depends on service limits |
| **Setup Complexity** | ✅ Simple (Azure only) | ⚠️ Moderate (Azure + third-party) |
| **Cost** | ✅ Free | ⚠️ May have usage costs |
| **Reliability** | ✅ Microsoft SLA | ⚠️ Depends on third-party uptime |
| **Screenshots/Clips** | ✅ Full access | ✅ Full access |
| **API Rate Limits** | ✅ Microsoft's generous limits | ⚠️ Third-party imposed limits |

## Troubleshooting

### Common Issues

1. **"Invalid redirect_uri" error (AADSTS90023)**
   - This error occurs when the redirect URI doesn't match what's registered in Azure
   - **For staging**: Ensure `https://staging.api.gameflex.io/xbox/callback` is added to Azure app registration
   - **For production**: Ensure `https://api.gameflex.io/xbox/callback` is added to Azure app registration
   - Check that all three redirect URIs are configured in Azure Authentication settings:
     - `https://dev.api.gameflex.io/xbox/callback`
     - `https://staging.api.gameflex.io/xbox/callback`
     - `https://api.gameflex.io/xbox/callback`

2. **"Invalid client" error**
   - Verify your Azure client ID and secret are correct
   - Check that the redirect URI matches exactly
   - Ensure "Access tokens" and "ID tokens" are enabled in Azure
   - Verify account type is set to "Personal Microsoft accounts only"

3. **"Invalid scope" error**
   - Ensure you've added the correct API permissions in Azure
   - Check that `XboxLive.signin` and `XboxLive.offline_access` scopes are granted
   - Verify admin consent has been granted for the permissions

4. **"Unauthorized" when calling Xbox Live APIs**
   - Check that the user has completed the OAuth flow
   - Verify the XSTS token is being generated correctly
   - Ensure you're using the correct Xbox Live API endpoints
   - Check that the Authorization header format is correct

5. **"No Xbox account linked"**
   - User needs to complete the full OAuth flow
   - Check that the callback endpoint is working
   - Verify the authorization code is being exchanged for tokens correctly
   - Verify the authorization code exchange is successful

### Debug Steps

1. **Check Azure app logs** in Azure Portal under "Sign-ins"
2. **Test the OAuth URL** manually in a browser
3. **Verify Azure app configuration** in Azure Portal
4. **Check GameFlex backend logs** for API errors
5. **Test Xbox Live API endpoints** directly with curl

### Example Debug Commands

```bash
# Test Microsoft token endpoint
curl -X POST "https://login.microsoftonline.com/consumers/oauth2/v2.0/token" \
  -H "Content-Type: application/x-www-form-urlencoded" \
  -d "client_id=YOUR_CLIENT_ID&client_secret=YOUR_CLIENT_SECRET&code=AUTH_CODE&grant_type=authorization_code&redirect_uri=https://dev.api.gameflex.io/xbox/callback"

# Test Xbox Live authentication
curl -X POST "https://user.auth.xboxlive.com/user/authenticate" \
  -H "Content-Type: application/json" \
  -H "x-xbl-contract-version: 1" \
  -d '{"Properties":{"AuthMethod":"RPS","SiteName":"user.auth.xboxlive.com","RpsTicket":"d=ACCESS_TOKEN"},"RelyingParty":"http://auth.xboxlive.com","TokenType":"JWT"}'
```

## API Rate Limits

Xbox Live API has generous rate limits:
- **Standard limits**: Thousands of requests per hour per user
- **No usage costs**: Free to use with Microsoft account
- **Enterprise limits**: Contact Microsoft for higher limits if needed

## Support and Resources

- [Microsoft Identity Platform Documentation](https://docs.microsoft.com/en-us/azure/active-directory/develop/)
- [Xbox Live API Documentation](https://docs.microsoft.com/en-us/gaming/xbox-live/api-ref/xbox-live-rest/)
- [Azure Portal](https://portal.azure.com)
- [Microsoft Graph Explorer](https://developer.microsoft.com/en-us/graph/graph-explorer) (for testing)

## Security Best Practices

1. **Client Secrets**: Store Azure client secrets securely in AWS Secrets Manager
2. **Token Storage**: Encrypt Xbox Live tokens in the database
3. **HTTPS Only**: Always use HTTPS for OAuth flows
4. **Token Expiration**: Implement proper token refresh logic
5. **Scope Limitation**: Only request necessary OAuth scopes
6. **Input Validation**: Validate all API inputs and responses

## Next Steps

After completing this setup:

1. **Update backend code** to use direct Xbox Live API
2. **Test the complete flow** from Xbox authentication to media browsing
3. **Implement error handling** for various failure scenarios
4. **Add user feedback** for authentication status
5. **Monitor API usage** and implement caching where appropriate
6. **Set up token refresh** for long-term access

## Implementation Details

The backend implementation will need to handle:

1. **OAuth 2.0 Flow**: Standard Microsoft OAuth implementation
2. **Xbox Live Authentication**: Convert Microsoft tokens to Xbox Live tokens
3. **XSTS Token Generation**: Get Xbox Secure Token Service tokens
4. **API Calls**: Use XSTS tokens for Xbox Live API requests
5. **Token Management**: Store and refresh tokens as needed

This approach provides direct access to Xbox Live APIs without relying on third-party services, ensuring better reliability, performance, and cost-effectiveness.
