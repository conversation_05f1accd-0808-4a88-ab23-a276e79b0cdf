/* GameFlex Landing Page Styles */

/* CSS Variables for GameFlex Colors */
:root {
    /* Main brand colors */
    --gf-green: #28F4C3;
    --gf-dark-background: #2A3642;
    --gf-dark-background-60: rgba(42, 54, 66, 0.6);
    --gf-card-background: #1A2530;

    /* Text colors */
    --gf-off-white: #F4F4F4;
    --gf-gray-text: rgba(244, 244, 244, 0.5);
    --gf-text-light: #8FAABD;

    /* Additional colors */
    --gf-blue: #00D5FF;
    --gf-yellow: #F2DE76;

    /* Gradient colors */
    --gf-light-teal: #8BE4CF;
    --gf-teal: #2BA5A5;
    --gf-dark-blue: #293642;
}

/* Reset and Base Styles */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Roboto', sans-serif;
    background: linear-gradient(135deg, var(--gf-light-teal) 0%, var(--gf-teal) 50%, var(--gf-dark-blue) 100%);
    color: var(--gf-off-white);
    min-height: 100vh;
    line-height: 1.6;
}

.container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 20px;
    min-height: 100vh;
    display: flex;
    flex-direction: column;
}

/* Header Styles */
.header {
    padding: 40px 0;
    text-align: center;
}

.logo-container {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 20px;
}

.app-icon {
    width: 120px;
    height: 120px;
    object-fit: contain;
}

.text-logo {
    width: 280px;
    height: auto;
    object-fit: contain;
}

/* Hero Section */
.hero {
    flex: 1;
    display: flex;
    align-items: center;
    justify-content: center;
    text-align: center;
    padding: 40px 0 60px 0;
}

.hero-content {
    max-width: 600px;
}

.hero-title {
    font-size: 3.5rem;
    font-weight: 700;
    margin-bottom: 20px;
    background: linear-gradient(45deg, var(--gf-green), var(--gf-blue));
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.hero-description {
    font-size: 1.2rem;
    color: var(--gf-off-white);
    margin-bottom: 40px;
    opacity: 0.9;
}

/* Download Badges */
.download-buttons {
    display: flex;
    gap: 20px;
    justify-content: center;
    flex-wrap: wrap;
    align-items: center;
}

.download-badge {
    display: inline-block;
    transition: all 0.3s ease;
    text-decoration: none;
}

.download-badge:hover {
    transform: translateY(-2px);
    filter: brightness(1.1);
}

.app-store-badge,
.google-play-badge {
    height: 60px;
    width: auto;
    max-width: 200px;
    object-fit: contain;
}

/* Features Section */
.features {
    padding: 80px 0;
    background: rgba(26, 37, 48, 0.3);
    border-radius: 20px;
    margin: 40px 0;
}

.features-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 40px;
    padding: 0 20px;
    align-items: stretch;
}

.feature-card {
    text-align: center;
    padding: 30px 20px;
    background: var(--gf-card-background);
    border-radius: 16px;
    border: 1px solid rgba(40, 244, 195, 0.2);
    transition: transform 0.3s ease, box-shadow 0.3s ease;
    display: flex;
    flex-direction: column;
    height: 100%;
}

.feature-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 10px 30px rgba(40, 244, 195, 0.2);
}

.feature-icon {
    font-size: 3rem;
    margin-bottom: 20px;
    color: var(--gf-green);
}

.feature-icon i {
    color: var(--gf-green);
}

.gaming-platforms .platform-icon {
    font-size: 3rem;
}

.platform-icon {
    font-size: 2.5rem;
    transition: all 0.3s ease;
    opacity: 0.8;
    cursor: pointer;
}

.platform-icon:hover {
    opacity: 1;
    transform: scale(1.15);
}

.platform-icon.xbox {
    color: #107C10;
}

.platform-icon.playstation {
    color: #003791;
}

.platform-icon.steam {
    color: #171A21;
}



.feature-card h3 {
    font-size: 1.5rem;
    color: var(--gf-green);
    margin-bottom: 15px;
    font-weight: 500;
}

.feature-card p {
    color: var(--gf-text-light);
    line-height: 1.6;
    margin-top: auto;
}

/* Footer */
.footer {
    text-align: center;
    padding: 40px 0;
    color: var(--gf-gray-text);
    border-top: 1px solid rgba(40, 244, 195, 0.2);
    margin-top: auto;
}

.footer-content {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 15px;
}

.footer-links {
    display: flex;
    align-items: center;
    gap: 15px;
    flex-wrap: wrap;
    justify-content: center;
}

.footer-link {
    color: var(--gf-text-light);
    text-decoration: none;
    font-size: 0.9rem;
    transition: color 0.3s ease;
}

.footer-link:hover {
    color: var(--gf-green);
}

.footer-separator {
    color: var(--gf-gray-text);
    font-size: 0.8rem;
}

.footer-copyright {
    font-size: 0.85rem;
    margin: 0;
}

/* Responsive Design */
@media (max-width: 768px) {
    .hero {
        padding: 20px 0 40px 0;
    }

    .hero-title {
        font-size: 2.5rem;
    }

    .hero-description {
        font-size: 1rem;
    }

    .download-buttons {
        flex-direction: row;
        justify-content: center;
        gap: 15px;
        flex-wrap: wrap;
    }

    .app-store-badge,
    .google-play-badge {
        height: 50px;
        max-width: 160px;
    }

    .text-logo {
        width: 240px;
    }

    .app-icon {
        width: 100px;
        height: 100px;
    }

    .features-grid {
        grid-template-columns: 1fr;
        gap: 30px;
    }

    .footer-links {
        flex-direction: column;
        gap: 10px;
    }

    .footer-separator {
        display: none;
    }
}

@media (max-width: 480px) {
    .container {
        padding: 0 15px;
    }

    .hero {
        padding: 15px 0 30px 0;
    }

    .hero-title {
        font-size: 2rem;
    }

    .download-buttons {
        gap: 10px;
    }

    .app-store-badge,
    .google-play-badge {
        height: 45px;
        max-width: 140px;
    }

    .text-logo {
        width: 200px;
    }

    .app-icon {
        width: 80px;
        height: 80px;
    }

    .features-grid {
        grid-template-columns: 1fr;
        gap: 30px;
    }
}

/* Extra small screens */
@media (max-width: 360px) {

    .app-store-badge,
    .google-play-badge {
        height: 40px;
        max-width: 120px;
    }

    .download-buttons {
        gap: 8px;
    }
}