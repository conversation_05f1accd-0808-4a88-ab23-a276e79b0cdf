# Security and Performance Headers for Cloudflare Pages

/*
  # Security Headers
  X-Frame-Options: DENY
  X-Content-Type-Options: nosniff
  X-XSS-Protection: 1; mode=block
  Referrer-Policy: strict-origin-when-cross-origin
  Permissions-Policy: camera=(), microphone=(), geolocation=(), payment=()
  
  # Performance Headers
  Cache-Control: public, max-age=31536000, immutable

# Static Assets - Long cache
/assets/*
  Cache-Control: public, max-age=31536000, immutable
  
# CSS and JS files
/*.css
  Cache-Control: public, max-age=31536000, immutable
  Content-Type: text/css; charset=utf-8

/*.js
  Cache-Control: public, max-age=31536000, immutable
  Content-Type: application/javascript; charset=utf-8

# HTML files - Short cache for updates
/*.html
  Cache-Control: public, max-age=3600
  Content-Type: text/html; charset=utf-8

# Root HTML
/
  Cache-Control: public, max-age=3600
  Content-Type: text/html; charset=utf-8

# Image files
/*.svg
  Cache-Control: public, max-age=31536000, immutable
  Content-Type: image/svg+xml

/*.png
  Cache-Control: public, max-age=31536000, immutable
  Content-Type: image/png

# Favicon and manifest files
/favicon.ico
  Cache-Control: public, max-age=86400
  Content-Type: image/x-icon

/apple-touch-icon.png
  Cache-Control: public, max-age=86400
  Content-Type: image/png

/robots.txt
  Cache-Control: public, max-age=86400
  Content-Type: text/plain; charset=utf-8

/manifest.json
  Cache-Control: public, max-age=86400
  Content-Type: application/manifest+json; charset=utf-8

/sitemap.xml
  Cache-Control: public, max-age=86400
  Content-Type: application/xml; charset=utf-8
