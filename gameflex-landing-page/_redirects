# Cloudflare Pages Redirects Configuration

# Redirect common variations to canonical URLs
/home /
/index /
/index.htm /
/index.php /

# <PERSON>le trailing slashes consistently
/about/ /about
/contact/ /contact
/download/ /download

# App store redirects (when you have actual app store links)
# /ios https://apps.apple.com/app/gameflex/id123456789 302
# /android https://play.google.com/store/apps/details?id=com.gameflex.app 302
# /app-store https://apps.apple.com/app/gameflex/id123456789 302
# /play-store https://play.google.com/store/apps/details?id=com.gameflex.app 302

# Social media redirects (when you have social accounts)
# /twitter https://twitter.com/gameflex 302
# /discord https://discord.gg/gameflex 302
# /reddit https://reddit.com/r/gameflex 302

# Fallback for SPA routing (if you add JavaScript routing later)
/* /index.html 200
