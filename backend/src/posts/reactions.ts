import { APIGatewayProxyEvent, APIGatewayProxyResult } from 'aws-lambda';
import { DynamoDBClient } from '@aws-sdk/client-dynamodb';
import { DynamoDBDocumentClient, PutCommand, DeleteCommand, QueryCommand } from '@aws-sdk/lib-dynamodb';

const dynamoClient = new DynamoDBClient({ region: process.env.AWS_REGION || 'us-east-1' });
const dynamodb = DynamoDBDocumentClient.from(dynamoClient);

const POST_REACTIONS_TABLE = process.env.POST_REACTIONS_TABLE as string;

const createResponse = (statusCode: number, body: any): APIGatewayProxyResult => ({
  statusCode,
  headers: {
    'Content-Type': 'application/json',
    'Access-Control-Allow-Origin': '*',
    'Access-Control-Allow-Headers': 'Content-Type,X-Amz-Date,Authorization,X-Api-Key,X-Amz-Security-Token',
    'Access-Control-Allow-Methods': 'GET,POST,PUT,DELETE,OPTIONS',
  },
  body: JSON.stringify(body),
});

const getUserIdFromContext = (event: APIGatewayProxyEvent): string | null => {
  if (event.requestContext && event.requestContext.authorizer) {
    return (event.requestContext.authorizer as any).userId;
  }
  return null;
};

// Curated emoji whitelist - basic reactions, extended reactions, and emotions only
const ALLOWED_EMOJIS = new Set([
  // Basic reactions
  '👍', '👎', '❤️', '😂', '😮', '😢', '😡', '🔥',
  // Extended reactions
  '👏', '🎉', '💯', '✨', '⚡', '💪', '🙌', '👀',
  // Emotions
  '🤔', '😍', '🥰', '😊', '😎', '🤩', '😴', '🤯',
  '😭', '🥺', '😤', '🤗', '😱', '🤪', '😇', '🙄',
]);

const validateEmoji = (emoji: string): boolean => {
  if (!emoji || typeof emoji !== 'string') {
    return false;
  }

  // Check if emoji is in whitelist
  if (!ALLOWED_EMOJIS.has(emoji)) {
    console.log(`Invalid emoji attempted: ${emoji}`);
    return false;
  }

  return true;
};

// Enhanced statistics tracking
const updateReactionStatistics = async (postId: string, emoji: string, userId: string, action: 'add' | 'remove') => {
  const timestamp = new Date().toISOString();
  const statKey = `${postId}#${emoji}#${action}`;

  try {
    // Log reaction event for analytics
    const statsCommand = new PutCommand({
      TableName: process.env.REACTION_STATS_TABLE || POST_REACTIONS_TABLE + '_stats',
      Item: {
        statKey,
        postId,
        emoji,
        userId,
        action,
        timestamp,
        date: timestamp.split('T')[0], // For daily aggregation
        hour: new Date().getHours(), // For hourly trends
      },
    });

    await dynamodb.send(statsCommand);
    console.log(`Logged reaction stat: ${action} ${emoji} on post ${postId} by user ${userId}`);
  } catch (error) {
    console.error('Failed to log reaction statistics:', error);
    // Don't fail the main operation if stats logging fails
  }
};

export const reactToPost = async (event: APIGatewayProxyEvent): Promise<APIGatewayProxyResult> => {
  try {
    const { id } = event.pathParameters || {};
    if (!id) return createResponse(400, { error: 'Post ID is required' });
    const userId = getUserIdFromContext(event);
    if (!userId) return createResponse(401, { error: 'User not authenticated' });
    if (!event.body) return createResponse(400, { error: 'Request body is required' });
    const { emoji } = JSON.parse(event.body || '{}');
    if (!emoji || typeof emoji !== 'string') return createResponse(400, { error: 'emoji is required' });

    // Validate emoji against whitelist
    if (!validateEmoji(emoji)) {
      return createResponse(400, {
        error: 'Invalid emoji',
        message: 'Only whitelisted emojis are allowed',
        allowedEmojis: Array.from(ALLOWED_EMOJIS).slice(0, 10) // Show first 10 as examples
      });
    }

    // ENFORCE SINGLE REACTION RULE: First, remove any existing reactions by this user for this post
    console.log(`Enforcing single reaction rule for user ${userId} on post ${id}`);

    // Query all reactions by this user for this post
    const queryExisting = new QueryCommand({
      TableName: POST_REACTIONS_TABLE,
      KeyConditionExpression: 'postId = :postId',
      FilterExpression: 'userId = :userId',
      ExpressionAttributeValues: {
        ':postId': id,
        ':userId': userId,
      },
    });

    const existingReactions = await dynamodb.send(queryExisting);
    console.log(`Found ${existingReactions.Items?.length || 0} existing reactions by user ${userId} for post ${id}`);

    // Delete all existing reactions by this user
    if (existingReactions.Items && existingReactions.Items.length > 0) {
      for (const reaction of existingReactions.Items) {
        const deleteCommand = new DeleteCommand({
          TableName: POST_REACTIONS_TABLE,
          Key: {
            postId: id,
            reactionKey: reaction.reactionKey,
          },
        });
        await dynamodb.send(deleteCommand);
        console.log(`Deleted existing reaction: ${reaction.emoji} by user ${userId}`);

        // Track removal statistics
        await updateReactionStatistics(id, reaction.emoji, userId, 'remove');
      }
    }

    // Now add the new reaction
    const reactionKey = `${emoji}#${userId}`;
    const put = new PutCommand({
      TableName: POST_REACTIONS_TABLE,
      Item: {
        postId: id,
        reactionKey,
        emoji,
        userId,
        createdAt: new Date().toISOString(),
      },
    });
    await dynamodb.send(put);
    console.log(`Added new reaction: ${emoji} by user ${userId} for post ${id}`);

    // Track addition statistics
    await updateReactionStatistics(id, emoji, userId, 'add');

    return createResponse(200, {
      message: 'Reaction added',
      emoji,
      timestamp: new Date().toISOString()
    });
  } catch (error) {
    console.error('ReactToPost error:', error);
    return createResponse(500, { error: 'Failed to react to post', details: (error as Error).message });
  }
};

export const unreactToPost = async (event: APIGatewayProxyEvent): Promise<APIGatewayProxyResult> => {
  try {
    const { id } = event.pathParameters || {};
    if (!id) return createResponse(400, { error: 'Post ID is required' });
    const userId = getUserIdFromContext(event);
    if (!userId) return createResponse(401, { error: 'User not authenticated' });
    const emoji = (event.queryStringParameters || {}).emoji;
    if (!emoji) return createResponse(400, { error: 'emoji query param is required' });

    // Validate emoji against whitelist
    if (!validateEmoji(emoji)) {
      return createResponse(400, {
        error: 'Invalid emoji',
        message: 'Only whitelisted emojis are allowed'
      });
    }

    const reactionKey = `${emoji}#${userId}`;
    const del = new DeleteCommand({
      TableName: POST_REACTIONS_TABLE,
      Key: { postId: id, reactionKey },
    });
    await dynamodb.send(del);

    // Track removal statistics
    await updateReactionStatistics(id, emoji, userId, 'remove');

    return createResponse(200, {
      message: 'Reaction removed',
      emoji,
      timestamp: new Date().toISOString()
    });
  } catch (error) {
    console.error('UnreactToPost error:', error);
    return createResponse(500, { error: 'Failed to remove reaction', details: (error as Error).message });
  }
};

// Get reaction statistics for analytics
export const getReactionStatistics = async (event: APIGatewayProxyEvent): Promise<APIGatewayProxyResult> => {
  try {
    const { id } = event.pathParameters || {};
    if (!id) return createResponse(400, { error: 'Post ID is required' });

    const { timeframe = '24h', groupBy = 'emoji' } = event.queryStringParameters || {};

    // Calculate time range
    const now = new Date();
    let startTime: Date;

    switch (timeframe) {
      case '1h':
        startTime = new Date(now.getTime() - 60 * 60 * 1000);
        break;
      case '24h':
        startTime = new Date(now.getTime() - 24 * 60 * 60 * 1000);
        break;
      case '7d':
        startTime = new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000);
        break;
      case '30d':
        startTime = new Date(now.getTime() - 30 * 24 * 60 * 60 * 1000);
        break;
      default:
        startTime = new Date(now.getTime() - 24 * 60 * 60 * 1000);
    }

    // Query reaction statistics
    const statsQuery = new QueryCommand({
      TableName: process.env.REACTION_STATS_TABLE || POST_REACTIONS_TABLE + '_stats',
      KeyConditionExpression: 'postId = :postId',
      FilterExpression: '#timestamp >= :startTime',
      ExpressionAttributeNames: {
        '#timestamp': 'timestamp',
      },
      ExpressionAttributeValues: {
        ':postId': id,
        ':startTime': startTime.toISOString(),
      },
    });

    const statsResult = await dynamodb.send(statsQuery);
    const stats = statsResult.Items || [];

    // Aggregate statistics
    const aggregated: any = {
      totalReactions: stats.length,
      timeframe,
      startTime: startTime.toISOString(),
      endTime: now.toISOString(),
    };

    if (groupBy === 'emoji') {
      const emojiStats: Record<string, { adds: number; removes: number; net: number }> = {};

      stats.forEach((stat: any) => {
        const emoji = stat.emoji;
        if (!emojiStats[emoji]) {
          emojiStats[emoji] = { adds: 0, removes: 0, net: 0 };
        }

        if (stat.action === 'add') {
          emojiStats[emoji].adds++;
          emojiStats[emoji].net++;
        } else if (stat.action === 'remove') {
          emojiStats[emoji].removes++;
          emojiStats[emoji].net--;
        }
      });

      aggregated.byEmoji = emojiStats;
    } else if (groupBy === 'hour') {
      const hourlyStats: Record<string, number> = {};

      stats.forEach((stat: any) => {
        const hour = new Date(stat.timestamp).getHours();
        const hourKey = `${hour}:00`;
        hourlyStats[hourKey] = (hourlyStats[hourKey] || 0) + 1;
      });

      aggregated.byHour = hourlyStats;
    } else if (groupBy === 'user') {
      const userStats: Record<string, { adds: number; removes: number }> = {};

      stats.forEach((stat: any) => {
        const userId = stat.userId;
        if (!userStats[userId]) {
          userStats[userId] = { adds: 0, removes: 0 };
        }

        if (stat.action === 'add') {
          userStats[userId].adds++;
        } else if (stat.action === 'remove') {
          userStats[userId].removes++;
        }
      });

      aggregated.byUser = userStats;
    }

    return createResponse(200, {
      postId: id,
      statistics: aggregated,
      rawDataCount: stats.length,
    });

  } catch (error) {
    console.error('GetReactionStatistics error:', error);
    return createResponse(500, { error: 'Failed to get reaction statistics', details: (error as Error).message });
  }
};

