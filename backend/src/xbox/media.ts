import { APIGatewayProxyEvent, APIGatewayProxyResult } from 'aws-lambda';
import { DynamoDBClient } from '@aws-sdk/client-dynamodb';
import { DynamoDBDocumentClient, GetCommand, QueryCommand, UpdateCommand } from '@aws-sdk/lib-dynamodb';
import { SecretsManagerClient, GetSecretValueCommand } from '@aws-sdk/client-secrets-manager';
const fetch = require('node-fetch');

// Import refresh function from main Xbox module
// Note: We'll need to export this function from the main Xbox module
import { refreshXboxTokens } from './index';

// Helper function to create response
const createResponse = (statusCode: number, body: any): APIGatewayProxyResult => ({
    statusCode,
    headers: {
        'Content-Type': 'application/json',
        'Access-Control-Allow-Origin': '*',
        'Access-Control-Allow-Headers': 'Content-Type,X-Amz-Date,Authorization,X-Api-Key,X-Amz-Security-Token',
        'Access-Control-Allow-Methods': 'GET,POST,PUT,DELETE,OPTIONS'
    },
    body: JSON.stringify(body)
});

// Helper function to get user ID from authorizer context
const getUserIdFromContext = (event: APIGatewayProxyEvent): string | null => {
    // When using Lambda authorizer, user info is available in the context
    if (event.requestContext && event.requestContext.authorizer) {
        return (event.requestContext.authorizer as any).userId;
    }
    return null;
};

// AWS Configuration
const awsConfig = {
    region: process.env.AWS_REGION || 'us-west-2'
};

const dynamodbClient = new DynamoDBClient(awsConfig);
const dynamodb = DynamoDBDocumentClient.from(dynamodbClient);
const secretsManager = new SecretsManagerClient(awsConfig);

const USERS_TABLE = process.env.USERS_TABLE;
const XBOX_ACCOUNTS_TABLE = process.env.XBOX_ACCOUNTS_TABLE;
const APP_CONFIG_SECRET_NAME = process.env.APP_CONFIG_SECRET_NAME;
const XBOX_CONFIG_SECRET_NAME = process.env.XBOX_CONFIG_SECRET_NAME;

// Xbox Live API endpoints - Official Xbox Web API endpoints
const XBOX_SCREENSHOTS_BASE_URL = 'https://screenshotsmetadata.xboxlive.com';
const XBOX_GAMECLIPS_BASE_URL = 'https://gameclipsmetadata.xboxlive.com';

// TypeScript interfaces
interface XboxMediaItem {
    id: string;
    type: 'screenshot' | 'gameclip';
    title: string;
    thumbnailUrl: string;
    downloadUrl: string;
    gameTitle: string;
    dateTaken: string;
    fileSize: number;
    duration?: number; // For game clips
    resolutionWidth: number;
    resolutionHeight: number;
    platform: string;
    titleId: string;
}

interface XboxApiResponse {
    screenshots?: XboxMediaItem[];
    gameClips?: XboxMediaItem[];
    pagination?: {
        hasMore: boolean;
        contToken?: string;
    };
}

// Interface for Xbox account data
interface XboxAccount {
    id: string;
    userId: string;
    xboxUserId: string;
    gamertag: string;
    xstsToken: string;
    userHash: string;
    microsoftRefreshToken?: string; // Microsoft OAuth refresh token for automatic renewal
    profilePictureUrl?: string;
    isActive: boolean;
    createdAt: string;
    updatedAt: string;
    tokenExpiresAt: string;
}

// Get Xbox screenshots for authenticated user
const getXboxScreenshots = async (event: APIGatewayProxyEvent): Promise<APIGatewayProxyResult> => {
    try {
        const userId = getUserIdFromContext(event);
        if (!userId) {
            return createResponse(401, { error: 'User not authenticated' });
        }

        // Get user's Xbox account info
        const xboxAccount = await getUserXboxAccount(userId);
        if (!xboxAccount) {
            return createResponse(404, { error: 'No Xbox account linked' });
        }

        // Check if token is still valid, refresh if needed
        const tokenExpiry = new Date(xboxAccount.tokenExpiresAt);
        const now = new Date();
        let currentXboxAccount = xboxAccount;

        if (tokenExpiry <= now) {
            try {
                // Try to refresh the tokens automatically
                await refreshXboxTokens(xboxAccount);

                // Get the updated Xbox account record
                const updatedAccountResult = await dynamodb.send(new GetCommand({
                    TableName: XBOX_ACCOUNTS_TABLE,
                    Key: { id: xboxAccount.id }
                }));

                if (updatedAccountResult.Item) {
                    currentXboxAccount = updatedAccountResult.Item as XboxAccount;
                } else {
                    return createResponse(401, { error: 'Xbox token expired and refresh failed. Please re-link your Xbox account.' });
                }
            } catch (error) {
                console.error('Failed to refresh Xbox tokens:', error);
                return createResponse(401, { error: 'Xbox token expired and refresh failed. Please re-link your Xbox account.' });
            }
        }

        // Parse query parameters
        const queryParams = event.queryStringParameters || {};
        const numItems = parseInt(queryParams.numItems || '25');
        const contToken = queryParams.contToken;

        // Fetch screenshots from Xbox Live API using current (possibly refreshed) tokens
        const screenshots = await fetchXboxScreenshots(
            currentXboxAccount.xstsToken,
            currentXboxAccount.userHash,
            { numItems, contToken }
        );

        return createResponse(200, {
            screenshots: screenshots.items,
            pagination: {
                hasMore: screenshots.hasMore,
                contToken: screenshots.contToken
            }
        });

    } catch (error) {
        console.error('GetXboxScreenshots error:', error);
        return createResponse(500, {
            error: 'Failed to fetch Xbox screenshots',
            details: (error as Error).message
        });
    }
};

// Get Xbox game clips for authenticated user
const getXboxGameClips = async (event: APIGatewayProxyEvent): Promise<APIGatewayProxyResult> => {
    try {
        const userId = getUserIdFromContext(event);
        if (!userId) {
            return createResponse(401, { error: 'User not authenticated' });
        }

        // Get user's Xbox account info
        const xboxAccount = await getUserXboxAccount(userId);
        if (!xboxAccount) {
            return createResponse(404, { error: 'No Xbox account linked' });
        }

        // Check if token is still valid, refresh if needed
        const tokenExpiry = new Date(xboxAccount.tokenExpiresAt);
        const now = new Date();
        let currentXboxAccount = xboxAccount;

        if (tokenExpiry <= now) {
            try {
                // Try to refresh the tokens automatically
                await refreshXboxTokens(xboxAccount);

                // Get the updated Xbox account record
                const updatedAccountResult = await dynamodb.send(new GetCommand({
                    TableName: XBOX_ACCOUNTS_TABLE,
                    Key: { id: xboxAccount.id }
                }));

                if (updatedAccountResult.Item) {
                    currentXboxAccount = updatedAccountResult.Item as XboxAccount;
                } else {
                    return createResponse(401, { error: 'Xbox token expired and refresh failed. Please re-link your Xbox account.' });
                }
            } catch (error) {
                console.error('Failed to refresh Xbox tokens:', error);
                return createResponse(401, { error: 'Xbox token expired and refresh failed. Please re-link your Xbox account.' });
            }
        }

        // Parse query parameters
        const queryParams = event.queryStringParameters || {};
        const numItems = parseInt(queryParams.numItems || '25');
        const contToken = queryParams.contToken;

        // Fetch game clips from Xbox Live API using current (possibly refreshed) tokens
        const gameClips = await fetchXboxGameClips(
            currentXboxAccount.xstsToken,
            currentXboxAccount.userHash,
            { numItems, contToken }
        );

        return createResponse(200, {
            gameClips: gameClips.items,
            pagination: {
                hasMore: gameClips.hasMore,
                contToken: gameClips.contToken
            }
        });

    } catch (error) {
        console.error('GetXboxGameClips error:', error);
        return createResponse(500, {
            error: 'Failed to fetch Xbox game clips',
            details: (error as Error).message
        });
    }
};

// Helper function to get user's Xbox account
const getUserXboxAccount = async (userId: string): Promise<XboxAccount | null> => {
    try {
        const queryParams = {
            TableName: XBOX_ACCOUNTS_TABLE,
            IndexName: 'UserIdIndex', // GSI on userId
            KeyConditionExpression: 'userId = :userId',
            FilterExpression: 'isActive = :isActive',
            ExpressionAttributeValues: {
                ':userId': userId,
                ':isActive': true
            }
        };

        const result = await dynamodb.send(new QueryCommand(queryParams));

        if (!result.Items || result.Items.length === 0) {
            return null;
        }

        return result.Items[0] as XboxAccount;
    } catch (error) {
        console.error('Error getting user Xbox account:', error);
        return null;
    }
};

// Helper function to fetch Xbox screenshots from Xbox Live API
const fetchXboxScreenshots = async (
    xstsToken: string,
    userHash: string,
    options: { numItems: number; contToken?: string }
): Promise<{ items: XboxMediaItem[]; hasMore: boolean; contToken?: string }> => {
    try {
        // Use the correct Xbox Web API endpoint for own screenshots
        const params = new URLSearchParams({
            skipItems: '0',
            maxItems: options.numItems.toString()
        });

        if (options.contToken) {
            params.append('continuationToken', options.contToken);
        }

        // Construct the correct endpoint URL - just /users/me/screenshots (no /recent)
        const requestUrl = `${XBOX_SCREENSHOTS_BASE_URL}/users/me/screenshots?${params}`;
        console.log('Fetching Xbox screenshots from:', requestUrl);
        console.log('Using authorization header with userHash:', userHash.substring(0, 8) + '...');

        // Generate a correlation vector for the request (simplified version)
        const correlationVector = `${Date.now()}.${Math.floor(Math.random() * 1000000)}`;

        const response = await fetch(requestUrl, {
            headers: {
                'Authorization': `XBL3.0 x=${userHash};${xstsToken}`,
                'x-xbl-contract-version': '5',
                'MS-CV': correlationVector,
                'Accept': 'application/json'
            }
        });

        console.log('Xbox screenshots API response status:', response.status);

        if (!response.ok) {
            const errorText = await response.text();
            console.error('Xbox screenshots API error response:', errorText);
            throw new Error(`Xbox Live API request failed: ${response.status} ${response.statusText} - ${errorText}`);
        }

        const data = await response.json();

        // Transform Xbox Live API response to our format
        // The actual response structure from Xbox API is different than expected
        const items: XboxMediaItem[] = (data.screenshots || []).map((item: any) => ({
            id: item.id || item.screenshotId || '',
            type: 'screenshot' as const,
            title: item.title || item.titleName || 'Screenshot',
            thumbnailUrl: item.thumbnailUrl || item.thumbnails?.[0]?.uri || '',
            downloadUrl: item.downloadUrl || item.screenshotUris?.[0]?.uri || '',
            gameTitle: item.gameTitle || item.titleName || 'Unknown Game',
            dateTaken: item.dateTaken || item.datePublished || '',
            fileSize: item.fileSize || item.screenshotUris?.[0]?.fileSize || 0,
            resolutionWidth: item.resolutionWidth || item.screenshotUris?.[0]?.width || 1920,
            resolutionHeight: item.resolutionHeight || item.screenshotUris?.[0]?.height || 1080,
            platform: item.platform || 'Xbox',
            titleId: String(item.titleId || '') // Convert to string to match Flutter model
        }));

        return {
            items,
            hasMore: !!data.pagingInfo?.continuationToken,
            contToken: data.pagingInfo?.continuationToken
        };
    } catch (error) {
        console.error('Error fetching Xbox screenshots:', error);
        throw error;
    }
};

// Helper function to fetch Xbox game clips from Xbox Live API
const fetchXboxGameClips = async (
    xstsToken: string,
    userHash: string,
    options: { numItems: number; contToken?: string }
): Promise<{ items: XboxMediaItem[]; hasMore: boolean; contToken?: string }> => {
    try {
        // Use the correct Xbox Web API endpoint for own clips
        const params = new URLSearchParams({
            skipItems: '0',
            maxItems: options.numItems.toString()
        });

        if (options.contToken) {
            params.append('continuationToken', options.contToken);
        }

        // Construct the correct endpoint URL - just /users/me/clips (no /recent)
        const requestUrl = `${XBOX_GAMECLIPS_BASE_URL}/users/me/clips?${params}`;
        console.log('Fetching Xbox gameclips from:', requestUrl);

        // Generate a correlation vector for the request (simplified version)
        const correlationVector = `${Date.now()}.${Math.floor(Math.random() * 1000000)}`;

        const response = await fetch(requestUrl, {
            headers: {
                'Authorization': `XBL3.0 x=${userHash};${xstsToken}`,
                'x-xbl-contract-version': '1',
                'MS-CV': correlationVector,
                'Accept': 'application/json'
            }
        });

        console.log('Xbox gameclips API response status:', response.status);

        if (!response.ok) {
            const errorText = await response.text();
            console.error('Xbox gameclips API error response:', errorText);
            throw new Error(`Xbox Live API request failed: ${response.status} ${response.statusText} - ${errorText}`);
        }

        const data = await response.json();

        // Transform Xbox Live API response to our format
        const items: XboxMediaItem[] = (data.gameClips || []).map((item: any) => ({
            id: item.gameClipId,
            type: 'gameclip' as const,
            title: item.titleName || 'Game Clip',
            thumbnailUrl: item.thumbnails?.[0]?.uri || '',
            downloadUrl: item.gameClipUris?.[0]?.uri || '',
            gameTitle: item.titleName || 'Unknown Game',
            dateTaken: item.dateRecorded,
            fileSize: item.gameClipUris?.[0]?.fileSize || 0,
            duration: item.durationInSeconds,
            resolutionWidth: item.gameClipUris?.[0]?.width || 1920,
            resolutionHeight: item.gameClipUris?.[0]?.height || 1080,
            platform: 'Xbox',
            titleId: item.titleId || ''
        }));

        return {
            items,
            hasMore: !!data.pagingInfo?.continuationToken,
            contToken: data.pagingInfo?.continuationToken
        };
    } catch (error) {
        console.error('Error fetching Xbox game clips:', error);
        throw error;
    }
};

// Main handler for Xbox media endpoints
export const handler = async (event: APIGatewayProxyEvent): Promise<APIGatewayProxyResult> => {
    console.log('Xbox media handler received event:', JSON.stringify(event, null, 2));

    const httpMethod = event.httpMethod;
    const resource = event.resource;

    try {
        if (httpMethod === 'GET' && resource === '/xbox/screenshots') {
            return await getXboxScreenshots(event);
        } else if (httpMethod === 'GET' && resource === '/xbox/gameclips') {
            return await getXboxGameClips(event);
        } else {
            return createResponse(404, { error: 'Endpoint not found' });
        }
    } catch (error) {
        console.error('Xbox media handler error:', error);
        return createResponse(500, {
            error: 'Internal server error',
            details: (error as Error).message
        });
    }
};
