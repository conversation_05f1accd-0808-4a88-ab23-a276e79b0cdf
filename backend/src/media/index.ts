import { APIGatewayProxyEvent, APIGatewayProxyResult, Context } from 'aws-lambda';
import { DynamoDBClient } from '@aws-sdk/client-dynamodb';
import { DynamoDBDocumentClient, GetCommand, PutCommand, DeleteCommand, UpdateCommand } from '@aws-sdk/lib-dynamodb';
import { S3Client, PutObjectCommand, DeleteObjectCommand, GetObjectCommand, CopyObjectCommand } from '@aws-sdk/client-s3';
import { getSignedUrl } from '@aws-sdk/s3-request-presigner';
import { v4 as uuidv4 } from 'uuid';
import { CognitoJwtVerifier } from 'aws-jwt-verify';

// Configure AWS SDK
const awsConfig = {
    region: process.env.AWS_REGION || 'us-west-2'
};

const dynamodbClient = new DynamoDBClient(awsConfig);
const dynamodb = DynamoDBDocumentClient.from(dynamodbClient);
const s3Client = new S3Client(awsConfig);

// Environment variables
const MEDIA_TABLE = process.env.MEDIA_TABLE!;
const USERS_TABLE = process.env.USERS_TABLE!;
const MEDIA_BUCKET = process.env.MEDIA_BUCKET || 'gameflex-media-development';
const CLOUDFRONT_DOMAIN = process.env.CLOUDFRONT_DOMAIN || '';
const POSTS_TABLE = process.env.POSTS_TABLE || 'gameflex-development-Posts';
const COGNITO_USER_POOL_ID = process.env.USER_POOL_ID!;
const COGNITO_CLIENT_ID = process.env.USER_POOL_CLIENT_ID!;

// Upload limits constants
const UPLOAD_LIMITS = {
    MAX_IMAGES_IN_REVIEW: 1,
    MAX_UPLOADS_PER_HOUR: 20,
    MAX_REJECTED_IMAGES_BEFORE_SUSPENSION: 10,
    MAX_OPEN_UPLOAD_LINKS: 3,
    DEMERIT_EXPIRY_DAYS: 30
};

// JWT Verifier
const verifier = CognitoJwtVerifier.create({
    userPoolId: COGNITO_USER_POOL_ID,
    tokenUse: 'access',
    clientId: COGNITO_CLIENT_ID,
});

// TypeScript interfaces
interface MediaRecord {
    id: string;
    fileName: string;
    fileType: string;
    fileSize: number;
    mediaType: string;
    userId: string;
    s3Key: string; // Current S3 key (staging initially, final after approval)
    bucketName: string;
    url: string; // Public URL (empty until approved)
    status: 'pending_upload' | 'uploaded' | 'processing' | 'approved' | 'failed' | 'rejected_inappropriate';
    postId?: string; // Optional post association
    createdAt: string;
    updatedAt: string;
    // Staging and approval workflow fields
    staging_s3_key?: string; // S3 key in staging area
    final_s3_key?: string; // Final S3 key after approval
    final_url?: string; // Final public URL after approval
    // Review workflow fields (for rejected content)
    review_s3_key?: string; // S3 key in review area for internal review
    review_type?: 'inappropriate' | 'failed'; // Type of review needed
    review_date?: string; // Date content was moved to review
    // AI processing results (populated by AI Lambda)
    moderation_analysis?: {
        isInappropriate: boolean;
        confidence: number;
        moderationLabels: Array<{
            name: string;
            confidence: number;
            parentName?: string;
            categories?: string[];
        }>;
        summary: string;
    };
    content_analysis?: {
        labels: Array<{
            name: string;
            confidence: number;
            categories?: string[];
            instances?: Array<{
                boundingBox?: {
                    width: number;
                    height: number;
                    left: number;
                    top: number;
                };
                confidence: number;
            }>;
            parents?: Array<{
                name: string;
            }>;
        }>;
        videoGameRelated: boolean;
        videoGameConfidence: number;
        suggestedTags: string[];
        dominantColors?: Array<{
            red: number;
            green: number;
            blue: number;
            cssColor: string;
            simplifiedColor: string;
            pixelPercent: number;
        }>;
        imageProperties?: any;
    };
    processing_completed_at?: string;
    processing_time_ms?: number;
    rejection_reason?: string;
    error_message?: string;
    // Admin approval fields (for overriding AI decisions)
    approval_reason?: string; // Reason for manual approval
    approved_by?: string; // Admin user ID who approved
    approved_at?: string; // Timestamp of manual approval
}

interface UploadRequest {
    fileName: string;
    fileType: string;
    fileSize?: number;
    mediaType?: string;
    postId?: string; // Optional post ID to associate media with
}

interface UpdateStatusRequest {
    status: 'pending_upload' | 'uploaded' | 'processing' | 'approved' | 'failed' | 'rejected_nsfw';
}

interface ProcessMediaRequest {
    mediaId: string;
}

interface UserContext {
    userId: string;
}

interface UserDemerit {
    reason: string;
    timestamp: string;
    mediaId?: string;
}

interface UploadLimitsCheck {
    canUpload: boolean;
    reason?: string;
    imagesInReview?: number;
    uploadsInLastHour?: number;
    activeDemerits?: number;
    openUploadLinks?: number;
}

// Helper function to create response
const createResponse = (statusCode: number, body: any): APIGatewayProxyResult => ({
    statusCode,
    headers: {
        'Content-Type': 'application/json',
        'Access-Control-Allow-Origin': '*',
        'Access-Control-Allow-Headers': 'Content-Type,X-Amz-Date,Authorization,X-Api-Key,X-Amz-Security-Token',
        'Access-Control-Allow-Methods': 'GET,POST,PUT,DELETE,OPTIONS'
    },
    body: JSON.stringify(body)
});

// Check upload limits for a user
const checkUploadLimits = async (userId: string): Promise<UploadLimitsCheck> => {
    try {
        // Get user data to check demerits and suspension status
        const userResponse = await dynamodb.send(new GetCommand({
            TableName: USERS_TABLE,
            Key: { id: userId }
        }));

        const user = userResponse.Item;
        // If user doesn't exist yet (new user), allow upload with no restrictions
        if (!user) {
            return {
                canUpload: true,
                activeDemerits: 0,
                imagesInReview: 0,
                uploadsInLastHour: 0,
                openUploadLinks: 0
            };
        }

        // Check if user is suspended
        if (user.upload_suspended) {
            return { canUpload: false, reason: 'Upload privileges suspended pending review' };
        }

        // Count active demerits (within last 30 days)
        const thirtyDaysAgo = new Date();
        thirtyDaysAgo.setDate(thirtyDaysAgo.getDate() - UPLOAD_LIMITS.DEMERIT_EXPIRY_DAYS);

        const demerits = user.demerits || [];
        const activeDemerits = demerits.filter((demerit: any) =>
            new Date(demerit.timestamp) > thirtyDaysAgo
        ).length;

        // Check if user has too many active demerits
        if (activeDemerits >= UPLOAD_LIMITS.MAX_REJECTED_IMAGES_BEFORE_SUSPENSION) {
            return {
                canUpload: false,
                reason: `Too many rejected images (${activeDemerits}/${UPLOAD_LIMITS.MAX_REJECTED_IMAGES_BEFORE_SUSPENSION}). Upload privileges suspended.`,
                activeDemerits
            };
        }

        // Check recent uploads and images in review
        // Note: This is a simplified implementation. In production, you'd use GSI queries
        // For now, we'll do basic checks and return success
        const oneHourAgo = new Date();
        oneHourAgo.setHours(oneHourAgo.getHours() - 1);

        // TODO: Implement proper GSI queries for:
        // 1. Count images with status 'processing' or 'pending_upload' (images in review)
        // 2. Count uploads in last hour by user
        // 3. Count open upload links (status 'pending_upload')

        return {
            canUpload: true,
            activeDemerits,
            imagesInReview: 0, // TODO: Implement actual count
            uploadsInLastHour: 0, // TODO: Implement actual count
            openUploadLinks: 0 // TODO: Implement actual count
        };

    } catch (error) {
        console.error('Error checking upload limits:', error);
        return { canUpload: false, reason: 'Error checking upload limits' };
    }
};

// Add demerit to user record
const addDemeritToUser = async (userId: string, reason: string, mediaId?: string): Promise<void> => {
    try {
        const demerit = {
            reason,
            timestamp: new Date().toISOString(),
            ...(mediaId && { mediaId })
        };

        await dynamodb.send(new UpdateCommand({
            TableName: USERS_TABLE,
            Key: { id: userId },
            UpdateExpression: 'SET demerits = list_append(if_not_exists(demerits, :empty_list), :demerit), updated_at = :updated_at',
            ExpressionAttributeValues: {
                ':demerit': [demerit],
                ':empty_list': [],
                ':updated_at': new Date().toISOString()
            }
        }));

        console.log(`Added demerit to user ${userId}: ${reason}`);
    } catch (error) {
        console.error('Error adding demerit to user:', error);
    }
};

// Helper function to get user ID from authorizer context or JWT token
const getUserIdFromContext = async (event: APIGatewayProxyEvent): Promise<string | null> => {
    // First try to get from Lambda authorizer context
    if (event.requestContext && event.requestContext.authorizer) {
        return (event.requestContext.authorizer as any).userId;
    }

    // Fallback to JWT validation if authorizer is not available
    const authHeader = event.headers.Authorization || event.headers.authorization;
    if (!authHeader || !authHeader.startsWith('Bearer ')) {
        return null;
    }

    const token = authHeader.substring(7); // Remove 'Bearer ' prefix

    try {
        const payload = await verifier.verify(token);
        return payload.sub; // 'sub' is the user ID in Cognito JWT
    } catch (error) {
        console.error('JWT verification failed:', error);
        return null;
    }
};

// Helper function to generate S3 key for uploaded file (staging area)
const generateStagingS3Key = (userId: string, mediaId: string, fileName: string, mediaType: string = 'media'): string => {
    const extension = fileName.split('.').pop()?.toLowerCase() || 'jpg';

    // All uploads go to staging first for AI processing
    switch (mediaType) {
        case 'avatar':
            return `staging/avatars/${userId}/${mediaId}.${extension}`;
        case 'reflex':
            return `staging/reflexes/${userId}/${mediaId}.${extension}`;
        case 'video':
            return `staging/videos/${userId}/${mediaId}.${extension}`;
        default:
            return `staging/media/${userId}/${mediaId}.${extension}`;
    }
};

// Helper function to generate final S3 key for approved content
const generateFinalS3Key = (userId: string, mediaId: string, fileName: string, mediaType: string = 'media'): string => {
    const extension = fileName.split('.').pop()?.toLowerCase() || 'jpg';

    switch (mediaType) {
        case 'avatar':
            return `avatars/${userId}/${mediaId}.${extension}`;
        case 'reflex':
            return `reflexes/${userId}/${mediaId}.${extension}`;
        case 'video':
            return `videos/${userId}/${mediaId}.${extension}`;
        default:
            return `media/${userId}/${mediaId}.${extension}`;
    }
};

// Helper function to get CloudFront URL
const getCloudFrontUrl = (s3Key: string): string => {
    if (CLOUDFRONT_DOMAIN) {
        return `https://${CLOUDFRONT_DOMAIN}/${s3Key}`;
    }
    // Fallback to S3 URL if CloudFront not configured
    return `https://${MEDIA_BUCKET}.s3.${awsConfig.region}.amazonaws.com/${s3Key}`;
};

// Upload media using AWS S3 and AI processing
const uploadMedia = async (event: APIGatewayProxyEvent): Promise<APIGatewayProxyResult> => {
    try {
        console.log('UploadMedia: Received event body:', event.body);

        if (!event.body) {
            return createResponse(400, { error: 'Request body is required' });
        }

        let parsedBody: UploadRequest;
        try {
            parsedBody = JSON.parse(event.body);
            console.log('UploadMedia: Parsed body:', parsedBody);
        } catch (parseError) {
            console.error('UploadMedia: JSON parse error:', parseError);
            return createResponse(400, { error: 'Invalid JSON in request body' });
        }

        const { fileName, fileType, fileSize, mediaType, postId } = parsedBody;

        // Get user ID from authorizer context
        const userId = await getUserIdFromContext(event);

        if (!fileName || !fileType) {
            return createResponse(400, { error: 'fileName and fileType are required' });
        }

        if (!userId) {
            return createResponse(401, { error: 'User not authenticated' });
        }

        // Check upload limits
        const limitsCheck = await checkUploadLimits(userId);
        if (!limitsCheck.canUpload) {
            return createResponse(429, {
                error: 'Upload limit exceeded',
                reason: limitsCheck.reason,
                details: {
                    activeDemerits: limitsCheck.activeDemerits,
                    imagesInReview: limitsCheck.imagesInReview,
                    uploadsInLastHour: limitsCheck.uploadsInLastHour,
                    openUploadLinks: limitsCheck.openUploadLinks
                }
            });
        }

        // Validate file type
        const allowedImageTypes = ['image/jpeg', 'image/jpg', 'image/png', 'image/gif', 'image/webp'];
        const allowedVideoTypes = ['video/mp4', 'video/mov', 'video/avi', 'video/quicktime', 'video/x-msvideo'];
        const allowedTypes = [...allowedImageTypes, ...allowedVideoTypes];

        if (!allowedTypes.includes(fileType.toLowerCase())) {
            return createResponse(400, {
                error: 'Unsupported file type',
                details: `Supported types: ${allowedTypes.join(', ')}`
            });
        }

        // Determine media type based on file type if not provided
        let finalMediaType = mediaType;
        if (!finalMediaType || finalMediaType === 'image') {
            if (allowedVideoTypes.includes(fileType.toLowerCase())) {
                finalMediaType = 'video';
            } else {
                finalMediaType = 'image';
            }
        }

        const mediaId = uuidv4();

        // Generate S3 key for staging area (private until approved)
        const stagingS3Key = generateStagingS3Key(userId, mediaId, fileName, finalMediaType);

        // Generate presigned URL for S3 upload to staging area (valid for 10 minutes)
        const uploadCommand = new PutObjectCommand({
            Bucket: MEDIA_BUCKET,
            Key: stagingS3Key,
            ContentType: fileType,
            Metadata: {
                'media-id': mediaId,
                'user-id': userId,
                'original-filename': fileName,
                'media-type': finalMediaType,
                'upload-timestamp': new Date().toISOString(),
            },
        });

        const uploadUrl = await getSignedUrl(s3Client, uploadCommand, { expiresIn: 600 });

        // Generate final S3 key for when content is approved
        const finalS3Key = generateFinalS3Key(userId, mediaId, fileName, finalMediaType);

        // Public URL will be available only after approval
        const publicUrl = getCloudFrontUrl(finalS3Key);

        console.log('UploadMedia: Generated S3 upload URL for staging key:', stagingS3Key);

        // Create media record with staging information
        const mediaRecord: MediaRecord = {
            id: mediaId,
            fileName,
            fileType,
            fileSize: fileSize || 0,
            mediaType: finalMediaType,
            userId,
            s3Key: stagingS3Key, // Initially points to staging location
            bucketName: MEDIA_BUCKET,
            url: '', // No public URL until approved
            status: 'pending_upload',
            postId: postId || undefined, // Associate with post if provided
            createdAt: new Date().toISOString(),
            updatedAt: new Date().toISOString(),
            // Store both staging and final keys for processing
            staging_s3_key: stagingS3Key,
            final_s3_key: finalS3Key,
            final_url: publicUrl, // Store the final URL for when content is approved
        };

        const putCommand = new PutCommand({
            TableName: MEDIA_TABLE,
            Item: mediaRecord
        });

        await dynamodb.send(putCommand);

        return createResponse(200, {
            message: 'Upload URL generated successfully',
            mediaId,
            uploadUrl: uploadUrl,
            media: mediaRecord,
            instructions: {
                step1: 'Upload your file to the provided uploadUrl using PUT method',
                step2: 'File will be automatically processed with AI after upload via S3 event trigger',
                step3: 'Check media status for processing results'
            }
        });

    } catch (error) {
        console.error('UploadMedia error:', error);
        const errorMessage = error instanceof Error ? error.message : 'Unknown error';
        const errorStack = error instanceof Error ? error.stack : 'No stack trace';
        console.error('UploadMedia error stack:', errorStack);
        return createResponse(500, { error: 'Failed to generate upload URL', details: errorMessage });
    }
};

// Get media
const getMedia = async (event: APIGatewayProxyEvent): Promise<APIGatewayProxyResult> => {
    try {
        const { id } = event.pathParameters || {};

        if (!id) {
            return createResponse(400, { error: 'Media ID is required' });
        }

        const getCommand = new GetCommand({
            TableName: MEDIA_TABLE,
            Key: { id }
        });

        const result = await dynamodb.send(getCommand);

        if (!result.Item) {
            return createResponse(404, { error: 'Media not found' });
        }

        const media = result.Item as MediaRecord;

        // Use final_url as fallback if url is empty (for backward compatibility)
        if (!media.url && media.final_url) {
            media.url = media.final_url;
        }

        // For S3 with CloudFront, we use the public URL directly
        // CloudFront provides public access to approved media files only
        if (media.status === 'approved' && media.url) {
            (media as any).downloadUrl = media.url;
        } else if (media.status === 'processing' || media.status === 'uploaded') {
            // Content is still being processed - no public URL available yet
            (media as any).downloadUrl = null;
            (media as any).message = 'Content is being processed and will be available shortly';
        } else if (media.status === 'rejected_inappropriate') {
            // Content was rejected - no URL will ever be available
            (media as any).downloadUrl = null;
            (media as any).message = 'Content was rejected due to inappropriate content';
        } else if (media.status === 'pending_upload' && media.url) {
            // For backward compatibility - if we have a URL, use it
            (media as any).downloadUrl = media.url;
        }

        return createResponse(200, { media });

    } catch (error) {
        console.error('GetMedia error:', error);
        const errorMessage = error instanceof Error ? error.message : 'Unknown error';
        return createResponse(500, { error: 'Failed to get media', details: errorMessage });
    }
};

// Get media analysis results
const getMediaAnalysis = async (event: APIGatewayProxyEvent): Promise<APIGatewayProxyResult> => {
    try {
        const { id } = event.pathParameters || {};

        if (!id) {
            return createResponse(400, { error: 'Media ID is required' });
        }

        // Get user ID from authorizer context
        const userId = await getUserIdFromContext(event);
        if (!userId) {
            return createResponse(401, { error: 'User not authenticated' });
        }

        const getCommand = new GetCommand({
            TableName: MEDIA_TABLE,
            Key: { id }
        });

        const result = await dynamodb.send(getCommand);

        if (!result.Item) {
            return createResponse(404, { error: 'Media not found' });
        }

        const media = result.Item as MediaRecord;

        // Verify user owns this media
        if (media.userId !== userId) {
            return createResponse(403, { error: 'Not authorized to access this media analysis' });
        }

        // Return analysis results
        const analysis = {
            mediaId: media.id,
            status: media.status,
            moderationAnalysis: media.moderation_analysis,
            contentAnalysis: media.content_analysis,
            processingCompletedAt: media.processing_completed_at,
            processingTimeMs: media.processing_time_ms,
            rejectionReason: media.rejection_reason,
            errorMessage: media.error_message,
        };

        return createResponse(200, { analysis });

    } catch (error) {
        console.error('GetMediaAnalysis error:', error);
        const errorMessage = error instanceof Error ? error.message : 'Unknown error';
        return createResponse(500, { error: 'Failed to get media analysis', details: errorMessage });
    }
};

// Get media for internal review (admin/moderator access)
const getMediaForReview = async (event: APIGatewayProxyEvent): Promise<APIGatewayProxyResult> => {
    try {
        const { id } = event.pathParameters || {};

        if (!id) {
            return createResponse(400, { error: 'Media ID is required' });
        }

        // TODO: Add admin/moderator role verification here
        // For now, we'll check for a special header or admin user ID
        const isAdmin = event.headers['x-admin-access'] === 'true' ||
            event.requestContext?.authorizer?.claims?.['custom:role'] === 'admin';

        if (!isAdmin) {
            return createResponse(403, { error: 'Admin access required for review content' });
        }

        const getCommand = new GetCommand({
            TableName: MEDIA_TABLE,
            Key: { id }
        });

        const result = await dynamodb.send(getCommand);

        if (!result.Item) {
            return createResponse(404, { error: 'Media not found' });
        }

        const media = result.Item as MediaRecord;

        // Only return media that is in review status
        if (media.status !== 'rejected_inappropriate' && media.status !== 'failed') {
            return createResponse(400, { error: 'Media is not in review status' });
        }

        // Generate presigned URL for review content if it exists
        let reviewUrl = null;
        if (media.review_s3_key) {
            const getObjectCommand = new GetObjectCommand({
                Bucket: MEDIA_BUCKET,
                Key: media.review_s3_key,
            });
            reviewUrl = await getSignedUrl(s3Client, getObjectCommand, { expiresIn: 3600 }); // 1 hour
        }

        // Return comprehensive review information
        const reviewData = {
            mediaId: media.id,
            userId: media.userId,
            fileName: media.fileName,
            fileType: media.fileType,
            fileSize: media.fileSize,
            mediaType: media.mediaType,
            status: media.status,
            reviewType: media.review_type,
            reviewDate: media.review_date,
            reviewUrl, // Presigned URL to access the content
            rejectionReason: media.rejection_reason,
            moderationAnalysis: media.moderation_analysis,
            contentAnalysis: media.content_analysis,
            processingCompletedAt: media.processing_completed_at,
            processingTimeMs: media.processing_time_ms,
            createdAt: media.createdAt,
            updatedAt: media.updatedAt,
        };

        return createResponse(200, { reviewData });

    } catch (error) {
        console.error('GetMediaForReview error:', error);
        const errorMessage = error instanceof Error ? error.message : 'Unknown error';
        return createResponse(500, { error: 'Failed to get media for review', details: errorMessage });
    }
};

// Approve rejected content after internal review (admin only)
const approveRejectedMedia = async (event: APIGatewayProxyEvent): Promise<APIGatewayProxyResult> => {
    try {
        const { id } = event.pathParameters || {};
        const body = JSON.parse(event.body || '{}');
        const { approvalReason } = body;

        if (!id) {
            return createResponse(400, { error: 'Media ID is required' });
        }

        if (!approvalReason) {
            return createResponse(400, { error: 'Approval reason is required' });
        }

        // TODO: Add admin role verification
        const isAdmin = event.headers['x-admin-access'] === 'true' ||
            event.requestContext?.authorizer?.claims?.['custom:role'] === 'admin';

        if (!isAdmin) {
            return createResponse(403, { error: 'Admin access required to approve content' });
        }

        // Get media record
        const getCommand = new GetCommand({
            TableName: MEDIA_TABLE,
            Key: { id }
        });

        const result = await dynamodb.send(getCommand);
        if (!result.Item) {
            return createResponse(404, { error: 'Media not found' });
        }

        const media = result.Item as MediaRecord;

        // Verify media is in rejected status
        if (media.status !== 'rejected_inappropriate') {
            return createResponse(400, { error: 'Media is not in rejected status' });
        }

        if (!media.review_s3_key || !media.final_s3_key) {
            return createResponse(400, { error: 'Media missing review or final S3 keys' });
        }

        // Move content from review area to final public location
        const copyCommand = new CopyObjectCommand({
            Bucket: MEDIA_BUCKET,
            CopySource: `${MEDIA_BUCKET}/${media.review_s3_key}`,
            Key: media.final_s3_key,
            MetadataDirective: 'COPY',
        });

        await s3Client.send(copyCommand);

        // Delete from review area
        const deleteReviewCommand = new DeleteObjectCommand({
            Bucket: MEDIA_BUCKET,
            Key: media.review_s3_key,
        });

        await s3Client.send(deleteReviewCommand);

        // Update media record
        const updateCommand = new UpdateCommand({
            TableName: MEDIA_TABLE,
            Key: { id },
            UpdateExpression: `
                SET #status = :status,
                    s3Key = :finalKey,
                    #url = :finalUrl,
                    approval_reason = :approvalReason,
                    approved_by = :approvedBy,
                    approved_at = :approvedAt,
                    updated_at = :updatedAt
                REMOVE review_s3_key, review_type, review_date
            `,
            ExpressionAttributeNames: {
                '#status': 'status',
                '#url': 'url'
            },
            ExpressionAttributeValues: {
                ':status': 'approved',
                ':finalKey': media.final_s3_key,
                ':finalUrl': media.final_url || '',
                ':approvalReason': approvalReason,
                ':approvedBy': await getUserIdFromContext(event) || 'admin', // Get admin user ID
                ':approvedAt': new Date().toISOString(),
                ':updatedAt': new Date().toISOString(),
            },
        });

        await dynamodb.send(updateCommand);

        console.log(`Media ${id} approved by admin and moved to public area`);

        return createResponse(200, {
            message: 'Media approved and made public',
            mediaId: id,
            status: 'approved',
            publicUrl: media.final_url,
            approvalReason,
        });

    } catch (error) {
        console.error('ApproveRejectedMedia error:', error);
        const errorMessage = error instanceof Error ? error.message : 'Unknown error';
        return createResponse(500, { error: 'Failed to approve rejected media', details: errorMessage });
    }
};

// Delete media
const deleteMedia = async (event: APIGatewayProxyEvent): Promise<APIGatewayProxyResult> => {
    try {
        const { id } = event.pathParameters || {};

        if (!id) {
            return createResponse(400, { error: 'Media ID is required' });
        }

        // Get media record
        const getCommand = new GetCommand({
            TableName: MEDIA_TABLE,
            Key: { id }
        });

        const result = await dynamodb.send(getCommand);

        if (!result.Item) {
            return createResponse(404, { error: 'Media not found' });
        }

        const media = result.Item as MediaRecord;

        // Delete from S3 (handle staging, final, and review locations)
        if (media.bucketName) {
            const deletePromises: Promise<any>[] = [];

            // Delete from current location (staging or final)
            if (media.s3Key) {
                deletePromises.push(
                    s3Client.send(new DeleteObjectCommand({
                        Bucket: media.bucketName,
                        Key: media.s3Key
                    }))
                );
                console.log(`Deleting S3 object: ${media.s3Key} from bucket: ${media.bucketName}`);
            }

            // Also delete from staging if it exists and is different from current key
            if (media.staging_s3_key && media.staging_s3_key !== media.s3Key) {
                deletePromises.push(
                    s3Client.send(new DeleteObjectCommand({
                        Bucket: media.bucketName,
                        Key: media.staging_s3_key
                    })).catch(error => {
                        // Don't fail if staging file doesn't exist
                        console.log(`Staging file ${media.staging_s3_key} not found or already deleted:`, error.message);
                    })
                );
                console.log(`Deleting staging S3 object: ${media.staging_s3_key} from bucket: ${media.bucketName}`);
            }

            // Also delete from final location if it exists and is different from current key
            if (media.final_s3_key && media.final_s3_key !== media.s3Key) {
                deletePromises.push(
                    s3Client.send(new DeleteObjectCommand({
                        Bucket: media.bucketName,
                        Key: media.final_s3_key
                    })).catch(error => {
                        // Don't fail if final file doesn't exist
                        console.log(`Final file ${media.final_s3_key} not found or already deleted:`, error.message);
                    })
                );
                console.log(`Deleting final S3 object: ${media.final_s3_key} from bucket: ${media.bucketName}`);
            }

            // Also delete from review area if it exists
            if (media.review_s3_key) {
                deletePromises.push(
                    s3Client.send(new DeleteObjectCommand({
                        Bucket: media.bucketName,
                        Key: media.review_s3_key
                    })).catch(error => {
                        // Don't fail if review file doesn't exist
                        console.log(`Review file ${media.review_s3_key} not found or already deleted:`, error.message);
                    })
                );
                console.log(`Deleting review S3 object: ${media.review_s3_key} from bucket: ${media.bucketName}`);
            }

            // Execute all deletions
            await Promise.all(deletePromises);
        }

        // Delete from DynamoDB
        const deleteCommand = new DeleteCommand({
            TableName: MEDIA_TABLE,
            Key: { id }
        });

        await dynamodb.send(deleteCommand);

        return createResponse(200, { message: 'Media deleted successfully' });

    } catch (error) {
        console.error('DeleteMedia error:', error);
        const errorMessage = error instanceof Error ? error.message : 'Unknown error';
        return createResponse(500, { error: 'Failed to delete media', details: errorMessage });
    }
};

// Update media status (called after successful upload)
const updateMediaStatus = async (event: APIGatewayProxyEvent): Promise<APIGatewayProxyResult> => {
    try {
        const { id } = event.pathParameters || {};

        if (!id) {
            return createResponse(400, { error: 'Media ID is required' });
        }

        if (!event.body) {
            return createResponse(400, { error: 'Request body is required' });
        }

        const { status }: UpdateStatusRequest = JSON.parse(event.body);

        if (!status) {
            return createResponse(400, { error: 'Status is required' });
        }

        const updateCommand = new UpdateCommand({
            TableName: MEDIA_TABLE,
            Key: { id },
            UpdateExpression: 'SET #status = :status, updatedAt = :updatedAt',
            ExpressionAttributeNames: {
                '#status': 'status'
            },
            ExpressionAttributeValues: {
                ':status': status,
                ':updatedAt': new Date().toISOString()
            },
            ReturnValues: 'ALL_NEW'
        });

        const result = await dynamodb.send(updateCommand);

        return createResponse(200, {
            message: 'Media status updated successfully',
            media: result.Attributes
        });

    } catch (error) {
        console.error('UpdateMediaStatus error:', error);
        const errorMessage = error instanceof Error ? error.message : 'Unknown error';
        return createResponse(500, { error: 'Failed to update media status', details: errorMessage });
    }
};

// Trigger AI processing for uploaded media
const processMedia = async (event: APIGatewayProxyEvent): Promise<APIGatewayProxyResult> => {
    try {
        const { id } = event.pathParameters || {};

        if (!id) {
            return createResponse(400, { error: 'Media ID is required' });
        }

        // Get user ID from authorizer context
        const userId = await getUserIdFromContext(event);
        if (!userId) {
            return createResponse(401, { error: 'User not authenticated' });
        }

        // Get media record
        const getCommand = new GetCommand({
            TableName: MEDIA_TABLE,
            Key: { id }
        });

        const result = await dynamodb.send(getCommand);
        if (!result.Item) {
            return createResponse(404, { error: 'Media not found' });
        }

        const media = result.Item as MediaRecord;

        // Verify user owns this media
        if (media.userId !== userId) {
            return createResponse(403, { error: 'Not authorized to process this media' });
        }

        // Check if media is in correct status for processing
        if (media.status !== 'uploaded') {
            return createResponse(400, {
                error: `Media cannot be processed in current status: ${media.status}`,
                currentStatus: media.status
            });
        }

        // Update status to processing
        const updateCommand = new UpdateCommand({
            TableName: MEDIA_TABLE,
            Key: { id },
            UpdateExpression: 'SET #status = :status, updated_at = :updated_at',
            ExpressionAttributeNames: { '#status': 'status' },
            ExpressionAttributeValues: {
                ':status': 'processing',
                ':updated_at': new Date().toISOString()
            },
            ReturnValues: 'ALL_NEW'
        });

        await dynamodb.send(updateCommand);

        // Note: AI processing will be automatically triggered by S3 event when file is uploaded
        // No need to manually trigger processing as it happens via S3 -> Lambda integration

        console.log('ProcessMedia: Media status updated to processing. AI processing will be triggered automatically by S3 events.');

        return createResponse(200, {
            message: 'Media marked for processing. AI processing will be triggered automatically.',
            mediaId: id,
            status: 'processing',
            note: 'Processing will begin automatically when file upload is detected via S3 events'
        });

    } catch (error) {
        console.error('ProcessMedia error:', error);
        const errorMessage = error instanceof Error ? error.message : 'Unknown error';
        return createResponse(500, { error: 'Failed to process media', details: errorMessage });
    }
};

// Main handler
export const handler = async (event: APIGatewayProxyEvent, context: Context): Promise<APIGatewayProxyResult> => {
    console.log('Event:', JSON.stringify(event, null, 2));

    const { httpMethod, path, pathParameters } = event;

    try {
        if (httpMethod === 'POST' && path === '/media/upload') {
            return await uploadMedia(event);
        } else if (httpMethod === 'POST' && pathParameters && pathParameters.id && path.includes('/process')) {
            return await processMedia(event);
        } else if (httpMethod === 'GET' && pathParameters && pathParameters.id && path.includes('/analysis')) {
            return await getMediaAnalysis(event);
        } else if (httpMethod === 'GET' && pathParameters && pathParameters.id && path.includes('/review')) {
            return await getMediaForReview(event);
        } else if (httpMethod === 'POST' && pathParameters && pathParameters.id && path.includes('/approve')) {
            return await approveRejectedMedia(event);
        } else if (httpMethod === 'GET' && pathParameters && pathParameters.id) {
            return await getMedia(event);
        } else if (httpMethod === 'DELETE' && pathParameters && pathParameters.id) {
            return await deleteMedia(event);
        } else if (httpMethod === 'PUT' && pathParameters && pathParameters.id) {
            return await updateMediaStatus(event);
        } else {
            return createResponse(404, { error: 'Not found' });
        }
    } catch (error) {
        console.error('Handler error:', error);
        const errorMessage = error instanceof Error ? error.message : 'Unknown error';
        return createResponse(500, { error: 'Internal server error', details: errorMessage });
    }
};
