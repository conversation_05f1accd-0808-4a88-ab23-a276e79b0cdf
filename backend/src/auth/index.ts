import {
    CognitoIdentityProviderClient,
    AdminCreate<PERSON>ser<PERSON>ommand,
    AdminSetUserPasswordCommand,
    AdminGet<PERSON>serCommand,
    AdminInitiateAuthCommand,
    GetUserCommand,
    type AdminCreateUserCommandInput,
    type AdminSetUserPasswordCommandInput,
    type AdminGetUserCommandInput,
    type AdminInitiateAuthCommandInput,
    type GetUserCommandInput,
    type AuthFlowType,
    type UserType,
    type AttributeType
} from '@aws-sdk/client-cognito-identity-provider';
import {
    DynamoDBClient
} from '@aws-sdk/client-dynamodb';
import {
    DynamoDBDocumentClient,
    PutCommand,
    QueryCommand,
    GetCommand,
    type PutCommandInput,
    type QueryCommandInput,
    type GetCommandInput
} from '@aws-sdk/lib-dynamodb';
import { APIGatewayProxyEvent, APIGatewayProxyResult } from 'aws-lambda';
import { appleSignIn } from './apple-signin';

// Types
interface SignUpRequest {
    email: string;
    password: string;
    username: string;
    firstName?: string;
    lastName?: string;
}

interface SignInRequest {
    email: string;
    password: string;
}

interface RefreshTokenRequest {
    refreshToken: string;
}

interface UserRecord {
    id: string;
    email: string;
    username?: string;
    firstName: string;
    lastName: string;
    cognitoUserId: string;
    createdAt: string;
    updatedAt: string;
}

interface AuthTokens {
    accessToken: string;
    refreshToken: string;
    idToken: string;
}

interface UserResponse {
    id: string;
    email: string;
    username?: string;
    firstName: string;
    lastName: string;
}

// Configure AWS SDK for SAM local
const awsConfig = {
    region: process.env.AWS_REGION || 'us-west-2'
};

// For local development with SAM, we need to set the endpoint if AWS_SAM_LOCAL is set
if (process.env.AWS_SAM_LOCAL) {
    console.log('Running in SAM Local mode');
}

const cognitoClient = new CognitoIdentityProviderClient(awsConfig);
const dynamoClient = new DynamoDBClient(awsConfig);
const dynamodb = DynamoDBDocumentClient.from(dynamoClient);

// Environment variables - check multiple possible names for compatibility
const USER_POOL_ID = process.env.USER_POOL_ID || process.env.USERPOOL_USER_POOL_ID;
const USER_POOL_CLIENT_ID = process.env.USER_POOL_CLIENT_ID || process.env.USERPOOLCLIENT_USER_POOL_CLIENT_ID;
const USERS_TABLE = process.env.USERS_TABLE || process.env.USERSTABLE_TABLE_NAME;

console.log('Environment variables:');
console.log('USER_POOL_ID:', USER_POOL_ID);
console.log('USER_POOL_CLIENT_ID:', USER_POOL_CLIENT_ID);
console.log('USERS_TABLE:', USERS_TABLE);

// Helper function to create response
const createResponse = (statusCode: number, body: any): APIGatewayProxyResult => ({
    statusCode,
    headers: {
        'Content-Type': 'application/json',
        'Access-Control-Allow-Origin': '*',
        'Access-Control-Allow-Headers': 'Content-Type,X-Amz-Date,Authorization,X-Api-Key,X-Amz-Security-Token',
        'Access-Control-Allow-Methods': 'GET,POST,PUT,DELETE,OPTIONS'
    },
    body: JSON.stringify(body)
});



// Sign up function
const signUp = async (event: APIGatewayProxyEvent): Promise<APIGatewayProxyResult> => {
    try {
        if (!event.body) {
            return createResponse(400, { error: 'Request body is required' });
        }

        const { email, password, username, firstName, lastName }: SignUpRequest = JSON.parse(event.body);

        if (!email || !password || !username) {
            return createResponse(400, { error: 'Email, password, and username are required' });
        }

        if (!USER_POOL_ID) {
            return createResponse(500, { error: 'User pool configuration missing' });
        }

        // Create user in Cognito
        const cognitoParams: AdminCreateUserCommandInput = {
            UserPoolId: USER_POOL_ID,
            Username: email,
            TemporaryPassword: password,
            MessageAction: 'SUPPRESS',
            UserAttributes: [
                { Name: 'email', Value: email },
                { Name: 'email_verified', Value: 'true' },
                { Name: 'given_name', Value: firstName || '' },
                { Name: 'family_name', Value: lastName || '' }
            ]
        };

        const createUserCommand = new AdminCreateUserCommand(cognitoParams);
        const cognitoUser = await cognitoClient.send(createUserCommand);

        // Set permanent password
        const setPasswordCommand = new AdminSetUserPasswordCommand({
            UserPoolId: USER_POOL_ID,
            Username: email,
            Password: password,
            Permanent: true
        });
        await cognitoClient.send(setPasswordCommand);

        // Get the user details to extract the sub (user ID)
        const getUserCommand = new AdminGetUserCommand({
            UserPoolId: USER_POOL_ID,
            Username: email
        });
        const userDetails = await cognitoClient.send(getUserCommand);

        // Extract the sub attribute which will be our user ID
        const userAttributes: Record<string, string> = {};
        userDetails.UserAttributes?.forEach((attr: AttributeType) => {
            if (attr.Name && attr.Value) {
                userAttributes[attr.Name] = attr.Value;
            }
        });

        const userId = userAttributes.sub;
        if (!userId) {
            return createResponse(500, { error: 'Failed to get user ID from Cognito' });
        }

        const userRecord: UserRecord = {
            id: userId,
            email,
            username,
            firstName: firstName || '',
            lastName: lastName || '',
            cognitoUserId: cognitoUser.User?.Username || email,
            createdAt: new Date().toISOString(),
            updatedAt: new Date().toISOString()
        };

        if (!USERS_TABLE) {
            return createResponse(500, { error: 'Users table configuration missing' });
        }

        const putCommand = new PutCommand({
            TableName: USERS_TABLE,
            Item: userRecord
        });
        await dynamodb.send(putCommand);

        return createResponse(201, {
            message: 'User created successfully',
            user: {
                id: userId,
                email,
                username,
                firstName: firstName || '',
                lastName: lastName || ''
            }
        });

    } catch (error: any) {
        console.error('SignUp error:', error);
        return createResponse(500, { error: 'Failed to create user', details: error.message });
    }
};

// Sign in function
const signIn = async (event: APIGatewayProxyEvent): Promise<APIGatewayProxyResult> => {
    try {
        if (!event.body) {
            return createResponse(400, { error: 'Request body is required' });
        }

        const { email, password }: SignInRequest = JSON.parse(event.body);

        if (!email || !password) {
            return createResponse(400, { error: 'Email and password are required' });
        }

        if (!USER_POOL_ID || !USER_POOL_CLIENT_ID) {
            return createResponse(500, { error: 'Cognito configuration missing' });
        }

        // Authenticate with Cognito
        const authParams: AdminInitiateAuthCommandInput = {
            UserPoolId: USER_POOL_ID,
            ClientId: USER_POOL_CLIENT_ID,
            AuthFlow: 'ADMIN_USER_PASSWORD_AUTH' as AuthFlowType,
            AuthParameters: {
                USERNAME: email,
                PASSWORD: password
            }
        };

        const authCommand = new AdminInitiateAuthCommand(authParams);
        const authResult = await cognitoClient.send(authCommand);

        if (!USERS_TABLE) {
            return createResponse(500, { error: 'Users table configuration missing' });
        }

        // Get user details from DynamoDB
        const queryCommand = new QueryCommand({
            TableName: USERS_TABLE,
            IndexName: 'EmailIndex',
            KeyConditionExpression: 'email = :email',
            ExpressionAttributeValues: {
                ':email': email
            }
        });
        const userResult = await dynamodb.send(queryCommand);

        if (!userResult.Items || userResult.Items.length === 0) {
            return createResponse(404, { error: 'User not found' });
        }

        const user = userResult.Items[0] as UserRecord;

        if (!authResult.AuthenticationResult) {
            return createResponse(401, { error: 'Authentication failed' });
        }

        return createResponse(200, {
            message: 'Sign in successful',
            tokens: {
                accessToken: authResult.AuthenticationResult.AccessToken,
                refreshToken: authResult.AuthenticationResult.RefreshToken,
                idToken: authResult.AuthenticationResult.IdToken
            },
            user: {
                id: user.id,
                email: user.email,
                username: user.username,
                firstName: user.firstName,
                lastName: user.lastName
            }
        });

    } catch (error: any) {
        console.error('SignIn error:', error);
        return createResponse(401, { error: 'Authentication failed', details: error.message });
    }
};

// Refresh token function
const refreshToken = async (event: APIGatewayProxyEvent): Promise<APIGatewayProxyResult> => {
    try {
        if (!event.body) {
            return createResponse(400, { error: 'Request body is required' });
        }

        const { refreshToken }: RefreshTokenRequest = JSON.parse(event.body);

        if (!refreshToken) {
            return createResponse(400, { error: 'Refresh token is required' });
        }

        if (!USER_POOL_ID || !USER_POOL_CLIENT_ID) {
            return createResponse(500, { error: 'Cognito configuration missing' });
        }

        const refreshParams: AdminInitiateAuthCommandInput = {
            UserPoolId: USER_POOL_ID,
            ClientId: USER_POOL_CLIENT_ID,
            AuthFlow: 'REFRESH_TOKEN_AUTH' as AuthFlowType,
            AuthParameters: {
                REFRESH_TOKEN: refreshToken
            }
        };

        const refreshCommand = new AdminInitiateAuthCommand(refreshParams);
        const authResult = await cognitoClient.send(refreshCommand);

        if (!authResult.AuthenticationResult) {
            return createResponse(401, { error: 'Token refresh failed' });
        }

        return createResponse(200, {
            message: 'Token refreshed successfully',
            tokens: {
                accessToken: authResult.AuthenticationResult.AccessToken,
                idToken: authResult.AuthenticationResult.IdToken
            }
        });

    } catch (error: any) {
        console.error('RefreshToken error:', error);
        return createResponse(401, { error: 'Token refresh failed', details: error.message });
    }
};

// Validate token function
const validateToken = async (event: APIGatewayProxyEvent): Promise<APIGatewayProxyResult> => {
    try {
        // Get token from Authorization header
        const authHeader = event.headers?.Authorization || event.headers?.authorization;

        if (!authHeader || !authHeader.startsWith('Bearer ')) {
            return createResponse(401, { error: 'Authorization header with Bearer token required' });
        }

        const accessToken = authHeader.substring(7);

        try {
            // Validate token with Cognito
            const getUserParams: GetUserCommandInput = {
                AccessToken: accessToken
            };

            const getUserCommand = new GetUserCommand(getUserParams);
            const cognitoUser = await cognitoClient.send(getUserCommand);

            // Get the user ID from Cognito (this is the 'sub' claim in the JWT)
            const userId = cognitoUser.Username; // This is actually the user ID in Cognito

            if (!userId) {
                return createResponse(401, { error: 'Invalid token - no user ID' });
            }

            if (!USERS_TABLE) {
                return createResponse(500, { error: 'Users table configuration missing' });
            }

            // Get user details from DynamoDB using user ID
            const getCommand = new GetCommand({
                TableName: USERS_TABLE,
                Key: {
                    id: userId
                }
            });
            const userResult = await dynamodb.send(getCommand);

            if (!userResult.Item) {
                return createResponse(404, { error: 'User not found' });
            }

            const user = userResult.Item as UserRecord;

            return createResponse(200, {
                message: 'Token is valid',
                valid: true,
                user: {
                    id: user.id,
                    email: user.email,
                    username: user.username,
                    firstName: user.firstName,
                    lastName: user.lastName
                }
            });

        } catch (cognitoError: any) {
            console.error('Token validation error:', cognitoError);
            return createResponse(401, {
                error: 'Invalid or expired token',
                valid: false
            });
        }

    } catch (error: any) {
        console.error('Validate token error:', error);
        return createResponse(500, { error: 'Internal server error', details: error.message });
    }
};

// Main handler
export const handler = async (event: APIGatewayProxyEvent): Promise<APIGatewayProxyResult> => {
    console.log('Event:', JSON.stringify(event, null, 2));

    const { httpMethod, path } = event;

    // Handle CORS preflight requests
    if (httpMethod === 'OPTIONS') {
        return createResponse(200, {});
    }

    try {
        console.log(`Processing: ${httpMethod} ${path}`);

        // Route based on HTTP method and path (REST approach)
        if (httpMethod === 'POST' && path === '/auth/signup') {
            console.log('Calling signUp');
            return await signUp(event);
        } else if (httpMethod === 'POST' && path === '/auth/signin') {
            console.log('Calling signIn');
            return await signIn(event);
        } else if (httpMethod === 'POST' && path === '/auth/refresh') {
            console.log('Calling refreshToken');
            return await refreshToken(event);
        } else if (httpMethod === 'GET' && path === '/auth/validate') {
            console.log('Calling validateToken');
            return await validateToken(event);
        } else if (httpMethod === 'POST' && path === '/auth/apple') {
            console.log('Calling appleSignIn');
            return await appleSignIn(event);
        } else {
            console.log(`No matching route for: ${httpMethod} ${path}`);
            return createResponse(404, { error: 'Not found', message: `Unsupported route: ${httpMethod} ${path}` });
        }
    } catch (error: any) {
        console.error('Handler error:', error);
        return createResponse(500, { error: 'Internal server error', details: error.message });
    }
};
