import {
    CognitoIdentityProviderClient,
    AdminInitiateAuthCommand,
    type AdminInitiateAuthCommandInput,
    type AuthFlowType
} from '@aws-sdk/client-cognito-identity-provider';
import { APIGatewayProxyEvent, APIGatewayProxyResult } from 'aws-lambda';

// Types
interface RefreshTokenRequest {
    refreshToken: string;
}

// Configure AWS SDK
const awsConfig = {
    region: process.env.AWS_REGION || 'us-west-2'
};

if (process.env.AWS_SAM_LOCAL) {
    console.log('Running in SAM Local mode');
}

const cognitoClient = new CognitoIdentityProviderClient(awsConfig);

// Environment variables
const USER_POOL_ID = process.env.USER_POOL_ID || process.env.USERPOOL_USER_POOL_ID;
const USER_POOL_CLIENT_ID = process.env.USER_POOL_CLIENT_ID || process.env.USERPOOLCLIENT_USER_POOL_CLIENT_ID;

// Helper function to create response
const createResponse = (statusCode: number, body: any): APIGatewayProxyResult => ({
    statusCode,
    headers: {
        'Content-Type': 'application/json',
        'Access-Control-Allow-Origin': '*',
        'Access-Control-Allow-Headers': 'Content-Type,X-Amz-Date,Authorization,X-Api-Key,X-Amz-Security-Token',
        'Access-Control-Allow-Methods': 'GET,POST,PUT,DELETE,OPTIONS'
    },
    body: JSON.stringify(body)
});

// Refresh token handler
export const handler = async (event: APIGatewayProxyEvent): Promise<APIGatewayProxyResult> => {
    console.log('RefreshToken Event:', JSON.stringify(event, null, 2));

    // Handle CORS preflight requests
    if (event.httpMethod === 'OPTIONS') {
        return createResponse(200, {});
    }

    try {
        if (!event.body) {
            return createResponse(400, { error: 'Request body is required' });
        }

        const { refreshToken }: RefreshTokenRequest = JSON.parse(event.body);

        if (!refreshToken) {
            return createResponse(400, { error: 'Refresh token is required' });
        }

        if (!USER_POOL_ID || !USER_POOL_CLIENT_ID) {
            return createResponse(500, { error: 'Cognito configuration missing' });
        }

        const refreshParams: AdminInitiateAuthCommandInput = {
            UserPoolId: USER_POOL_ID,
            ClientId: USER_POOL_CLIENT_ID,
            AuthFlow: 'REFRESH_TOKEN_AUTH' as AuthFlowType,
            AuthParameters: {
                REFRESH_TOKEN: refreshToken
            }
        };

        const refreshCommand = new AdminInitiateAuthCommand(refreshParams);
        const authResult = await cognitoClient.send(refreshCommand);

        if (!authResult.AuthenticationResult) {
            return createResponse(401, { error: 'Token refresh failed' });
        }

        return createResponse(200, {
            message: 'Token refreshed successfully',
            tokens: {
                accessToken: authResult.AuthenticationResult.AccessToken,
                idToken: authResult.AuthenticationResult.IdToken
            }
        });

    } catch (error: any) {
        console.error('RefreshToken error:', error);
        return createResponse(401, { error: 'Token refresh failed', details: error.message });
    }
};
