import {
    CognitoIdentityProviderClient,
    AdminUpdateUserAttributesCommand,
    type AdminUpdateUserAttributesCommandInput
} from '@aws-sdk/client-cognito-identity-provider';
import {
    DynamoDBClient
} from '@aws-sdk/client-dynamodb';
import {
    DynamoDBDocumentClient,
    UpdateCommand,
    QueryCommand,
    GetCommand,
    type UpdateCommandInput,
    type QueryCommandInput,
    type GetCommandInput
} from '@aws-sdk/lib-dynamodb';
import { APIGatewayProxyEvent, APIGatewayProxyResult } from 'aws-lambda';
// Types
interface SetUsernameRequest {
    username: string;
}

interface UserRecord {
    id: string;
    email: string;
    username?: string;
    firstName: string;
    lastName: string;
    cognitoUserId: string;
    createdAt: string;
    updatedAt: string;
}

// Configure AWS SDK
const awsConfig = {
    region: process.env.AWS_REGION || 'us-west-2'
};

if (process.env.AWS_SAM_LOCAL) {
    console.log('Running in SAM Local mode');
}

const cognitoClient = new CognitoIdentityProviderClient(awsConfig);
const dynamoClient = new DynamoDBClient(awsConfig);
const dynamodb = DynamoDBDocumentClient.from(dynamoClient);

// Environment variables
const USER_POOL_ID = process.env.USER_POOL_ID || process.env.USERPOOL_USER_POOL_ID;
const USERS_TABLE = process.env.USERS_TABLE || process.env.USERSTABLE_TABLE_NAME;

// Helper function to create response
const createResponse = (statusCode: number, body: any): APIGatewayProxyResult => ({
    statusCode,
    headers: {
        'Content-Type': 'application/json',
        'Access-Control-Allow-Origin': '*',
        'Access-Control-Allow-Headers': 'Content-Type,X-Amz-Date,Authorization,X-Api-Key,X-Amz-Security-Token',
        'Access-Control-Allow-Methods': 'OPTIONS,POST,GET,PUT,DELETE'
    },
    body: JSON.stringify(body)
});

// Helper function to escape regex special characters
const escapeRegex = (string: string): string => {
    return string.replace(/[.*+?^${}()|[\]\\]/g, '\\$&');
};

// Helper function to check if a bad word appears as a whole word or at boundaries
const isWordBoundaryMatch = (text: string, badWord: string): boolean => {
    const lowerText = text.toLowerCase();
    const lowerBadWord = badWord.toLowerCase();

    // Create regex with word boundaries
    const wordBoundaryRegex = new RegExp(`\\b${escapeRegex(lowerBadWord)}\\b`, 'i');
    if (wordBoundaryRegex.test(lowerText)) {
        return true;
    }

    // Check if bad word appears at the start or end of the username
    if (lowerText.startsWith(lowerBadWord) || lowerText.endsWith(lowerBadWord)) {
        return true;
    }

    // Check if bad word appears separated by numbers or special characters
    // e.g., "fuck123", "123shit", "bad_word"
    const separatorRegex = new RegExp(`(^|[^a-z])${escapeRegex(lowerBadWord)}([^a-z]|$)`, 'i');
    if (separatorRegex.test(lowerText)) {
        return true;
    }

    return false;
};

// Common legitimate words that contain potential bad word substrings
const legitimateWords = new Set([
    'grass', 'class', 'classic', 'glass', 'pass', 'password', 'assessment', 'assist', 'assignment',
    'bass', 'mass', 'brass', 'compass', 'bypass', 'trespass', 'harass', 'embarrass',
    'hell', 'hello', 'shell', 'smell', 'spell', 'dwell', 'swell', 'well',
    'damn', 'damnation', // sometimes used in legitimate contexts
    'scrap', 'scrape', 'script', 'describe', 'prescription',
    'analyze', 'analysis', 'analyst',
]);

// Helper function to check if a username contains intentional profanity
const containsIntentionalProfanity = (username: string, badWordsList: string[]): boolean => {
    const lowerUsername = username.toLowerCase();

    // First check if the username (without numbers/symbols) is a known legitimate word
    const lettersOnly = lowerUsername.replace(/[^a-z]/g, '');
    if (legitimateWords.has(lettersOnly)) {
        return false;
    }

    for (const badWord of badWordsList) {
        const lowerBadWord = badWord.toLowerCase();

        // Skip very short words that are likely to cause false positives
        if (lowerBadWord.length <= 2) {
            continue;
        }

        // Skip common short words that appear in many legitimate words
        if (['ass', 'hell', 'damn'].includes(lowerBadWord) && lowerBadWord.length <= 4) {
            // For these words, only flag if they appear as standalone words or with minimal additions
            if (isWordBoundaryMatch(lowerUsername, lowerBadWord)) {
                // Check if it's part of a longer legitimate word
                const beforeBadWord = lowerUsername.substring(0, lowerUsername.indexOf(lowerBadWord));
                const afterBadWord = lowerUsername.substring(lowerUsername.indexOf(lowerBadWord) + lowerBadWord.length);

                // If there are significant letters before or after, it might be legitimate
                const beforeLetters = beforeBadWord.replace(/[^a-z]/g, '');
                const afterLetters = afterBadWord.replace(/[^a-z]/g, '');

                // If the bad word is surrounded by letters (like "grass", "classic"), it's likely legitimate
                if (beforeLetters.length > 0 && afterLetters.length > 0) {
                    continue;
                }

                // Special case: if it's followed by common profane combinations, block it
                const afterText = afterBadWord.toLowerCase();
                if (lowerBadWord === 'hell' && (afterText.includes('yeah') || afterText.includes('yes'))) {
                    return true;
                }
                if (lowerBadWord === 'damn' && (afterText.includes('it') || afterText.includes('you'))) {
                    return true;
                }

                // If it's at the start/end with only numbers/symbols, it's likely intentional
                if ((beforeLetters.length === 0 && afterLetters.length <= 1) ||
                    (afterLetters.length === 0 && beforeLetters.length <= 1)) {
                    return true;
                }
            }
            continue;
        }

        // Check for word boundary matches (most reliable for longer words)
        if (isWordBoundaryMatch(lowerUsername, lowerBadWord)) {
            return true;
        }

        // For longer bad words (4+ chars), check if they appear as a significant portion
        if (lowerBadWord.length >= 4) {
            // Check if the bad word makes up a significant portion of the username
            const badWordRatio = lowerBadWord.length / lowerUsername.length;
            if (badWordRatio >= 0.6 && lowerUsername.includes(lowerBadWord)) {
                // Additional check: make sure it's not just a substring of a legitimate word
                const withoutBadWord = lowerUsername.replace(lowerBadWord, '');
                const remainingChars = withoutBadWord.replace(/[^a-z]/g, '');

                // If most remaining characters are just numbers/symbols, it's likely intentional profanity
                if (remainingChars.length <= 2) {
                    return true;
                }
            }
        }
    }

    return false;
};

// Helper function to check profanity
const checkProfanity = async (username: string): Promise<boolean> => {
    try {
        // Dynamically import the bad-words library
        const { Filter } = await import('bad-words');
        const badWordsFilter = new Filter();

        // Use the built-in isProfane method first (most reliable for exact matches)
        if (badWordsFilter.isProfane(username)) {
            return true;
        }

        // Get the list of bad words for more sophisticated checking
        const badWordsList: string[] = badWordsFilter.list || [];

        // Use our more sophisticated checking
        if (containsIntentionalProfanity(username, badWordsList)) {
            return true;
        }

        // Check normalized versions (remove separators and try again)
        const normalizedVariants = [
            username.replace(/[\s_.-]/g, ''), // Remove separators
            username.replace(/[0-9]/g, ''),   // Remove numbers
        ];

        for (const variant of normalizedVariants) {
            if (variant.length > 0 && variant !== username) {
                // Only check if the variant is significantly different and still meaningful
                if (variant.length >= 3 && badWordsFilter.isProfane(variant)) {
                    return true;
                }
            }
        }

        return false;
    } catch (error) {
        console.error('Error loading bad-words library:', error);
        // If the library fails to load, we'll skip profanity checking
        return false;
    }
};

// Helper function to get user ID from context
const getUserIdFromContext = (event: APIGatewayProxyEvent): string | null => {
    return event.requestContext?.authorizer?.userId || null;
};

// Check username availability handler
const checkUsernameAvailability = async (event: APIGatewayProxyEvent): Promise<APIGatewayProxyResult> => {
    const username = event.queryStringParameters?.username;

    if (!username) {
        return createResponse(400, {
            error: 'Username parameter is required',
            available: false,
            valid: false
        });
    }

    // Normalize username to lowercase for case-insensitive handling
    const normalizedUsername = username.toLowerCase();

    // Validate username length
    if (normalizedUsername.length < 4 || normalizedUsername.length > 32) {
        return createResponse(200, {
            available: false,
            valid: false,
            error: 'Invalid username length',
            details: 'Username must be 4-32 characters long'
        });
    }

    // Validate username format
    const usernameRegex = /^[a-zA-Z0-9_-]+$/;
    if (!usernameRegex.test(normalizedUsername)) {
        return createResponse(200, {
            available: false,
            valid: false,
            error: 'Invalid username format',
            details: 'Username can only contain alphanumeric characters, underscores, and hyphens'
        });
    }

    // Check for profanity
    if (await checkProfanity(normalizedUsername)) {
        return createResponse(200, {
            available: false,
            valid: false,
            error: 'Invalid username',
            details: 'Username contains inappropriate content. Please choose a different username.'
        });
    }

    if (!USERS_TABLE) {
        return createResponse(500, {
            error: 'Users table configuration missing',
            available: false,
            valid: false
        });
    }

    // Check if username already exists
    try {
        const checkUsernameCommand = new QueryCommand({
            TableName: USERS_TABLE,
            IndexName: 'UsernameIndex',
            KeyConditionExpression: 'username = :username',
            ExpressionAttributeValues: {
                ':username': normalizedUsername
            }
        });

        const existingUserResult = await dynamodb.send(checkUsernameCommand);

        const isAvailable = !existingUserResult.Items || existingUserResult.Items.length === 0;

        return createResponse(200, {
            available: isAvailable,
            valid: true,
            username: normalizedUsername
        });
    } catch (error: any) {
        console.error('Check username availability error:', error);
        return createResponse(500, {
            error: 'Failed to check username availability',
            available: false,
            valid: false
        });
    }
};

// Set username handler
export const handler = async (event: APIGatewayProxyEvent): Promise<APIGatewayProxyResult> => {
    console.log('SetUsername Event:', JSON.stringify(event, null, 2));

    // Handle CORS preflight requests
    if (event.httpMethod === 'OPTIONS') {
        return createResponse(200, {});
    }

    // Route to appropriate handler based on HTTP method and path
    if (event.httpMethod === 'GET' && event.path.includes('/check-username-availability')) {
        return await checkUsernameAvailability(event);
    }

    try {
        if (!event.body) {
            return createResponse(400, { error: 'Request body is required' });
        }

        const { username: rawUsername }: SetUsernameRequest = JSON.parse(event.body);

        if (!rawUsername) {
            return createResponse(400, { error: 'Username is required' });
        }

        // Normalize username to lowercase for case-insensitive handling
        const username = rawUsername.toLowerCase();

        // Validate username length
        if (username.length < 4 || username.length > 32) {
            return createResponse(400, {
                error: 'Invalid username length',
                details: 'Username must be 4-32 characters long'
            });
        }

        // Validate username format
        const usernameRegex = /^[a-zA-Z0-9_-]+$/;
        if (!usernameRegex.test(username)) {
            return createResponse(400, {
                error: 'Invalid username format',
                details: 'Username can only contain alphanumeric characters, underscores, and hyphens'
            });
        }

        // Check for profanity
        if (await checkProfanity(username)) {
            return createResponse(400, {
                error: 'Invalid username',
                details: 'Username contains inappropriate content. Please choose a different username.'
            });
        }

        // Get user ID from authorizer context
        const userId = getUserIdFromContext(event);

        if (!userId) {
            return createResponse(401, { error: 'User not authenticated' });
        }

        if (!USERS_TABLE) {
            return createResponse(500, { error: 'Users table configuration missing' });
        }

        // Get current user to verify they exist
        const getUserCommand = new GetCommand({
            TableName: USERS_TABLE,
            Key: { id: userId }
        });

        const userResult = await dynamodb.send(getUserCommand);

        if (!userResult.Item) {
            return createResponse(404, { error: 'User not found' });
        }

        const user = userResult.Item as UserRecord;

        // Check if user already has a username
        if (user.username) {
            return createResponse(409, {
                error: 'Username already set',
                details: 'You already have a username. Contact support if you need to change it.'
            });
        }

        // Check if username already exists (case-insensitive)
        const checkUsernameCommand = new QueryCommand({
            TableName: USERS_TABLE,
            IndexName: 'UsernameIndex',
            KeyConditionExpression: 'username = :username',
            ExpressionAttributeValues: {
                ':username': username
            }
        });

        const existingUserResult = await dynamodb.send(checkUsernameCommand);

        if (existingUserResult.Items && existingUserResult.Items.length > 0) {
            return createResponse(409, {
                error: 'Username already taken',
                details: 'This username is already in use. Please choose a different one.'
            });
        }

        // Update user with username
        const updateCommand = new UpdateCommand({
            TableName: USERS_TABLE,
            Key: { id: userId },
            UpdateExpression: 'SET username = :username, updatedAt = :updatedAt',
            ExpressionAttributeValues: {
                ':username': username,
                ':updatedAt': new Date().toISOString()
            },
            ReturnValues: 'ALL_NEW'
        });

        const updateResult = await dynamodb.send(updateCommand);
        const updatedUser = updateResult.Attributes as UserRecord;

        // Update Cognito user attributes with the username
        if (!USER_POOL_ID) {
            console.error('USER_POOL_ID not configured, skipping Cognito update');
        } else {
            try {
                const updateCognitoCommand = new AdminUpdateUserAttributesCommand({
                    UserPoolId: USER_POOL_ID,
                    Username: user.cognitoUserId,
                    UserAttributes: [
                        {
                            Name: 'preferred_username',
                            Value: username
                        }
                    ]
                });
                await cognitoClient.send(updateCognitoCommand);
                console.log('Successfully updated Cognito user attributes with username');
            } catch (cognitoError) {
                console.error('Failed to update Cognito user attributes:', cognitoError);
                // Don't fail the request if Cognito update fails, as DynamoDB is already updated
            }
        }

        return createResponse(200, {
            message: 'Username set successfully',
            user: {
                id: updatedUser.id,
                email: updatedUser.email,
                username: updatedUser.username,
                firstName: updatedUser.firstName,
                lastName: updatedUser.lastName
            }
        });

    } catch (error: any) {
        console.error('SetUsername error:', error);

        // Handle specific DynamoDB errors
        if (error.name === 'ConditionalCheckFailedException') {
            return createResponse(409, {
                error: 'Username conflict',
                details: 'This username is already taken or there was a conflict updating your account.'
            });
        } else if (error.name === 'ValidationException') {
            return createResponse(400, {
                error: 'Invalid request',
                details: 'Please check your input and try again.'
            });
        } else {
            return createResponse(500, {
                error: 'Failed to set username',
                details: 'An unexpected error occurred. Please try again.'
            });
        }
    }
};
