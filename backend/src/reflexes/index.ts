import { APIGatewayProxyEvent, APIGatewayProxyResult, Context } from 'aws-lambda';
import { DynamoDBClient } from '@aws-sdk/client-dynamodb';
import { DynamoDBDocumentClient, GetCommand, PutCommand, DeleteCommand, UpdateCommand, QueryCommand } from '@aws-sdk/lib-dynamodb';
import { v4 as uuidv4 } from 'uuid';

// Configure AWS SDK for DynamoDB
const awsConfig = {
    region: process.env.AWS_REGION || 'us-east-1'
};

const dynamodbClient = new DynamoDBClient(awsConfig);
const dynamodb = DynamoDBDocumentClient.from(dynamodbClient);

const REFLEXES_TABLE = process.env.REFLEXES_TABLE || 'test-reflexes-table';
const REFLEX_LIKES_TABLE = process.env.REFLEX_LIKES_TABLE || 'test-reflex-likes-table';
const POSTS_TABLE = process.env.POSTS_TABLE || 'test-posts-table';
const USERS_TABLE = process.env.USERS_TABLE || 'test-users-table';
const MEDIA_TABLE = process.env.MEDIA_TABLE || 'test-media-table';
const REFLEX_REACTIONS_TABLE = process.env.REFLEX_REACTIONS_TABLE || 'test-reflex-reactions-table';

// TypeScript interfaces
interface Reflex {
    id: string;
    postId: string;
    userId: string;
    mediaId?: string;
    flareData?: any;
    textOverlay?: string;
    reflexType: 'flare' | 'custom_image';
    likes: number;
    isActive: boolean;
    createdAt: string;
    updatedAt: string;
    username?: string;
    displayName?: string;
    avatar_url?: string;
    avatarUrl?: string;
    isLikedByCurrentUser?: boolean;
}

interface CreateReflexRequest {
    mediaId?: string;
    flareData?: any;
    textOverlay?: string;
    reflexType?: 'flare' | 'custom_image';
}

interface ReflexLike {
    reflexId: string;
    userId: string;
    createdAt: string;
}

interface Post {
    id: string;
    reflexes?: number;
}

interface User {
    id: string;
    username?: string;
    displayName?: string;
    avatar_url?: string;
    avatarUrl?: string;
}

interface Media {
    id: string;
}

interface ReflexReaction {
    reflexId: string;
    reactionKey: string; // `${emoji}#${userId}`
    emoji: string;
    userId: string;
    createdAt: string;
}

// Helper function to create response
const createResponse = (statusCode: number, body: any): APIGatewayProxyResult => ({
    statusCode,
    headers: {
        'Content-Type': 'application/json',
        'Access-Control-Allow-Origin': '*',
        'Access-Control-Allow-Headers': 'Content-Type,X-Amz-Date,Authorization,X-Api-Key,X-Amz-Security-Token',
        'Access-Control-Allow-Methods': 'GET,POST,PUT,DELETE,OPTIONS'
    },
    body: JSON.stringify(body)
});

// Helper function to get user ID from event
const getUserIdFromEvent = (event: APIGatewayProxyEvent): string => {
    try {
        if (event.requestContext && event.requestContext.authorizer) {
            const userId = (event.requestContext.authorizer as any).userId;
            if (userId) {
                return userId;
            }
        }
        throw new Error('User ID not found in request context');
    } catch (error) {
        console.error('Error extracting user ID:', error);
        throw new Error('Authentication required');
    }
};

// Get reflexes for a post
const getReflexes = async (event: APIGatewayProxyEvent): Promise<APIGatewayProxyResult> => {
    try {
        const { id } = event.pathParameters || {};

        if (!id) {
            return createResponse(400, { error: 'Post ID is required' });
        }

        // Verify post exists
        const postCommand = new GetCommand({
            TableName: POSTS_TABLE,
            Key: { id }
        });

        const postResult = await dynamodb.send(postCommand);

        if (!postResult.Item) {
            return createResponse(404, { error: 'Post not found' });
        }

        // Query reflexes for the post
        const queryCommand = new QueryCommand({
            TableName: REFLEXES_TABLE,
            IndexName: 'postId-index',
            KeyConditionExpression: 'postId = :postId',
            ExpressionAttributeValues: {
                ':postId': id
            },
            ScanIndexForward: false // Get most recent first
        });

        const result = await dynamodb.send(queryCommand);

        // Get current user ID for like status check
        const currentUserId = getUserIdFromEvent(event);

        // Enrich reflexes with user data and like status
        const reflexesWithUserData = await Promise.all(
            (result.Items || []).map(async (reflex: any) => {
                try {
                    // Fetch user data
                    const userCommand = new GetCommand({
                        TableName: USERS_TABLE,
                        Key: { id: reflex.userId }
                    });

                    const userResult = await dynamodb.send(userCommand);

                    if (userResult.Item) {
                        const user = userResult.Item as User;
                        reflex.username = user.username;
                        reflex.displayName = user.displayName;
                        reflex.avatar_url = user.avatar_url;
                    }

                    // Check if current user has liked this reflex
                    let isLikedByCurrentUser = false;
                    if (currentUserId) {
                        try {
                            const getLikeCommand = new GetCommand({
                                TableName: REFLEX_LIKES_TABLE,
                                Key: {
                                    reflexId: reflex.id,
                                    userId: currentUserId
                                }
                            });
                            const likeResult = await dynamodb.send(getLikeCommand);
                            isLikedByCurrentUser = !!likeResult.Item;
                        } catch (error) {
                            console.error(`Failed to check like status for reflex ${reflex.id}:`, error);
                            // Continue with false if check fails
                        }
                    }

                    // Fetch reaction summary and current user's selections for each reflex
                    try {
                        const rxQuery = new QueryCommand({
                            TableName: REFLEX_REACTIONS_TABLE,
                            KeyConditionExpression: 'reflexId = :reflexId',
                            ExpressionAttributeValues: { ':reflexId': reflex.id }
                        });
                        const rxRes = await dynamodb.send(rxQuery);
                        const counts: Record<string, number> = {};
                        const userEmojis: string[] = [];
                        for (const r of (rxRes.Items || [])) {
                            const emoji = (r as any).emoji;
                            counts[emoji] = (counts[emoji] || 0) + 1;
                            if (currentUserId && (r as any).userId === currentUserId) {
                                userEmojis.push(emoji);
                            }
                        }
                        (reflex as any).reactions = counts;
                        // Note: userEmojis should contain at most 1 emoji due to single reaction enforcement
                        (reflex as any).currentUserReactions = userEmojis;
                    } catch (error) {
                        console.error(`Failed to fetch reactions for reflex ${reflex.id}:`, error);
                    }

                    // Add the like status to the reflex
                    reflex.isLikedByCurrentUser = isLikedByCurrentUser;

                    // Fetch media data if reflex has mediaId
                    if (reflex.mediaId) {
                        try {
                            const getMediaCommand = new GetCommand({
                                TableName: MEDIA_TABLE,
                                Key: { id: reflex.mediaId }
                            });
                            const mediaResult = await dynamodb.send(getMediaCommand);

                            if (mediaResult.Item) {
                                const media = mediaResult.Item as any;

                                // Use final_url as fallback if url is empty (for backward compatibility)
                                if (!media.url && media.final_url) {
                                    media.url = media.final_url;
                                }

                                reflex.media = media;
                            }
                        } catch (error) {
                            console.error(`Failed to fetch media for reflex ${reflex.id}:`, error);
                            // Continue without media data if fetch fails
                        }
                    }

                } catch (error) {
                    console.error('Error fetching user data for reflex:', error);
                    // Continue without user data
                }
                return reflex as Reflex;
            })
        );

        return createResponse(200, {
            reflexes: reflexesWithUserData,
            count: reflexesWithUserData.length
        });
    } catch (error) {
        console.error('GetReflexes error:', error);
        const errorMessage = error instanceof Error ? error.message : 'Unknown error';
        return createResponse(500, { error: 'Failed to get reflexes', details: errorMessage });
    }
};

// Create a new reflex
const createReflex = async (event: APIGatewayProxyEvent): Promise<APIGatewayProxyResult> => {
    try {
        const { id } = event.pathParameters || {}; // post_id
        const userId = getUserIdFromEvent(event);

        if (!id) {
            return createResponse(400, { error: 'Post ID is required' });
        }

        if (!event.body) {
            return createResponse(400, { error: 'Request body is required' });
        }

        const {
            mediaId,
            flareData,
            textOverlay,
            reflexType = 'flare'
        }: CreateReflexRequest = JSON.parse(event.body);

        // Validate required fields based on reflex type
        if (reflexType === 'custom_image' && !mediaId) {
            return createResponse(400, { error: 'mediaId is required for custom_image reflexes' });
        }

        if (reflexType === 'flare' && !flareData && !textOverlay) {
            return createResponse(400, { error: 'flareData or textOverlay is required for flare reflexes' });
        }

        // Verify post exists
        const postCommand = new GetCommand({
            TableName: POSTS_TABLE,
            Key: { id }
        });

        const postResult = await dynamodb.send(postCommand);

        if (!postResult.Item) {
            return createResponse(404, { error: 'Post not found' });
        }

        // Verify media exists if mediaId provided and store media data
        let mediaData = null;
        if (mediaId) {
            const mediaCommand = new GetCommand({
                TableName: MEDIA_TABLE,
                Key: { id: mediaId }
            });

            const mediaResult = await dynamodb.send(mediaCommand);

            if (!mediaResult.Item) {
                return createResponse(404, { error: 'Media not found' });
            }

            mediaData = mediaResult.Item;

            // Use final_url as fallback if url is empty (for backward compatibility)
            if (!mediaData.url && mediaData.final_url) {
                mediaData.url = mediaData.final_url;
            }
        }

        const reflexId = uuidv4();
        const reflex: Reflex = {
            id: reflexId,
            postId: id,
            userId: userId,
            mediaId: mediaId || undefined,
            flareData: flareData || undefined,
            textOverlay: textOverlay || undefined,
            reflexType,
            likes: 0,
            isActive: true,
            createdAt: new Date().toISOString(),
            updatedAt: new Date().toISOString()
        };

        const putCommand = new PutCommand({
            TableName: REFLEXES_TABLE,
            Item: reflex
        });

        await dynamodb.send(putCommand);

        // Update post reflex count
        const updateCommand = new UpdateCommand({
            TableName: POSTS_TABLE,
            Key: { id },
            UpdateExpression: 'ADD reflexes :inc',
            ExpressionAttributeValues: { ':inc': 1 }
        });

        await dynamodb.send(updateCommand);

        // Fetch user data for the response
        try {
            const userCommand = new GetCommand({
                TableName: USERS_TABLE,
                Key: { id: userId }
            });

            const userResult = await dynamodb.send(userCommand);

            if (userResult.Item) {
                const user = userResult.Item as User;
                reflex.username = user.username;
                reflex.displayName = user.displayName;
                reflex.avatarUrl = user.avatarUrl;
            }
        } catch (error) {
            console.error('Error fetching user data for response:', error);
            // Continue without user data in response
        }

        // Add media data to the response if available
        if (mediaData) {
            (reflex as any).media = mediaData;
        }

        return createResponse(201, {
            message: 'Reflex created successfully',
            reflex
        });

    } catch (error) {
        console.error('CreateReflex error:', error);
        const errorMessage = error instanceof Error ? error.message : 'Unknown error';
        return createResponse(500, { error: 'Failed to create reflex', details: errorMessage });
    }
};

// Delete a reflex
const deleteReflex = async (event: APIGatewayProxyEvent): Promise<APIGatewayProxyResult> => {
    try {
        const { id } = event.pathParameters || {};
        const userId = getUserIdFromEvent(event);

        if (!id) {
            return createResponse(400, { error: 'Reflex ID is required' });
        }

        // Get the reflex to verify ownership
        const getCommand = new GetCommand({
            TableName: REFLEXES_TABLE,
            Key: { id }
        });

        const reflexResult = await dynamodb.send(getCommand);

        if (!reflexResult.Item) {
            return createResponse(404, { error: 'Reflex not found' });
        }

        const reflex = reflexResult.Item as Reflex;

        // Check if user owns the reflex
        if (reflex.userId !== userId) {
            return createResponse(403, { error: 'Not authorized to delete this reflex' });
        }

        const postId = reflex.postId;

        // Delete the reflex
        const deleteCommand = new DeleteCommand({
            TableName: REFLEXES_TABLE,
            Key: { id }
        });

        await dynamodb.send(deleteCommand);

        // Update post reflex count
        const updateCommand = new UpdateCommand({
            TableName: POSTS_TABLE,
            Key: { id: postId },
            UpdateExpression: 'ADD reflexes :dec',
            ExpressionAttributeValues: { ':dec': -1 }
        });

        await dynamodb.send(updateCommand);

        return createResponse(200, { message: 'Reflex deleted successfully' });

    } catch (error) {
        console.error('DeleteReflex error:', error);
        const errorMessage = error instanceof Error ? error.message : 'Unknown error';
        return createResponse(500, { error: 'Failed to delete reflex', details: errorMessage });
    }
};

// Like reflex
const likeReflex = async (event: APIGatewayProxyEvent): Promise<APIGatewayProxyResult> => {
    try {
        const { id } = event.pathParameters || {};

        if (!id) {
            return createResponse(400, { error: 'Reflex ID is required' });
        }

        // Get user ID from authorizer context
        const userId = getUserIdFromEvent(event);

        if (!userId) {
            return createResponse(401, { error: 'User not authenticated' });
        }

        // Check if already liked
        const getLikeCommand = new GetCommand({
            TableName: REFLEX_LIKES_TABLE,
            Key: { reflexId: id, userId: userId }
        });
        const existingLike = await dynamodb.send(getLikeCommand);

        if (existingLike.Item) {
            return createResponse(400, { error: 'Reflex already liked' });
        }

        // Verify the reflex exists
        const getReflexCommand = new GetCommand({
            TableName: REFLEXES_TABLE,
            Key: { id }
        });
        const reflexResult = await dynamodb.send(getReflexCommand);

        if (!reflexResult.Item) {
            return createResponse(404, { error: 'Reflex not found' });
        }

        // Add like
        const putLikeCommand = new PutCommand({
            TableName: REFLEX_LIKES_TABLE,
            Item: {
                reflexId: id,
                userId: userId,
                createdAt: new Date().toISOString()
            }
        });
        await dynamodb.send(putLikeCommand);

        // Update reflex likes count
        const updateReflexCommand = new UpdateCommand({
            TableName: REFLEXES_TABLE,
            Key: { id },
            UpdateExpression: 'ADD likes :inc',
            ExpressionAttributeValues: { ':inc': 1 }
        });
        await dynamodb.send(updateReflexCommand);

        return createResponse(200, { message: 'Reflex liked successfully' });

    } catch (error) {
        console.error('LikeReflex error:', error);
        return createResponse(500, { error: 'Failed to like reflex', details: (error as Error).message });
    }
};

// Unlike reflex
const unlikeReflex = async (event: APIGatewayProxyEvent): Promise<APIGatewayProxyResult> => {
    try {
        const { id } = event.pathParameters || {};

        if (!id) {
            return createResponse(400, { error: 'Reflex ID is required' });
        }

        // Get user ID from authorizer context
        const userId = getUserIdFromEvent(event);

        if (!userId) {
            return createResponse(401, { error: 'User not authenticated' });
        }

        // Remove like
        const deleteLikeCommand = new DeleteCommand({
            TableName: REFLEX_LIKES_TABLE,
            Key: { reflexId: id, userId: userId }
        });
        await dynamodb.send(deleteLikeCommand);

        // Update reflex likes count
        const updateReflexCommand = new UpdateCommand({
            TableName: REFLEXES_TABLE,
            Key: { id },
            UpdateExpression: 'ADD likes :dec',
            ExpressionAttributeValues: { ':dec': -1 }
        });
        await dynamodb.send(updateReflexCommand);

        return createResponse(200, { message: 'Reflex unliked successfully' });

    } catch (error) {
        console.error('UnlikeReflex error:', error);
        return createResponse(500, { error: 'Failed to unlike reflex', details: (error as Error).message });
    }
};

// Curated emoji whitelist - basic reactions, extended reactions, and emotions only
const ALLOWED_EMOJIS = new Set([
    // Basic reactions
    '👍', '👎', '❤️', '😂', '😮', '😢', '😡', '🔥',
    // Extended reactions
    '👏', '🎉', '💯', '✨', '⚡', '💪', '🙌', '👀',
    // Emotions
    '🤔', '😍', '🥰', '😊', '😎', '🤩', '😴', '🤯',
    '😭', '🥺', '😤', '🤗', '😱', '🤪', '😇', '🙄',
]);

const validateEmoji = (emoji: string): boolean => {
    if (!emoji || typeof emoji !== 'string') {
        return false;
    }

    // Check if emoji is in whitelist
    if (!ALLOWED_EMOJIS.has(emoji)) {
        console.log(`Invalid emoji attempted: ${emoji}`);
        return false;
    }

    return true;
};

// Enhanced statistics tracking for reflexes
const updateReflexReactionStatistics = async (reflexId: string, emoji: string, userId: string, action: 'add' | 'remove') => {
    const timestamp = new Date().toISOString();
    const statKey = `${reflexId}#${emoji}#${action}`;

    try {
        // Log reaction event for analytics
        const statsCommand = new PutCommand({
            TableName: process.env.REFLEX_REACTION_STATS_TABLE || REFLEX_REACTIONS_TABLE + '_stats',
            Item: {
                statKey,
                reflexId,
                emoji,
                userId,
                action,
                timestamp,
                date: timestamp.split('T')[0], // For daily aggregation
                hour: new Date().getHours(), // For hourly trends
            },
        });

        await dynamodb.send(statsCommand);
        console.log(`Logged reflex reaction stat: ${action} ${emoji} on reflex ${reflexId} by user ${userId}`);
    } catch (error) {
        console.error('Failed to log reflex reaction statistics:', error);
        // Don't fail the main operation if stats logging fails
    }
};

// Add emoji reaction to reflex
const reactToReflex = async (event: APIGatewayProxyEvent): Promise<APIGatewayProxyResult> => {
    try {
        const { id } = event.pathParameters || {};
        if (!id) return createResponse(400, { error: 'Reflex ID is required' });
        const userId = getUserIdFromEvent(event);
        if (!userId) return createResponse(401, { error: 'User not authenticated' });
        if (!event.body) return createResponse(400, { error: 'Request body is required' });
        const { emoji } = JSON.parse(event.body || '{}');
        if (!emoji || typeof emoji !== 'string') return createResponse(400, { error: 'emoji is required' });

        // Validate emoji against whitelist
        if (!validateEmoji(emoji)) {
            return createResponse(400, {
                error: 'Invalid emoji',
                message: 'Only whitelisted emojis are allowed',
                allowedEmojis: Array.from(ALLOWED_EMOJIS).slice(0, 10) // Show first 10 as examples
            });
        }

        // ENFORCE SINGLE REACTION RULE: First, remove any existing reactions by this user for this reflex
        console.log(`Enforcing single reaction rule for user ${userId} on reflex ${id}`);

        // Query all reactions by this user for this reflex
        const queryExisting = new QueryCommand({
            TableName: REFLEX_REACTIONS_TABLE,
            KeyConditionExpression: 'reflexId = :reflexId',
            FilterExpression: 'userId = :userId',
            ExpressionAttributeValues: {
                ':reflexId': id,
                ':userId': userId,
            },
        });

        const existingReactions = await dynamodb.send(queryExisting);
        console.log(`Found ${existingReactions.Items?.length || 0} existing reactions by user ${userId} for reflex ${id}`);

        // Delete all existing reactions by this user
        if (existingReactions.Items && existingReactions.Items.length > 0) {
            for (const reaction of existingReactions.Items) {
                const deleteCommand = new DeleteCommand({
                    TableName: REFLEX_REACTIONS_TABLE,
                    Key: {
                        reflexId: id,
                        reactionKey: reaction.reactionKey,
                    },
                });
                await dynamodb.send(deleteCommand);
                console.log(`Deleted existing reaction: ${reaction.emoji} by user ${userId}`);

                // Track removal statistics
                await updateReflexReactionStatistics(id, reaction.emoji, userId, 'remove');
            }
        }

        // Now add the new reaction
        const reactionKey = `${emoji}#${userId}`;
        const put = new PutCommand({
            TableName: REFLEX_REACTIONS_TABLE,
            Item: {
                reflexId: id,
                reactionKey,
                emoji,
                userId,
                createdAt: new Date().toISOString(),
            },
        });
        await dynamodb.send(put);
        console.log(`Added new reaction: ${emoji} by user ${userId} for reflex ${id}`);

        // Track addition statistics
        await updateReflexReactionStatistics(id, emoji, userId, 'add');

        return createResponse(200, {
            message: 'Reaction added',
            emoji,
            timestamp: new Date().toISOString()
        });
    } catch (error) {
        console.error('ReactToReflex error:', error);
        return createResponse(500, { error: 'Failed to react to reflex', details: (error as Error).message });
    }
};

// Remove emoji reaction from reflex
const unreactToReflex = async (event: APIGatewayProxyEvent): Promise<APIGatewayProxyResult> => {
    try {
        const { id } = event.pathParameters || {};
        if (!id) return createResponse(400, { error: 'Reflex ID is required' });
        const userId = getUserIdFromEvent(event);
        if (!userId) return createResponse(401, { error: 'User not authenticated' });
        const emoji = (event.queryStringParameters || {}).emoji;
        if (!emoji) return createResponse(400, { error: 'emoji query param is required' });

        // Validate emoji against whitelist
        if (!validateEmoji(emoji)) {
            return createResponse(400, {
                error: 'Invalid emoji',
                message: 'Only whitelisted emojis are allowed'
            });
        }

        const reactionKey = `${emoji}#${userId}`;
        const del = new DeleteCommand({
            TableName: REFLEX_REACTIONS_TABLE,
            Key: { reflexId: id, reactionKey },
        });
        await dynamodb.send(del);

        // Track removal statistics
        await updateReflexReactionStatistics(id, emoji, userId, 'remove');

        return createResponse(200, {
            message: 'Reaction removed',
            emoji,
            timestamp: new Date().toISOString()
        });
    } catch (error) {
        console.error('UnreactToReflex error:', error);
        return createResponse(500, { error: 'Failed to remove reaction', details: (error as Error).message });
    }
};


// Main handler
export const handler = async (event: APIGatewayProxyEvent, context: Context): Promise<APIGatewayProxyResult> => {
    console.log('Event:', JSON.stringify(event, null, 2));

    const { httpMethod, path, pathParameters } = event;

    try {
        // Handle CORS preflight
        if (httpMethod === 'OPTIONS') {
            return createResponse(200, { message: 'CORS preflight' });
        }

        // Route requests
        if (httpMethod === 'GET' && path.includes('/posts/') && path.includes('/reflexes')) {
            return await getReflexes(event);
        } else if (httpMethod === 'POST' && path.includes('/posts/') && path.includes('/reflexes')) {
            return await createReflex(event);
        } else if (httpMethod === 'POST' && path.includes('/reflexes/') && path.includes('/like')) {
            return await likeReflex(event);
        } else if (httpMethod === 'DELETE' && path.includes('/reflexes/') && path.includes('/like')) {
            return await unlikeReflex(event);
        } else if (httpMethod === 'POST' && path.includes('/reflexes/') && path.includes('/reactions')) {
            return await reactToReflex(event);
        } else if (httpMethod === 'DELETE' && path.includes('/reflexes/') && path.includes('/reactions')) {
            return await unreactToReflex(event);
        } else if (httpMethod === 'DELETE' && path.includes('/reflexes/')) {
            return await deleteReflex(event);
        } else {
            return createResponse(404, { error: 'Route not found' });
        }

    } catch (error) {
        console.error('Handler error:', error);
        const errorMessage = error instanceof Error ? error.message : 'Unknown error';
        return createResponse(500, { error: 'Internal server error', details: errorMessage });
    }
};
