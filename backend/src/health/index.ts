import { DynamoDBClient } from '@aws-sdk/client-dynamodb';
import { DynamoDBDocumentClient, ScanCommand } from '@aws-sdk/lib-dynamodb';
import { APIGatewayProxyEvent, APIGatewayProxyResult } from 'aws-lambda';

// Configure AWS SDK v3 client
const dynamoClient = new DynamoDBClient({
    region: process.env.AWS_REGION || 'us-west-2'
});

const dynamodb = DynamoDBDocumentClient.from(dynamoClient);

// TypeScript interfaces
interface HealthResponse {
    status: 'healthy' | 'unhealthy';
    timestamp: string;
    environment: string;
    version: string;
    services: {
        database: 'healthy' | 'unhealthy' | 'skipped';
        api: 'healthy';
    };
    uptime: number;
    memory: NodeJS.MemoryUsage;
    environment_variables: {
        USER_POOL_ID?: string;
        USER_POOL_CLIENT_ID?: string;
        USERS_TABLE?: string;
        POSTS_TABLE?: string;
        MEDIA_BUCKET?: string;
    };
}

interface ErrorResponse {
    status: 'unhealthy';
    timestamp: string;
    error: string;
}

// Helper function to create response
const createResponse = (statusCode: number, body: HealthResponse | ErrorResponse | { error: string }): APIGatewayProxyResult => ({
    statusCode,
    headers: {
        'Content-Type': 'application/json',
        'Access-Control-Allow-Origin': '*',
        'Access-Control-Allow-Headers': 'Content-Type,X-Amz-Date,Authorization,X-Api-Key,X-Amz-Security-Token',
        'Access-Control-Allow-Methods': 'GET,POST,PUT,DELETE,OPTIONS'
    },
    body: JSON.stringify(body)
});

// Health check function
const healthCheck = async (): Promise<APIGatewayProxyResult> => {
    try {
        const timestamp = new Date().toISOString();
        const environment = process.env.ENVIRONMENT || 'development';

        // Test DynamoDB connection (skip for local development)
        let dbStatus: 'healthy' | 'unhealthy' | 'skipped' = 'skipped';
        if (process.env.AWS_ACCESS_KEY_ID && process.env.AWS_SECRET_ACCESS_KEY) {
            try {
                const scanCommand = new ScanCommand({
                    TableName: process.env.USERS_TABLE || 'gameflex-development-Users',
                    Limit: 1
                });
                await dynamodb.send(scanCommand);
                dbStatus = 'healthy';
            } catch (error) {
                dbStatus = 'unhealthy';
                console.error('DynamoDB health check failed:', error);
            }
        }

        const healthData: HealthResponse = {
            status: 'healthy',
            timestamp,
            environment,
            version: '1.0.0',
            services: {
                database: dbStatus,
                api: 'healthy'
            },
            uptime: process.uptime(),
            memory: process.memoryUsage(),
            environment_variables: {
                USER_POOL_ID: process.env.USER_POOL_ID,
                USER_POOL_CLIENT_ID: process.env.USER_POOL_CLIENT_ID,
                USERS_TABLE: process.env.USERS_TABLE,
                POSTS_TABLE: process.env.POSTS_TABLE,
                MEDIA_BUCKET: process.env.MEDIA_BUCKET
            }
        };

        return createResponse(200, healthData);

    } catch (error) {
        console.error('Health check error:', error);
        const errorResponse: ErrorResponse = {
            status: 'unhealthy',
            timestamp: new Date().toISOString(),
            error: error instanceof Error ? error.message : 'Unknown error'
        };
        return createResponse(500, errorResponse);
    }
};

// Main handler
export const handler = async (event: APIGatewayProxyEvent): Promise<APIGatewayProxyResult> => {
    console.log('Health check event:', JSON.stringify(event, null, 2));

    const { httpMethod, path } = event;

    if (httpMethod === 'GET' && path === '/health') {
        return await healthCheck();
    } else {
        return createResponse(404, { error: 'Not found' });
    }
};
