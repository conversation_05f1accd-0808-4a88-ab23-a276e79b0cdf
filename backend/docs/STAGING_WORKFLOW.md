# Media Staging Workflow

This document describes the secure media staging workflow that ensures no inappropriate content becomes publicly accessible.

## Overview

The staging workflow implements a two-phase approach:
1. **Staging Phase**: Files are uploaded to a private staging area
2. **Approval Phase**: After AI moderation, approved files are moved to public area

## Architecture

```
Upload → Staging Area → AI Processing → Approved? → Public Area
                                    ↓
                                   No → Review Area → Manual Review → Approve? → Public Area
                                                                        ↓
                                                                       No → Keep in Review
```

### Security Benefits

- **Zero Public Exposure**: Inappropriate content never becomes publicly accessible
- **Automatic Cleanup**: Rejected content is automatically moved to review area
- **CloudFront Protection**: CDN only serves approved content
- **Lifecycle Management**: Staging area has automatic cleanup after 24 hours

## Workflow Steps

### 1. Upload Request
```
POST /media/upload
```

**Response includes:**
- Upload URL pointing to staging area (`staging/media/{userId}/{mediaId}.ext`)
- Media record with `status: 'pending_upload'`
- Empty `url` field (no public access yet)

### 2. File Upload
Client uploads directly to S3 staging area using presigned URL.

### 3. S3 Event Trigger
S3 `ObjectCreated` event triggers AI processing Lambda (only for `staging/` prefix).

### 4. AI Processing
Lambda function:
1. Downloads file from staging
2. Runs AWS Rekognition moderation analysis
3. Runs content analysis and tagging

### 5. Decision Point

#### If Content is Approved ✅
1. Copy file from staging to final location
2. Delete file from staging
3. Update media record:
   - `status: 'completed'`
   - `s3Key: final_s3_key`
   - `url: public_cloudfront_url`
4. Store analysis results

#### If Content is Rejected ❌
1. Move file from staging to review area for internal review
2. Update media record:
   - `status: 'rejected_inappropriate'`
   - `rejection_reason: detailed_reason`
   - `review_s3_key: review_location`
   - `review_type: 'inappropriate'`
   - `review_date: current_date`
3. Store moderation results
4. Content available for admin review and potential appeal

## S3 Folder Structure

```
bucket/
├── staging/                    # Private staging area
│   ├── media/{userId}/{id}.ext
│   ├── avatars/{userId}/{id}.ext
│   ├── videos/{userId}/{id}.ext
│   └── reflexes/{userId}/{id}.ext
├── review/                     # Internal review area
│   ├── inappropriate/
│   │   └── {date}/{mediaId}/filename.ext
│   └── failed/
│       └── {date}/{mediaId}/filename.ext
├── media/                      # Public approved content
│   └── {userId}/{id}.ext
├── avatars/                    # Public approved avatars
│   └── {userId}/{id}.ext
├── videos/                     # Public approved videos
│   └── {userId}/{id}.ext
├── reflexes/                   # Public approved reflexes
│   └── {userId}/{id}.ext
└── metadata/                   # Analysis results
    └── analysis/{id}.json
```

## Database Schema

### Media Record Fields

```typescript
interface MediaRecord {
  // ... existing fields
  s3Key: string;              // Current location (staging initially, final after approval)
  url: string;                // Public URL (empty until approved)
  staging_s3_key?: string;    // Staging location
  final_s3_key?: string;      // Final public location
  final_url?: string;         // Final public URL
  review_s3_key?: string;     // Review location (if rejected)
  review_type?: string;       // Type of review needed
  review_date?: string;       // Date moved to review
  status: 'pending_upload' | 'uploaded' | 'processing' | 'completed' | 'failed' | 'rejected_inappropriate';
}
```

### Status Flow

```
pending_upload → uploaded → processing → completed ✅
                                      → rejected_inappropriate ❌ → review → approved ✅
                                      → failed ⚠️
```

## API Behavior

### GET /media/{id}

**Status-based responses:**

- `pending_upload` / `uploaded` / `processing`:
  ```json
  {
    "media": {
      "status": "processing",
      "url": "",
      "downloadUrl": null,
      "message": "Content is being processed and will be available shortly"
    }
  }
  ```

- `completed`:
  ```json
  {
    "media": {
      "status": "completed",
      "url": "https://cdn.example.com/media/user123/file.jpg",
      "downloadUrl": "https://cdn.example.com/media/user123/file.jpg"
    }
  }
  ```

- `rejected_inappropriate`:
  ```json
  {
    "media": {
      "status": "rejected_inappropriate",
      "url": "",
      "downloadUrl": null,
      "message": "Content was rejected due to inappropriate content"
    }
  }
  ```

### GET /media/{id}/analysis

Returns detailed moderation and content analysis results.

### GET /media/{id}/review (Admin Only)

Returns content for internal review with presigned URLs.

### POST /media/{id}/approve (Admin Only)

Approves rejected content and moves it to public area.

## CloudFront Configuration

### Allowed Paths
- `/media/*` - Approved content
- `/avatars/*` - Approved avatars
- `/videos/*` - Approved videos
- `/reflexes/*` - Approved reflexes
- `/thumbnails/*` - Generated thumbnails

### Blocked Paths
- `/staging/*` - No public access to staging area
- `/review/*` - No public access to review area

## Lifecycle Management

### S3 Lifecycle Rules

1. **Staging Cleanup**: Files in `staging/` deleted after 1 day
2. **Review Transition**: Files in `review/` → Glacier after 90 days
3. **Glacier Transition**: Approved content → Glacier after 30 days
4. **Deep Archive**: Production content → Deep Archive after 365 days

### Automatic Cleanup

- **Approved Content**: Moved to public area, staging cleaned
- **Rejected Content**: Moved to review area, staging cleaned
- **Orphaned Staging Files**: Cleaned up after 24 hours
- **Processing Metadata**: Cleaned up after 7 days

## Error Handling

### Processing Failures
- Content remains in staging
- Status set to `failed`
- Manual review/retry possible

### Network Issues
- Staging files have 24-hour cleanup
- Client can retry upload with new presigned URL

### Rekognition Failures
- Content allowed by default (fail-safe approach)
- Error logged for manual review

## Monitoring

### Key Metrics
- Processing time per file
- Rejection rate by content type
- Staging area utilization
- Failed processing rate
- Review queue size

### CloudWatch Alarms
- High rejection rate (>10%)
- Processing failures (>5%)
- Staging area size growth
- Lambda timeout errors
- Review queue growth

## Security Considerations

### Access Control
- Staging area: Private (no public access)
- Review area: Admin access only
- Final area: Public via CloudFront only
- Lambda: Read/write access to all areas

### Data Protection
- All S3 buckets encrypted at rest
- Presigned URLs expire in 10 minutes (upload) / 1 hour (review)
- CloudFront enforces HTTPS
- Admin access logged

### Compliance
- Automatic inappropriate content blocking
- Audit trail of all moderation decisions
- Configurable moderation thresholds
- Appeal process for users

## Testing

### Test Scenarios
1. Upload appropriate content → Should become public
2. Upload inappropriate content → Should be moved to review
3. Admin approval → Should move to public
4. Processing failure → Should remain in staging for retry
5. Network interruption → Should cleanup automatically

### Validation
```bash
# Check staging area is empty after processing
aws s3 ls s3://bucket/staging/ --recursive

# Verify public content is accessible
curl -I https://cdn.example.com/media/user123/file.jpg

# Verify staging/review is not accessible
curl -I https://cdn.example.com/staging/media/user123/file.jpg
# Should return 403 or 404
```

## Migration from Previous System

For existing media without staging workflow:
1. Existing files remain in current locations
2. New uploads use staging workflow
3. Gradual migration possible via background job
