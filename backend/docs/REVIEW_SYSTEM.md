# Internal Review System for Rejected Content

This document describes the internal review system that allows administrators to review and potentially approve content that was automatically rejected by AI moderation.

## Overview

When content is flagged as inappropriate by AWS Rekognition, instead of being deleted, it's moved to a secure review area where internal moderators can:

1. **Review the content** and AI analysis results
2. **Appeal decisions** if the AI was incorrect
3. **Approve content** that was wrongly rejected
4. **Maintain audit trails** of all decisions

## Architecture

```
Upload → Staging → AI Analysis → Inappropriate? → Review Area → Manual Review → Approve/Reject
                                      ↓
                                    Appropriate → Public Area
```

### Review Area Structure

```
bucket/
├── review/
│   ├── inappropriate/
│   │   └── 2024-01-15/          # Organized by date
│   │       └── {mediaId}/
│   │           └── filename.ext
│   └── failed/                  # Processing failures
│       └── 2024-01-15/
│           └── {mediaId}/
│               └── filename.ext
```

## Database Schema

### Review Fields in Media Table

```typescript
interface MediaRecord {
  // ... existing fields
  
  // Review workflow fields
  review_s3_key?: string;        // Location in review area
  review_type?: 'inappropriate' | 'failed';  // Type of review needed
  review_date?: string;          // Date moved to review (YYYY-MM-DD)
  
  // Admin approval fields
  approval_reason?: string;      // Reason for manual approval
  approved_by?: string;          // Admin user ID who approved
  approved_at?: string;          // Timestamp of manual approval
}
```

## API Endpoints

### 1. Get Content for Review (Admin Only)

```http
GET /media/{id}/review
Headers:
  x-admin-access: true
  Authorization: Bearer {admin_token}
```

**Response:**
```json
{
  "reviewData": {
    "mediaId": "uuid",
    "userId": "user123",
    "fileName": "image.jpg",
    "fileType": "image/jpeg",
    "status": "rejected_inappropriate",
    "reviewType": "inappropriate",
    "reviewDate": "2024-01-15",
    "reviewUrl": "https://presigned-url-to-content",
    "rejectionReason": "Content flagged as inappropriate: Contains: Suggestive (confidence: 85%)",
    "moderationAnalysis": {
      "isInappropriate": true,
      "confidence": 85,
      "moderationLabels": [...],
      "summary": "Contains: Suggestive"
    },
    "contentAnalysis": {
      "labels": [...],
      "suggestedTags": [...]
    },
    "processingTimeMs": 1250,
    "createdAt": "2024-01-15T10:30:00Z"
  }
}
```

### 2. Approve Rejected Content (Admin Only)

```http
POST /media/{id}/approve
Headers:
  x-admin-access: true
  Authorization: Bearer {admin_token}
Content-Type: application/json

{
  "approvalReason": "Content review determined this is appropriate gaming content, not suggestive material"
}
```

**Response:**
```json
{
  "message": "Media approved and made public",
  "mediaId": "uuid",
  "status": "approved",
  "publicUrl": "https://cdn.example.com/media/user123/file.jpg",
  "approvalReason": "Content review determined this is appropriate..."
}
```

## Review Workflow

### 1. Content Rejection
When AI flags content as inappropriate:
1. Content moved from `staging/` to `review/inappropriate/{date}/{mediaId}/`
2. Media status set to `rejected_inappropriate`
3. S3 object tagged with metadata for tracking
4. DynamoDB record updated with review information

### 2. Internal Review Process
Administrators can:
1. Access review queue via admin dashboard
2. View content using presigned URLs (1-hour expiration)
3. Review AI analysis and confidence scores
4. Make approval/rejection decisions with reasons

### 3. Appeal Process
If content is approved after review:
1. Content moved from review area to public area
2. Media status changed to `completed`
3. Public URL becomes available
4. Approval audit trail recorded

## Security Considerations

### Access Control
- **Review Area**: Private S3 bucket, admin access only
- **Presigned URLs**: 1-hour expiration for security
- **Admin Authentication**: Role-based access control
- **Audit Logging**: All decisions logged with admin ID

### Data Protection
- Review content encrypted at rest
- Presigned URLs use HTTPS only
- Admin access logged in CloudTrail
- No public access to review area via CloudFront

## Storage Lifecycle

### Review Area Lifecycle
- **Active Review**: Content remains in review area
- **90 Days**: Transition to Glacier for cost optimization
- **Long-term**: Keep for compliance and appeal purposes
- **Manual Cleanup**: Admin tools can remove old content

### Cost Optimization
- Glacier transition after 90 days reduces storage costs
- Presigned URLs avoid data transfer costs
- Efficient tagging for automated lifecycle management

## Monitoring & Analytics

### Key Metrics
- **Review Queue Size**: Number of items awaiting review
- **Review Response Time**: Time from rejection to decision
- **Approval Rate**: Percentage of appeals approved
- **False Positive Rate**: AI incorrect rejections

### CloudWatch Dashboards
- Review queue depth over time
- Admin activity and response times
- Appeal success rates by content type
- Storage costs for review area

## Admin Tools Integration

### Required Features for Admin Dashboard
1. **Review Queue**: List of content awaiting review
2. **Content Viewer**: Secure display of flagged content
3. **Decision Interface**: Approve/reject with reason
4. **Search & Filter**: Find specific content or users
5. **Audit Trail**: History of all decisions

### API Integration Examples

```typescript
// Get review queue
const reviewQueue = await fetch('/admin/review-queue', {
  headers: { 'x-admin-access': 'true' }
});

// Review specific content
const contentReview = await fetch(`/media/${mediaId}/review`, {
  headers: { 'x-admin-access': 'true' }
});

// Approve content
const approval = await fetch(`/media/${mediaId}/approve`, {
  method: 'POST',
  headers: { 'x-admin-access': 'true' },
  body: JSON.stringify({ approvalReason: 'Manual review approved' })
});
```

## Compliance & Legal

### Data Retention
- Review content retained for compliance purposes
- Audit trail maintained for legal requirements
- User appeal rights documented
- GDPR compliance for deletion requests

### Appeal Process
1. **User Notification**: Users informed of rejection
2. **Appeal Submission**: Users can request review
3. **Internal Review**: Admin team reviews appeal
4. **Decision Communication**: Results communicated to user
5. **Audit Documentation**: All steps logged

## Testing & Validation

### Test Scenarios
1. Content correctly flagged as inappropriate
2. Content incorrectly flagged (false positive)
3. Admin approval workflow
4. Storage lifecycle transitions
5. Access control enforcement

### Validation Checklist
- [ ] Review area is private and secure
- [ ] Presigned URLs expire correctly
- [ ] Admin authentication works
- [ ] Approval workflow functions
- [ ] Audit trail is complete
- [ ] Storage lifecycle rules active

## Future Enhancements

### Planned Features
1. **Batch Review Tools**: Review multiple items at once
2. **ML Feedback Loop**: Improve AI with manual decisions
3. **User Appeal Interface**: Self-service appeal system
4. **Advanced Analytics**: Detailed reporting and insights
5. **Automated Workflows**: Rules-based auto-approval

### Integration Opportunities
1. **Support Ticket System**: Link appeals to support tickets
2. **User Communication**: Automated notifications
3. **Content Categorization**: Improved tagging and organization
4. **Performance Metrics**: KPIs for moderation effectiveness
