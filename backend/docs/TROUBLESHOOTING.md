# GameFlex Backend Troubleshooting Guide

This guide helps resolve common issues with the GameFlex backend setup and seeding.

## Quick Diagnostics

### 1. Test Environment Loading
```bash
# Test development environment
./test-env-loading.sh

# Test staging environment  
./test-env-loading.sh staging
```

### 2. Debug AWS Connection
```bash
./debug-aws.sh
```

## Common Issues

### ❌ "Failed to load environment configuration"

**Cause**: Missing or incorrect .env files

**Solution**:
```bash
# 1. Check if .env files exist
ls -la .env*

# 2. Create from examples if missing
cp .env.example .env
cp .env.staging.example .env.staging

# 3. Edit with your actual credentials
nano .env
nano .env.staging
```

**Required variables in .env**:
- `R2_ACCOUNT_ID`
- `R2_ACCESS_KEY_ID` 
- `R2_SECRET_ACCESS_KEY`
- `R2_ENDPOINT`
- `R2_BUCKET_NAME`
- `R2_PUBLIC_URL`
- `CLOUDFLARE_API_TOKEN`

### ❌ "Cannot connect to AWS"

**Cause**: AWS credentials not configured or incorrect region

**Solution**:
```bash
# 1. Check AWS configuration
aws configure list

# 2. Configure AWS credentials
aws configure
# Enter your AWS Access Key ID, Secret, and region (us-west-2)

# 3. Test connection
aws sts get-caller-identity

# 4. Alternative: Use environment variables
export AWS_ACCESS_KEY_ID=your_key
export AWS_SECRET_ACCESS_KEY=your_secret  
export AWS_DEFAULT_REGION=us-west-2
```

### ❌ "R2 secret does not exist"

**Cause**: SAM stack not deployed yet

**Solution**:
```bash
# 1. Deploy SAM stack first
sam build
sam deploy

# 2. Then run seeding
./scripts/seed-data.sh
```

### ❌ "samconfig.toml not found"

**Cause**: Missing samconfig.toml file

**Solution**:
```bash
# 1. Copy from example
cp samconfig.toml.example samconfig.toml

# 2. No need to edit - secrets are now in .env files
```

### ❌ "Failed to update secret"

**Cause**: Insufficient AWS permissions or wrong region

**Solution**:
```bash
# 1. Check your AWS permissions
aws iam get-user

# 2. Ensure you have SecretsManager permissions
# Your AWS user/role needs:
# - secretsmanager:GetSecretValue
# - secretsmanager:PutSecretValue
# - secretsmanager:DescribeSecret

# 3. Check region matches
echo $AWS_REGION  # Should be us-west-2
```

## Environment-Specific Issues

### Development Environment

**Issue**: Local development not working
```bash
# 1. Ensure .env file exists with secrets
ls -la .env

# 2. Test environment loading
./test-env-loading.sh development

# 3. Deploy and seed
sam build && sam deploy
./scripts/seed-data.sh
```

### Staging Environment

**Issue**: Staging deployment fails
```bash
# 1. Ensure .env.staging exists
ls -la .env.staging

# 2. Set certificate ARN
export CERTIFICATE_ARN="arn:aws:acm:us-west-2:account:certificate/cert-id"

# 3. Deploy staging
./deploy-staging.sh

# 4. Seed staging data
./seed-staging.sh
```

## File Structure Check

Your backend directory should have:
```
backend/
├── .env                    # Development secrets (gitignored)
├── .env.staging           # Staging secrets (gitignored)  
├── .env.example           # Development template (committed)
├── .env.staging.example   # Staging template (committed)
├── samconfig.toml         # Deployment config (gitignored)
├── samconfig.toml.example # Config template (committed)
├── scripts/
│   ├── seed-data.sh       # Main seeding script
│   └── load-sam-env.sh    # Environment loader
├── test-env-loading.sh    # Environment test script
├── debug-aws.sh          # AWS debug script
└── seed-staging.sh       # Staging seeder
```

## Step-by-Step Setup

### First Time Setup
```bash
# 1. Copy configuration templates
cp samconfig.toml.example samconfig.toml
cp .env.example .env
cp .env.staging.example .env.staging

# 2. Edit .env files with your actual credentials
nano .env        # Add your R2 and CloudFlare credentials
nano .env.staging # Add your staging credentials

# 3. Configure AWS
aws configure    # Set region to us-west-2

# 4. Test configuration
./test-env-loading.sh
./debug-aws.sh

# 5. Deploy and seed
sam build && sam deploy
./scripts/seed-data.sh
```

### Staging Setup
```bash
# 1. Ensure staging environment configured
./test-env-loading.sh staging

# 2. Set certificate ARN for custom domain
export CERTIFICATE_ARN="your-certificate-arn"

# 3. Deploy staging
./deploy-staging.sh

# 4. Seed staging data
./seed-staging.sh
```

## Getting Help

If you're still having issues:

1. **Run diagnostics**:
   ```bash
   ./test-env-loading.sh
   ./debug-aws.sh
   ```

2. **Check logs**: Look for specific error messages in the output

3. **Verify files**: Ensure all required files exist and have correct content

4. **AWS permissions**: Verify your AWS user has the necessary permissions

5. **Region**: Ensure you're using us-west-2 region consistently
