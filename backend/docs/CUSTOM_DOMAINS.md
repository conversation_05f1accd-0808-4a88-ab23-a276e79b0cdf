# GameFlex API Custom Domains Configuration

This document describes the custom domain configuration for GameFlex API gateways across different environments.

## Domain Configuration

Each environment now has its own custom domain:

- **Development**: `dev.api.gameflex.io`
- **Staging**: `staging.api.gameflex.io`
- **Production**: `api.gameflex.io`

All domains use HTTPS with TLS 1.2 and are configured with AWS Certificate Manager.

## Infrastructure Changes

### 1. CDK Stack Updates

The `GameFlexBackendStack` now includes:

- **Custom Domain Support**: Implemented `createCustomDomain()` method that creates API Gateway custom domains
- **Certificate Integration**: Uses AWS Certificate Manager certificates for SSL/TLS
- **Base Path Mapping**: Maps custom domains to API Gateway stages
- **CloudFormation Outputs**: Provides domain targets for DNS configuration

### 2. Environment Configuration

Updated `backend/bin/cdk.ts` with domain configurations:

```typescript
const envConfig = {
  development: {
    domainName: 'dev.api.gameflex.io',
    certificateArn: 'arn:aws:acm:us-east-1:************:certificate/9c6c8946-2c19-4efa-ab52-c44a42236662',
    // ... other config
  },
  staging: {
    domainName: 'staging.api.gameflex.io',
    certificateArn: 'arn:aws:acm:us-east-1:************:certificate/9c6c8946-2c19-4efa-ab52-c44a42236662',
    // ... other config
  },
  production: {
    domainName: 'api.gameflex.io',
    certificateArn: 'arn:aws:acm:us-east-1:************:certificate/9c6c8946-2c19-4efa-ab52-c44a42236662',
    // ... other config
  }
};
```

## Frontend Configuration Updates

### 1. Environment Config

Updated `lib/config/environment_config.dart`:

```dart
// Remote API domain configuration
static const String developmentApiDomain = 'dev.api.gameflex.io';
static const String stagingApiDomain = 'staging.api.gameflex.io';
static const String productionApiDomain = 'api.gameflex.io';

// API URLs map (no /v1 needed as API Gateway stage handles routing)
static Map<String, String> get apiUrls => {
  'localhost': 'http://127.0.0.1:$localhostApiPort',
  'android_emulator': 'http://********:$localhostApiPort',
  'development': 'https://$developmentApiDomain',
  'staging': 'https://$stagingApiDomain',
  'production': 'https://$productionApiDomain',
};
```

### 2. Config Service

Updated `lib/services/config_service.dart` to include development remote option:

```dart
static const String _developmentApiUrl = 'https://dev.api.gameflex.io';
static const String _stagingApiUrl = 'https://staging.api.gameflex.io';
static const String _productionApiUrl = 'https://api.gameflex.io';
```

### 3. Mobile Platform Configuration

**iOS (Info.plist)**:
- Added custom domain entries for proper HTTPS handling
- Configured TLS 1.2 requirements for GameFlex domains

**Android (network_security_config.xml)**:
- Added GameFlex API domains to allowed HTTPS domains
- Maintained backward compatibility with AWS domains

## Deployment Process

### 1. Prerequisites

- AWS Certificate Manager certificate must exist in `us-east-1` region
- Certificate ARN: `arn:aws:acm:us-east-1:************:certificate/9c6c8946-2c19-4efa-ab52-c44a42236662`
- DNS zones for `gameflex.io` must be configured

### 2. Deploy Infrastructure

```bash
cd backend

# Deploy development environment
./deploy.sh --environment development

# Deploy staging environment  
./deploy.sh --environment staging

# Deploy production environment
./deploy.sh --environment production
```

### 3. Configure DNS

After deployment, configure DNS records in your DNS provider (CloudFlare):

1. **Get the API Gateway domain targets** from CloudFormation outputs:
   ```bash
   aws cloudformation describe-stacks \
     --stack-name gameflex-development \
     --query 'Stacks[0].Outputs[?OutputKey==`DomainNameTarget`].OutputValue' \
     --output text
   ```

2. **Create CNAME records**:
   - `dev.api.gameflex.io` → `{DomainNameTarget from development stack}`
   - `staging.api.gameflex.io` → `{DomainNameTarget from staging stack}`
   - `api.gameflex.io` → `{DomainNameTarget from production stack}`

### 4. Verify Deployment

Test each environment:

```bash
# Development
curl -I https://dev.api.gameflex.io/health

# Staging
curl -I https://staging.api.gameflex.io/health

# Production
curl -I https://api.gameflex.io/health
```

## Configuration Files Updated

### Backend
- `backend/lib/gameflex-backend-stack.ts` - Added custom domain implementation
- `backend/bin/cdk.ts` - Updated environment configurations
- `backend/.env.example` - Updated API URLs
- `backend/tests/config/test.config.ts` - Updated default test URL
- `backend/tests/.env.example` - Updated test configuration
- `backend/tests/README.md` - Updated setup instructions
- `backend/swagger.json` - Updated host configuration

### Frontend
- `lib/config/environment_config.dart` - Added domain configurations
- `lib/services/config_service.dart` - Added development remote option
- `.env.example` - Updated API URL examples

### Mobile Platforms
- `ios/Runner/Info.plist` - Added custom domain security settings
- `android/app/src/main/res/xml/network_security_config.xml` - Added domain allowlist

## Testing

### Backend Tests
Tests now default to development domain but can be configured per environment:

```bash
# Test against development
TEST_API_BASE_URL=https://dev.api.gameflex.io npm test

# Test against staging
TEST_API_BASE_URL=https://staging.api.gameflex.io npm test

# Test against production
TEST_API_BASE_URL=https://api.gameflex.io npm test
```

### Frontend Configuration
The mobile app now includes a "Development" option in the server selection dropdown that connects to the remote development environment.

## Troubleshooting

### Common Issues

1. **Certificate not found**: Ensure the certificate exists in `us-east-1` region
2. **DNS not resolving**: Check CNAME records are properly configured
3. **SSL errors**: Verify certificate covers all subdomains
4. **API Gateway errors**: Check custom domain is properly mapped to API stage

### Verification Commands

```bash
# Check DNS resolution
dig dev.api.gameflex.io CNAME
dig staging.api.gameflex.io CNAME
dig api.gameflex.io CNAME

# Check SSL certificate
openssl s_client -connect dev.api.gameflex.io:443 -servername dev.api.gameflex.io

# Test API connectivity
curl -v https://dev.api.gameflex.io/health
```
