# Policy Updates for New AWS Services

This document outlines the updates made to IAM policies to support the new AWS services added to the GameFlex backend infrastructure.

## Overview

The following AWS services were added and require updated IAM permissions:
- **Amazon S3** (Media buckets with staging/review workflow)
- **Amazon CloudFront** (CDN for media delivery)
- **Amazon Rekognition** (AI content moderation and analysis)

## Updated Policy Files

### 1. Development Environment

#### `development-policy.json`
- **Added S3 Resources**:
  - `gameflex-media-dev*` - Media storage buckets
  - `gameflex-cloudfront-logs-dev*` - CloudFront access logs
- **Enhanced CloudFront Access**: Changed from specific actions to `cloudfront:*`
- **Added Rekognition Access**: Full permissions for AI processing
- **Updated Secrets Manager**: Removed R2 config, added Apple/Xbox configs
- **Updated Deny Section**: Added media bucket patterns

#### `development-cfn-execution-policy.json`
- **Added CloudFront Access**: Full CloudFormation permissions for distributions
- **Added Rekognition Access**: Full permissions for service management
- **Updated Deny Section**: Added media bucket patterns

### 2. Staging Environment

#### `staging-policy.json`
- **Added S3 Resources**:
  - `gameflex-media-staging*` - Media storage buckets
  - `gameflex-cloudfront-logs-staging*` - CloudFront access logs
- **Enhanced CloudFront Access**: Changed from specific actions to `cloudfront:*`
- **Added Rekognition Access**: Full permissions for AI processing
- **Updated Secrets Manager**: Removed R2 config references

#### `staging-cfn-execution-policy.json`
- **Added CloudFront Access**: Full CloudFormation permissions
- **Added Rekognition Access**: Full permissions for service management

### 3. Production Environment

#### `production-policy.json`
- **Added S3 Resources**:
  - `gameflex-media-prod*` - Media storage buckets
  - `gameflex-cloudfront-logs-prod*` - CloudFront access logs
- **Enhanced CloudFront Access**: Changed from specific actions to `cloudfront:*`
- **Added Rekognition Access**: Full permissions for AI processing
- **Updated Secrets Manager**: Removed R2 config references

#### `production-cfn-execution-policy.json`
- **Added CloudFront Access**: Full CloudFormation permissions
- **Added Rekognition Access**: Full permissions for service management

## New Permissions Added

### Amazon S3
```json
{
  "Sid": "S3Access",
  "Effect": "Allow",
  "Action": "s3:*",
  "Resource": [
    "arn:aws:s3:::gameflex-media-{env}*",
    "arn:aws:s3:::gameflex-media-{env}*/*",
    "arn:aws:s3:::gameflex-cloudfront-logs-{env}*",
    "arn:aws:s3:::gameflex-cloudfront-logs-{env}*/*"
  ]
}
```

### Amazon CloudFront
```json
{
  "Sid": "CloudFrontAccess",
  "Effect": "Allow",
  "Action": "cloudfront:*",
  "Resource": "*"
}
```

### Amazon Rekognition
```json
{
  "Sid": "RekognitionAccess",
  "Effect": "Allow",
  "Action": [
    "rekognition:DetectModerationLabels",
    "rekognition:DetectLabels",
    "rekognition:DetectText",
    "rekognition:DetectFaces",
    "rekognition:RecognizeCelebrities",
    "rekognition:DetectProtectiveEquipment",
    "rekognition:GetLabelDetection",
    "rekognition:GetContentModeration"
  ],
  "Resource": "*"
}
```

## Removed Permissions

### CloudFlare R2 (No longer used)
- Removed `gameflex-r2-config-{env}*` from Secrets Manager resources
- CloudFlare R2 has been completely replaced with Amazon S3

## S3 Bucket Naming Convention

The new media buckets follow this naming pattern:
- **Development**: `gameflex-media-development`
- **Staging**: `gameflex-media-staging`
- **Production**: `gameflex-media-production`

CloudFront logs buckets:
- **Development**: `gameflex-cloudfront-logs-development`
- **Staging**: `gameflex-cloudfront-logs-staging`
- **Production**: `gameflex-cloudfront-logs-production`

## Security Considerations

### Principle of Least Privilege
- Rekognition permissions are scoped to only the actions needed for content analysis
- S3 permissions are scoped to specific bucket patterns
- CloudFront permissions are environment-specific

### Cross-Environment Protection
- Deny policies prevent access to other environments
- Bucket naming ensures environment isolation
- Secrets are environment-specific

## Deployment Impact

### Required Actions
1. **Update IAM Policies**: Apply the updated policy files to existing IAM roles
2. **Verify Permissions**: Test deployment in development environment first
3. **Monitor CloudTrail**: Watch for permission denied errors during deployment

### Rollback Plan
- Previous policy versions are maintained in git history
- Can revert to previous policies if issues arise
- CloudFlare R2 resources can be temporarily re-enabled if needed

## Testing Checklist

- [ ] CDK deployment succeeds in development
- [ ] S3 buckets are created with correct permissions
- [ ] CloudFront distributions deploy successfully
- [ ] Lambda functions can access Rekognition
- [ ] Media upload workflow functions end-to-end
- [ ] AI processing triggers correctly
- [ ] Review system works for rejected content

## Monitoring

### CloudWatch Metrics to Monitor
- S3 bucket access patterns
- CloudFront cache hit rates
- Rekognition API call volumes
- Lambda function errors related to permissions

### CloudTrail Events to Watch
- S3 access denied events
- CloudFront permission errors
- Rekognition API failures
- Secrets Manager access patterns

## Future Considerations

### Potential Optimizations
- Consider using S3 bucket policies for more granular access control
- Implement CloudFront signed URLs for sensitive content
- Add resource-based policies for cross-account access if needed

### Scaling Considerations
- Monitor Rekognition API limits and request increases if needed
- Consider regional deployment for global performance
- Plan for additional S3 buckets for different content types
