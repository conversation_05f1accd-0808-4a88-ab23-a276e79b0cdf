# AWS Rekognition Integration

This document describes the AWS Rekognition integration for GameFlex media processing, which automatically analyzes uploaded images and videos for inappropriate content and generates comprehensive tags.

## Overview

The AI processing system uses AWS Rekognition to:
1. **Content Moderation**: Detect inappropriate content (NSFW, violence, etc.)
2. **Content Analysis**: Generate detailed tags and metadata
3. **Gaming Detection**: Identify video game-related content
4. **Image Properties**: Extract color information and quality metrics

## Architecture

```
S3 Upload → S3 Event → AI Processing Lambda → Rekognition → DynamoDB
```

### Components

- **S3 Bucket**: Stores uploaded media files
- **AI Processing Lambda**: Processes images with Rekognition
- **AWS Rekognition**: Provides AI analysis services
- **DynamoDB**: Stores analysis results linked to media records

## Processing Pipeline

### 1. Content Moderation

Uses `DetectModerationLabels` to identify inappropriate content:

```typescript
interface ModerationAnalysis {
  isInappropriate: boolean;
  confidence: number;
  moderationLabels: Array<{
    name: string;
    confidence: number;
    parentName?: string;
    categories?: string[];
  }>;
  summary: string;
}
```

**Rejection Criteria**: Content with moderation labels above 80% confidence is automatically rejected.

### 2. Content Analysis & Tagging

Uses `DetectLabels` with enhanced features:

```typescript
interface ContentAnalysis {
  labels: Array<{
    name: string;
    confidence: number;
    categories?: string[];
    instances?: Array<{
      boundingBox?: BoundingBox;
      confidence: number;
    }>;
    parents?: Array<{ name: string }>;
  }>;
  videoGameRelated: boolean;
  videoGameConfidence: number;
  suggestedTags: string[];
  dominantColors?: DominantColor[];
  imageProperties?: ImageProperties;
}
```

### 3. Gaming Content Detection

Automatically identifies video game-related content using keywords:

- Gaming hardware (console, controller, gamepad)
- Gaming software (steam, xbox, playstation)
- Gaming content (character, weapon, armor)
- Gaming activities (streaming, esports)

## API Endpoints

### Upload Media
```
POST /media/upload
```
Generates S3 presigned URL for direct upload.

### Get Media Analysis
```
GET /media/{id}/analysis
```
Returns comprehensive analysis results:

```json
{
  "analysis": {
    "mediaId": "uuid",
    "status": "completed",
    "moderationAnalysis": {
      "isInappropriate": false,
      "confidence": 95,
      "moderationLabels": [...],
      "summary": "Content appears appropriate"
    },
    "contentAnalysis": {
      "labels": [...],
      "videoGameRelated": true,
      "videoGameConfidence": 90,
      "suggestedTags": ["gaming", "computer", "screen"],
      "dominantColors": [...],
      "imageProperties": {...}
    },
    "processingTimeMs": 1250
  }
}
```

## Database Schema

### Media Table Updates

New fields added to the Media table:

```typescript
interface MediaRecord {
  // ... existing fields
  moderation_analysis?: ModerationAnalysis;
  content_analysis?: ContentAnalysis;
  processing_completed_at?: string;
  processing_time_ms?: number;
  rejection_reason?: string;
  status: 'pending_upload' | 'uploaded' | 'processing' | 'completed' | 'failed' | 'rejected_inappropriate';
}
```

## Configuration

### Lambda Environment Variables

```bash
AWS_REGION=us-west-2
MEDIA_TABLE=gameflex-{env}-Media
BUCKET_NAME=gameflex-media-{env}
```

### IAM Permissions

The AI processing Lambda requires:

```json
{
  "Version": "2012-10-17",
  "Statement": [
    {
      "Effect": "Allow",
      "Action": [
        "rekognition:DetectModerationLabels",
        "rekognition:DetectLabels",
        "rekognition:DetectText",
        "rekognition:DetectFaces",
        "rekognition:RecognizeCelebrities",
        "rekognition:DetectProtectiveEquipment"
      ],
      "Resource": "*"
    }
  ]
}
```

## Monitoring & Costs

### CloudWatch Metrics

Monitor these key metrics:
- Lambda invocations and duration
- Rekognition API calls
- Error rates and failed processing

### Cost Optimization

- **Rekognition**: Pay per image analyzed (~$0.001 per image)
- **Lambda**: Pay per execution and duration
- **S3**: Storage costs with Glacier lifecycle (30 days)

### Processing Limits

- **Concurrent Executions**: Limited to 10 (production) / 5 (development)
- **Timeout**: 5 minutes per image
- **Memory**: 1024 MB for image processing

## Testing

Run the test script to verify integration:

```bash
cd backend/test-scripts
npx ts-node test-rekognition.ts
```

## Deployment

1. Deploy the updated CDK stack:
```bash
npm run deploy:development
```

2. Upload a test image to trigger processing

3. Check DynamoDB for analysis results

4. Use the analysis API to retrieve results

## Error Handling

The system gracefully handles errors:

- **Rekognition Failures**: Content is allowed by default
- **Processing Timeouts**: Marked as failed with retry capability
- **Invalid Images**: Rejected with appropriate error messages

## Future Enhancements

Potential improvements:
- Video analysis support
- Custom model training
- Real-time processing notifications
- Batch processing for existing media
