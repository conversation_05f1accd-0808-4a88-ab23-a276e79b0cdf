# Gitleaks configuration for GameFlex project
# This file configures secrets detection patterns and exclusions

title = "GameFlex Secrets Detection Configuration"

# Global configuration
[extend]
# Use default gitleaks rules as base
useDefault = true

# Custom rules for GameFlex-specific patterns
[[rules]]
id = "aws-access-key"
description = "AWS Access Key ID"
regex = '''AKIA[0-9A-Z]{16}'''
tags = ["aws", "credentials"]

[[rules]]
id = "aws-secret-key"
description = "AWS Secret Access Key"
regex = '''[A-Za-z0-9/+=]{40}'''
tags = ["aws", "credentials"]

[[rules]]
id = "cloudflare-api-key"
description = "Cloudflare API Key"
regex = '''[a-f0-9]{37}'''
tags = ["cloudflare", "api-key"]

[[rules]]
id = "r2-access-key"
description = "Cloudflare R2 Access Key"
regex = '''[A-Z0-9]{20}'''
tags = ["cloudflare", "r2", "credentials"]

[[rules]]
id = "jwt-secret"
description = "JWT Secret Key"
regex = '''jwt[_-]?secret[_-]?key?\s*[:=]\s*["\']?[A-Za-z0-9+/=]{32,}["\']?'''
tags = ["jwt", "secret"]

[[rules]]
id = "database-url"
description = "Database Connection URL"
regex = '''(postgres|mysql|mongodb)://[^:\s]+:[^@\s]+@[^/\s]+/[^\s]+'''
tags = ["database", "credentials"]

[[rules]]
id = "api-key-generic"
description = "Generic API Key"
regex = '''api[_-]?key[_-]?\s*[:=]\s*["\']?[A-Za-z0-9]{20,}["\']?'''
tags = ["api-key"]

[[rules]]
id = "private-key"
description = "Private Key"
regex = '''-----BEGIN (RSA |EC |DSA |OPENSSH |PGP )?PRIVATE KEY-----'''
tags = ["private-key", "certificate"]

[[rules]]
id = "certificate"
description = "Certificate"
regex = '''-----BEGIN CERTIFICATE-----'''
tags = ["certificate"]

[[rules]]
id = "password-hardcoded"
description = "Hardcoded Password"
regex = '''password\s*[:=]\s*["\'][^"\']{8,}["\']'''
tags = ["password", "credentials"]

[[rules]]
id = "cognito-client-secret"
description = "AWS Cognito Client Secret"
regex = '''[A-Za-z0-9+/=]{56}'''
tags = ["aws", "cognito", "secret"]

[[rules]]
id = "flutter-env-secret"
description = "Flutter Environment Secret"
regex = '''const\s+String\s+\w*[Ss]ecret\w*\s*=\s*["\'][^"\']+["\']'''
tags = ["flutter", "secret"]

[[rules]]
id = "node-env-secret"
description = "Node.js Environment Secret"
regex = '''process\.env\.\w*[Ss]ecret\w*\s*\|\|\s*["\'][^"\']+["\']'''
tags = ["nodejs", "secret"]

# Allowlist - patterns to ignore
[[allowlist]]
description = "Test files and examples"
paths = [
    '''test/.*''',
    '''tests/.*''',
    '''.*test\.dart''',
    '''.*test\.ts''',
    '''.*test\.js''',
    '''example/.*''',
    '''examples/.*''',
    '''.*/example\..*''',
    '''.*/examples\..*'''
]

[[allowlist]]
description = "Documentation files"
paths = [
    '''README\.md''',
    '''.*\.md''',
    '''docs/.*''',
    '''\.gitlab/.*\.md'''
]

[[allowlist]]
description = "Configuration templates"
paths = [
    '''.*\.example''',
    '''.*\.template''',
    '''.*\.sample'''
]

[[allowlist]]
description = "Build and dependency files"
paths = [
    '''node_modules/.*''',
    '''\.pub-cache/.*''',
    '''\.dart_tool/.*''',
    '''build/.*''',
    '''dist/.*''',
    '''coverage/.*''',
    '''cdk\.out/.*'''
]

[[allowlist]]
description = "Git and IDE files"
paths = [
    '''\.git/.*''',
    '''\.vscode/.*''',
    '''\.idea/.*''',
    '''.*\.log'''
]

# Specific allowlist rules for known false positives
[[allowlist]]
description = "Flutter pubspec.lock hashes"
regex = '''[a-f0-9]{64}'''
paths = ['''pubspec\.lock''']

[[allowlist]]
description = "Package lock file hashes"
regex = '''sha[0-9]+-[A-Za-z0-9+/=]+'''
paths = ['''package-lock\.json''', '''yarn\.lock''']

[[allowlist]]
description = "CDK context values"
regex = '''arn:aws:.*'''
paths = ['''cdk\.context\.json''']

[[allowlist]]
description = "Test API endpoints"
regex = '''https://.*\.execute-api\..*\.amazonaws\.com/.*'''
paths = ['''.*test.*''', '''.*\.test\..*''']

[[allowlist]]
description = "Example credentials in documentation"
regex = '''AKIA[0-9A-Z]{16}'''
paths = ['''.*\.md''', '''docs/.*''']

[[allowlist]]
description = "Placeholder secrets in config examples"
regex = '''your-.*-here'''
paths = ['''.*\.example''', '''.*\.template''', '''.*\.md''']

[[allowlist]]
description = "Base64 encoded images and assets"
regex = '''data:image/.*base64,.*'''

[[allowlist]]
description = "Flutter generated files"
paths = [
    '''ios/Flutter/Generated\.xcconfig''',
    '''ios/Runner/GeneratedPluginRegistrant\..*''',
    '''android/app/src/main/java/.*/GeneratedPluginRegistrant\.java''',
    '''linux/flutter/generated_plugin_registrant\..*''',
    '''macos/Flutter/GeneratedPluginRegistrant\.swift''',
    '''windows/flutter/generated_plugin_registrant\..*'''
]

[[allowlist]]
description = "AWS CDK generated files"
paths = [
    '''cdk\.out/.*''',
    '''.*\.assets\.json''',
    '''.*\.template\.json'''
]

# Entropy detection configuration
[entropy]
# Enable entropy detection for high-entropy strings
enabled = true

# Minimum entropy threshold (0.0 to 8.0)
threshold = 6.0

# Groups to check for entropy
groups = [
    "password",
    "secret",
    "key",
    "token",
    "credential"
]

# File extensions to scan
[fileExtensions]
include = [
    ".dart",
    ".ts",
    ".js",
    ".json",
    ".yaml",
    ".yml",
    ".toml",
    ".env",
    ".sh",
    ".md"
]

exclude = [
    ".lock",
    ".log",
    ".cache",
    ".tmp"
]

# Scanning configuration
[scan]
# Maximum file size to scan (in bytes)
maxFileSize = 1048576  # 1MB

# Maximum number of files to scan
maxFiles = 10000

# Timeout for scanning (in seconds)
timeout = 300

# Follow symbolic links
followSymlinks = false

# Scan hidden files
scanHidden = false

# Performance settings
[performance]
# Number of worker threads
workers = 4

# Memory limit (in MB)
memoryLimit = 512

# Enable parallel scanning
parallel = true

# Reporting configuration
[reporting]
# Output format (json, csv, sarif)
format = "json"

# Include file content in report
includeContent = false

# Include commit information
includeCommit = true

# Redact secrets in output
redactSecrets = true

# Verbose output
verbose = false
