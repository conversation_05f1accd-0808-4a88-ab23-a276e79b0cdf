#!/usr/bin/env node
import * as cdk from 'aws-cdk-lib';
import { GameFlexBackendStack } from '../lib/gameflex-backend-stack';

const app = new cdk.App();

// Get environment from context or environment variables
const environment = app.node.tryGetContext('environment') || process.env.ENVIRONMENT || 'development';
const projectName = app.node.tryGetContext('projectName') || process.env.PROJECT_NAME || 'gameflex';

// Environment-specific qualifiers
const qualifierMap = {
  development: 'gfdev',
  staging: 'gfstaging',
  production: 'gfprod'
};

const qualifier = qualifierMap[environment as keyof typeof qualifierMap] || 'gfdev';

// Environment-specific configuration
const envConfig = {
  development: {
    account: process.env.CDK_DEFAULT_ACCOUNT,
    region: process.env.CDK_DEFAULT_REGION || 'us-west-2',
    domainName: 'dev.api.gameflex.io',
    mediaDomainName: 'dev.media.gameflex.io',
    certificateArn: 'arn:aws:acm:us-east-1:************:certificate/9c6c8946-2c19-4efa-ab52-c44a42236662',
    mediaCertificateArn: 'arn:aws:acm:us-east-1:************:certificate/48310ae8-9b6c-40e6-9306-2dcdb4e76ee4',
  },
  staging: {
    account: process.env.CDK_DEFAULT_ACCOUNT,
    region: process.env.CDK_DEFAULT_REGION || 'us-west-2',
    domainName: 'staging.api.gameflex.io',
    mediaDomainName: 'staging.media.gameflex.io',
    certificateArn: 'arn:aws:acm:us-east-1:************:certificate/9c6c8946-2c19-4efa-ab52-c44a42236662',
    mediaCertificateArn: 'arn:aws:acm:us-east-1:************:certificate/48310ae8-9b6c-40e6-9306-2dcdb4e76ee4',
  },
  production: {
    account: process.env.CDK_DEFAULT_ACCOUNT,
    region: process.env.CDK_DEFAULT_REGION || 'us-east-1',
    domainName: 'api.gameflex.io',
    mediaDomainName: 'media.gameflex.io',
    certificateArn: 'arn:aws:acm:us-east-1:************:certificate/9c6c8946-2c19-4efa-ab52-c44a42236662',
    mediaCertificateArn: 'arn:aws:acm:us-east-1:************:certificate/48310ae8-9b6c-40e6-9306-2dcdb4e76ee4',
  }
};

const config = envConfig[environment as keyof typeof envConfig] || envConfig.development;

new GameFlexBackendStack(app, `${projectName}-${environment}`, {
  env: {
    account: config.account,
    region: config.region,
  },
  synthesizer: new cdk.DefaultStackSynthesizer({
    qualifier: qualifier,
  }),
  environment,
  projectName,
  domainName: config.domainName,
  mediaDomainName: config.mediaDomainName,
  certificateArn: config.certificateArn,
  mediaCertificateArn: config.mediaCertificateArn,
});