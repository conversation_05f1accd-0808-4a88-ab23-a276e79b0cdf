#!/bin/bash

# Script to ensure required secrets exist before CDK deployment
# This prevents deployment failures when secrets don't exist

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Print functions
print_status() {
    echo -e "${BLUE}ℹ️  $1${NC}"
}

print_success() {
    echo -e "${GREEN}✅ $1${NC}"
}

print_warning() {
    echo -e "${YELLOW}⚠️  $1${NC}"
}

print_error() {
    echo -e "${RED}❌ $1${NC}"
}

# Function to check if secret exists
secret_exists() {
    local secret_name=$1
    aws secretsmanager describe-secret --secret-id "$secret_name" &> /dev/null
}

# Function to create secret if it doesn't exist
ensure_secret() {
    local secret_name=$1
    local description=$2
    local secret_value=$3
    
    if secret_exists "$secret_name"; then
        print_success "Secret '$secret_name' already exists"
        return 0
    fi
    
    print_status "Creating secret '$secret_name'..."
    
    aws secretsmanager create-secret \
        --name "$secret_name" \
        --description "$description" \
        --secret-string "$secret_value" \
        --region "${AWS_REGION:-us-west-2}" > /dev/null
    
    if [ $? -eq 0 ]; then
        print_success "Created secret '$secret_name'"
    else
        print_error "Failed to create secret '$secret_name'"
        return 1
    fi
}

# Main function
main() {
    local project_name=${1:-gameflex}
    local environment=${2:-development}
    
    print_status "Ensuring secrets exist for project: $project_name, environment: $environment"
    
    # R2 Configuration Secret
    local r2_secret_name="${project_name}-r2-config-${environment}"
    local r2_secret_value='{
        "accountId": "",
        "accessKeyId": "",
        "secretAccessKey": "",
        "endpoint": "",
        "bucketName": "'${project_name}'-'${environment}'",
        "publicUrl": "https://pub-34709f09e8384ef1a67928492571c01d.r2.dev"
    }'
    
    ensure_secret "$r2_secret_name" "CloudFlare R2 configuration for GameFlex" "$r2_secret_value"
    
    # App Configuration Secret
    local app_config_secret_name="${project_name}-app-config-${environment}"
    local app_config_secret_value='{
        "cloudflareApiToken": "",
        "testUserEmail": "test@'${environment}'.gameflex.io",
        "testUserPassword": "Test123!",
        "debugMode": "'${environment}'",
        "apiBaseUrl": "",
        "userPoolId": "",
        "userPoolClientId": "",
        "xapiKey": ""
    }'
    
    ensure_secret "$app_config_secret_name" "General application configuration for GameFlex" "$app_config_secret_value"

    # Apple Sign In Configuration Secret
    local apple_config_secret_name="${project_name}-apple-config-${environment}"
    local apple_config_secret_value='{
        "teamId": "",
        "clientId": "",
        "keyId": "",
        "privateKey": ""
    }'

    ensure_secret "$apple_config_secret_name" "Apple Sign In configuration for GameFlex" "$apple_config_secret_value"

    # Xbox Integration Configuration Secret (Direct Azure)
    local xbox_config_secret_name="${project_name}-xbox-config-${environment}"
    local xbox_config_secret_value='{
        "azureClientId": "",
        "azureClientSecret": ""
    }'

    ensure_secret "$xbox_config_secret_name" "Xbox integration configuration for GameFlex" "$xbox_config_secret_value"

    print_success "All required secrets are available"
    
    # Instructions for updating secrets
    print_warning "Remember to update the secrets with actual values:"
    echo ""
    echo "For R2 configuration:"
    echo "aws secretsmanager put-secret-value --secret-id '$r2_secret_name' --secret-string '{\"accountId\":\"YOUR_R2_ACCOUNT_ID\",\"accessKeyId\":\"YOUR_R2_ACCESS_KEY\",\"secretAccessKey\":\"YOUR_R2_SECRET_KEY\",\"endpoint\":\"https://YOUR_ACCOUNT_ID.r2.cloudflarestorage.com\",\"bucketName\":\"${project_name}-${environment}\",\"publicUrl\":\"https://pub-34709f09e8384ef1a67928492571c01d.r2.dev\"}'"
    echo ""
    echo "For app configuration:"
    echo "aws secretsmanager put-secret-value --secret-id '$app_config_secret_name' --secret-string '{\"cloudflareApiToken\":\"YOUR_CLOUDFLARE_TOKEN\",\"testUserEmail\":\"test@${environment}.gameflex.io\",\"testUserPassword\":\"Test123!\",\"debugMode\":\"${environment}\",\"apiBaseUrl\":\"YOUR_API_URL\",\"userPoolId\":\"YOUR_USER_POOL_ID\",\"userPoolClientId\":\"YOUR_USER_POOL_CLIENT_ID\",\"xapiKey\":\"YOUR_XAPI_US_API_KEY\"}'"
    echo ""
    echo "For Apple Sign In configuration:"
    echo "aws secretsmanager put-secret-value --secret-id '$apple_config_secret_name' --secret-string '{\"teamId\":\"YOUR_APPLE_TEAM_ID\",\"clientId\":\"YOUR_APPLE_CLIENT_ID\",\"keyId\":\"YOUR_APPLE_KEY_ID\",\"privateKey\":\"YOUR_APPLE_PRIVATE_KEY_CONTENT\"}'"
    echo ""
    echo "For Xbox integration configuration:"
    echo "aws secretsmanager put-secret-value --secret-id '$xbox_config_secret_name' --secret-string '{\"azureClientId\":\"YOUR_AZURE_CLIENT_ID\",\"azureClientSecret\":\"YOUR_AZURE_CLIENT_SECRET\"}'"
}

# Check if AWS CLI is available
if ! command -v aws &> /dev/null; then
    print_error "AWS CLI is not installed or not in PATH"
    exit 1
fi

# Check if AWS credentials are configured
if ! aws sts get-caller-identity &> /dev/null; then
    print_error "AWS credentials are not configured or invalid"
    exit 1
fi

# Run main function with provided arguments
main "$@"
