#!/bin/sh

# GameFlex CI/CD Deployment Script
# Simplified deployment script for GitLab CI/CD environments
# Compatible with Alpine Linux and POSIX sh

set -e  # Exit on any error

# Colors for output (compatible with sh)
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Function to print colored output
print_status() {
    printf "${BLUE}[INFO]${NC} %s\n" "$1"
}

print_success() {
    printf "${GREEN}[SUCCESS]${NC} %s\n" "$1"
}

print_warning() {
    printf "${YELLOW}[WARNING]${NC} %s\n" "$1"
}

print_error() {
    printf "${RED}[ERROR]${NC} %s\n" "$1"
}

# Validate environment argument
if [ $# -eq 0 ]; then
    print_error "Environment argument is required"
    print_error "Usage: $0 <environment>"
    print_error "Valid environments: development, staging, production"
    exit 1
fi

ENVIRONMENT="$1"
PROJECT_NAME="gameflex"
STACK_NAME="${PROJECT_NAME}-${ENVIRONMENT}"

print_status "Starting CDK deployment for GameFlex backend"
print_status "Environment: $ENVIRONMENT"
print_status "Stack Name: $STACK_NAME"
print_status "Project: $PROJECT_NAME"

# Validate environment
case "$ENVIRONMENT" in
    development|staging|production)
        print_status "Valid environment: $ENVIRONMENT"
        ;;
    *)
        print_error "Invalid environment: $ENVIRONMENT"
        print_error "Valid environments: development, staging, production"
        exit 1
        ;;
esac

# Check if running in CI
if [ -z "$CI" ]; then
    print_warning "Not running in CI environment"
    print_warning "This script is designed for CI/CD pipelines"
fi

# Verify AWS CLI is available
if ! command -v aws >/dev/null 2>&1; then
    print_error "AWS CLI is not installed or not in PATH"
    exit 1
fi

# Verify CDK is available (check npm package first, then global)
if [ -f "node_modules/.bin/cdk" ]; then
    CDK_CMD="npx cdk"
    print_status "Using local CDK installation via npm"
elif command -v cdk >/dev/null 2>&1; then
    CDK_CMD="cdk"
    print_status "Using global CDK installation"
else
    print_error "AWS CDK is not available"
    print_error "Please ensure CDK is installed via npm or globally"
    exit 1
fi

print_success "CDK installation verified"

# Set environment-specific variables
case "$ENVIRONMENT" in
    development)
        print_status "Configuring for development environment"
        ;;
    staging)
        print_status "Configuring for staging environment"
        ;;
    production)
        print_status "Configuring for production environment"
        ;;
esac

# Export environment for CDK
export ENVIRONMENT

# Verify CDK files exist
if [ ! -f "cdk.json" ]; then
    print_error "cdk.json not found. Are you running from the correct directory?"
    exit 1
fi

if [ ! -d "cdk.out" ]; then
    print_error "cdk.out directory not found. Please run CDK synthesis first."
    exit 1
fi

print_status "CDK files verified"

# Deploy the stack
print_status "Starting CDK deployment..."
print_status "Stack: $STACK_NAME"

# Use CDK deploy with CI-friendly options
if $CDK_CMD deploy "$STACK_NAME" \
    --require-approval never \
    --progress events \
    --outputs-file "cdk-outputs-${ENVIRONMENT}.json" \
    --verbose; then
    
    print_success "CDK deployment completed successfully"
    print_success "Stack: $STACK_NAME"
    
    # Show outputs if file exists
    if [ -f "cdk-outputs-${ENVIRONMENT}.json" ]; then
        print_status "Deployment outputs saved to: cdk-outputs-${ENVIRONMENT}.json"
    fi
    
else
    print_error "CDK deployment failed"
    print_error "Stack: $STACK_NAME"
    exit 1
fi

print_success "GameFlex backend deployment completed for $ENVIRONMENT environment"
