#!/bin/bash

# GameFlex AWS Data Seeding Script - Comprehensive Version
# This script seeds AWS services with test data for development
# Combines the best features from both previous seeding scripts
#
# Usage: ./seed-data.sh [-f|--force]
#   -f, --force    Delete all existing data before seeding (fresh start)

set -e

# Parse command line arguments
FORCE_MODE=false
SEED_ENVIRONMENT=""
while [[ $# -gt 0 ]]; do
    case $1 in
        -f|--force)
            FORCE_MODE=true
            shift
            ;;
        -e|--environment)
            SEED_ENVIRONMENT="$2"
            shift 2
            ;;
        -h|--help)
            echo "Usage: $0 [-f|--force] [-e|--environment ENV]"
            echo "  -f, --force         Delete all existing data before seeding (fresh start)"
            echo "  -e, --environment   Environment to seed (development, staging, production)"
            echo "  -h, --help          Show this help message"
            echo ""
            echo "Examples:"
            echo "  $0                           # Seed development environment"
            echo "  $0 -e staging               # Seed staging environment"
            echo "  $0 -f -e staging            # Force fresh seed of staging"
            exit 0
            ;;
        *)
            echo "Unknown option: $1"
            echo "Use -h or --help for usage information"
            exit 1
            ;;
    esac
done

# Set environment (command line > environment variable > default)
if [ -n "$SEED_ENVIRONMENT" ]; then
    export ENVIRONMENT="$SEED_ENVIRONMENT"
elif [ -z "$ENVIRONMENT" ]; then
    export ENVIRONMENT="development"
fi

if [ "$FORCE_MODE" = true ]; then
    echo "[SEED] Starting GameFlex AWS data seeding [$ENVIRONMENT] with FORCE MODE (fresh data)..."
else
    echo "[SEED] Starting GameFlex AWS data seeding [$ENVIRONMENT]..."
fi

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
CYAN='\033[0;36m'
NC='\033[0m'

# Function to print colored output
print_status() {
    echo -e "${GREEN}[INFO]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARN]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

print_header() {
    echo -e "${BLUE}[SEED]${NC} $1"
}

# Load environment variables from .env files
load_env_file() {
    local script_dir="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
    local backend_dir="$(dirname "$script_dir")"
    local environment=${ENVIRONMENT:-development}

    print_status "Loading configuration for environment: $environment"

    # Set basic defaults for CDK deployment
    export ENVIRONMENT="$environment"
    export PROJECT_NAME="gameflex"
    export AWS_REGION="us-west-2"

    # Second, load secrets from .env files
    local env_file="$backend_dir/.env.${environment}"
    local default_env_file="$backend_dir/.env"

    print_status "Loading secrets from .env files..."
    if [ -f "$env_file" ]; then
        print_status "Loading secrets from .env.${environment}..."
        set -a
        source "$env_file"
        set +a
        print_status "✅ Secrets loaded from .env.${environment}"
    elif [ -f "$default_env_file" ]; then
        print_status "Loading secrets from .env (fallback)..."
        set -a
        source "$default_env_file"
        set +a
        print_status "✅ Secrets loaded from .env"
    else
        print_error "❌ No .env file found for environment: $environment"
        print_status "Expected files:"
        print_status "  - $env_file"
        print_status "  - $default_env_file (fallback)"
        print_status ""
        print_status "Create the appropriate .env file with your secrets:"
        print_status "  cp .env.example .env"
        if [ "$environment" != "development" ]; then
            print_status "  cp .env.${environment}.example .env.${environment}"
        fi
        return 1
    fi

    # Note: R2 variables are no longer required since we're using S3
    print_status "✅ Environment configuration loaded successfully"
}

# Function to populate AWS Secrets Manager
populate_secrets_manager() {
    print_header "Populating AWS Secrets Manager with configuration..."

    local environment=${ENVIRONMENT:-development}
    local project_name=${PROJECT_NAME:-gameflex}
    local aws_region=${AWS_REGION:-us-west-2}

    # App Config Secret Name (R2 config no longer needed since we use S3)
    local app_config_secret_name="${project_name}-app-config-${environment}"

    print_status "Target secrets:"
    print_status "  App Config: $app_config_secret_name"
    print_status "  Region: $aws_region"

    # Test AWS connection first
    if ! aws sts get-caller-identity --region "$aws_region" >/dev/null 2>&1; then
        print_error "❌ Cannot connect to AWS. Please check your credentials and region."
        print_status "Current AWS_REGION: $aws_region"
        print_status "Try: aws configure list"
        return 1
    fi

    print_status "✅ AWS connection verified"

    # Check if app config secret exists
    local app_config_secret_exists=false

    if aws secretsmanager describe-secret --secret-id "$app_config_secret_name" --region "$aws_region" >/dev/null 2>&1; then
        app_config_secret_exists=true
        print_status "✅ App config secret exists: $app_config_secret_name"
    else
        print_warning "⚠️  App config secret does not exist: $app_config_secret_name"
    fi

    # Create App Config secret JSON
    local app_config_secret_json=$(cat <<EOF
{
  "testUserEmail": "${TEST_USER_EMAIL:-test@${environment}.gameflex.io}",
  "testUserPassword": "${TEST_USER_PASSWORD:-Test123!}",
  "debugMode": "${DEBUG_MODE:-${environment}}",
  "apiBaseUrl": "${API_BASE_URL:-}",
  "userPoolId": "${USER_POOL_ID:-}",
  "userPoolClientId": "${USER_POOL_CLIENT_ID:-}"
}
EOF
)

    # Update or create App Config secret
    if [ "$app_config_secret_exists" = true ]; then
        print_status "Updating existing App Config secret: $app_config_secret_name"
        if aws secretsmanager put-secret-value \
            --secret-id "$app_config_secret_name" \
            --secret-string "$app_config_secret_json" \
            --region "$aws_region" >/dev/null 2>&1; then
            print_status "✅ App config secret updated successfully"
        else
            print_error "❌ Failed to update app config secret"
            return 1
        fi
    else
        print_warning "App Config secret $app_config_secret_name does not exist. It should be created by CDK deployment."
        print_status "Deploy your CDK stack first, then run this script again."
        print_status "Manual creation command:"
        echo "aws secretsmanager create-secret --name '$app_config_secret_name' --secret-string '$app_config_secret_json' --region '$aws_region'"
    fi

    print_status "✅ Secrets Manager population completed"
}

# Function to get S3 bucket information from CDK deployment
get_s3_infrastructure_info() {
    print_header "Getting S3 infrastructure information from CDK deployment..."

    local environment=${ENVIRONMENT:-development}
    local project_name=${PROJECT_NAME:-gameflex}
    local aws_region=${AWS_REGION:-us-west-2}
    local stack_name="${project_name}-${environment}"

    # Get CloudFormation stack outputs
    local stack_outputs
    if ! stack_outputs=$(aws cloudformation describe-stacks \
        --stack-name "$stack_name" \
        --region "$aws_region" \
        --query 'Stacks[0].Outputs' \
        --output json 2>/dev/null); then
        print_error "❌ Failed to get CloudFormation stack outputs for $stack_name"
        print_status "Make sure your CDK stack is deployed:"
        print_status "  cd backend && ./deploy.sh --environment $environment"
        return 1
    fi

    # Extract S3 bucket name
    S3_BUCKET_NAME=$(echo "$stack_outputs" | jq -r '.[] | select(.OutputKey=="MediaBucketName") | .OutputValue' 2>/dev/null)
    if [ -z "$S3_BUCKET_NAME" ] || [ "$S3_BUCKET_NAME" = "null" ]; then
        print_error "❌ Could not find MediaBucketName in stack outputs"
        return 1
    fi

    # Extract CloudFront domain
    CLOUDFRONT_DOMAIN=$(echo "$stack_outputs" | jq -r '.[] | select(.OutputKey=="CloudFrontDistributionDomain") | .OutputValue' 2>/dev/null)
    if [ -z "$CLOUDFRONT_DOMAIN" ] || [ "$CLOUDFRONT_DOMAIN" = "null" ]; then
        print_warning "⚠️  Could not find CloudFront domain in stack outputs"
        CLOUDFRONT_DOMAIN=""
    fi

    # Extract custom media domain if available
    MEDIA_CUSTOM_DOMAIN=$(echo "$stack_outputs" | jq -r '.[] | select(.OutputKey=="MediaCustomDomain") | .OutputValue' 2>/dev/null)
    if [ -z "$MEDIA_CUSTOM_DOMAIN" ] || [ "$MEDIA_CUSTOM_DOMAIN" = "null" ]; then
        MEDIA_CUSTOM_DOMAIN=""
    fi

    # Set the media URL (prefer custom domain, fallback to CloudFront)
    if [ -n "$MEDIA_CUSTOM_DOMAIN" ]; then
        MEDIA_BASE_URL="https://$MEDIA_CUSTOM_DOMAIN"
        print_status "✅ Using custom media domain: $MEDIA_CUSTOM_DOMAIN"
    elif [ -n "$CLOUDFRONT_DOMAIN" ]; then
        MEDIA_BASE_URL="https://$CLOUDFRONT_DOMAIN"
        print_status "✅ Using CloudFront domain: $CLOUDFRONT_DOMAIN"
    else
        MEDIA_BASE_URL="https://$S3_BUCKET_NAME.s3.$aws_region.amazonaws.com"
        print_status "⚠️  Using S3 direct URL (no CDN): $MEDIA_BASE_URL"
    fi

    print_status "S3 Infrastructure:"
    print_status "  Bucket: $S3_BUCKET_NAME"
    print_status "  Media URL: $MEDIA_BASE_URL"
    print_status "  Region: $aws_region"

    return 0
}

# Function to get stack prefix for CDK deployment
get_stack_prefix() {
    local environment=${ENVIRONMENT:-development}
    local project_name=${PROJECT_NAME:-gameflex}

    # CDK stack naming convention: {project_name}-{environment}
    echo "${project_name}-${environment}"
}

# Function to get AWS region
get_region() {
    echo "${AWS_REGION:-us-west-2}"
}



# Helper functions
item_exists() {
    local table_name=$1
    local key_condition=$2

    # Check if item exists by getting the item and checking if we get a result
    local item_result=$(aws dynamodb get-item \
        --table-name "$table_name" \
        --key "$key_condition" \
        --region "$AWS_REGION" \
        --query 'Item' \
        --output text 2>/dev/null)

    # Return success if we got a result and it's not empty/null
    [ $? -eq 0 ] && [ -n "$item_result" ] && [ "$item_result" != "None" ]
}

# Function to get Cognito User Pool ID
get_user_pool_id() {
    local user_pool_id=$(aws cognito-idp list-user-pools \
        --max-results 50 \
        --region "$AWS_REGION" \
        --query "UserPools[?contains(Name, '${PROJECT_NAME}') && contains(Name, '${ENVIRONMENT}')].Id" \
        --output text 2>/dev/null | head -n1)

    if [ -z "$user_pool_id" ] || [ "$user_pool_id" = "None" ]; then
        print_error "User Pool not found for project: ${PROJECT_NAME}-${ENVIRONMENT}"
        print_error "Make sure your CDK application is deployed with Cognito resources"
        return 1
    fi

    echo "$user_pool_id"
}

# Function to check if Cognito user exists
cognito_user_exists() {
    local email=$1
    local user_pool_id=$2

    local user_result=$(aws cognito-idp admin-get-user \
        --user-pool-id "$user_pool_id" \
        --username "$email" \
        --region "$AWS_REGION" \
        --query 'Username' \
        --output text 2>/dev/null)

    [ $? -eq 0 ] && [ -n "$user_result" ] && [ "$user_result" != "None" ]
}

# Function to create Cognito user
create_cognito_user() {
    local email=$1
    local password=$2
    local display_name=$3
    local user_pool_id=$4

    # Skip existence check in force mode since we cleared all users
    if [ "$FORCE_MODE" != true ] && cognito_user_exists "$email" "$user_pool_id"; then
        print_status "Cognito user $email already exists, skipping creation"
        return 0
    fi

    print_status "Creating Cognito user: $email"

    # Create the user
    if aws cognito-idp admin-create-user \
        --user-pool-id "$user_pool_id" \
        --username "$email" \
        --user-attributes Name=email,Value="$email" Name=email_verified,Value=true Name=name,Value="$display_name" \
        --temporary-password "TempPassword123!" \
        --message-action SUPPRESS \
        --region "$AWS_REGION" > /dev/null 2>&1; then

        # Set permanent password
        if aws cognito-idp admin-set-user-password \
            --user-pool-id "$user_pool_id" \
            --username "$email" \
            --password "$password" \
            --permanent \
            --region "$AWS_REGION" > /dev/null 2>&1; then
            print_status "✅ Successfully created Cognito user: $email"
            return 0
        else
            print_error "❌ Failed to set password for user: $email"
            return 1
        fi
    else
        print_error "❌ Failed to create Cognito user: $email"
        return 1
    fi
}

# Function to get Cognito user ID by email
get_cognito_user_id() {
    local email=$1
    local user_pool_id=$2

    local cognito_user_id=$(aws cognito-idp admin-get-user \
        --user-pool-id "$user_pool_id" \
        --username "$email" \
        --region "$AWS_REGION" \
        --query 'Username' \
        --output text 2>/dev/null)

    if [ $? -eq 0 ] && [ -n "$cognito_user_id" ] && [ "$cognito_user_id" != "None" ]; then
        echo "$cognito_user_id"
    else
        echo "cognito-user-not-found-$email"
    fi
}

# Function to clear all data from a DynamoDB table
clear_table_data() {
    local table_name=$1
    local table_description=$2

    print_status "Clearing all data from $table_description ($table_name)..."

    # Get all items in the table
    local scan_result=$(aws dynamodb scan \
        --table-name "$table_name" \
        --region "$AWS_REGION" \
        --select "ALL_ATTRIBUTES" \
        --output json 2>/dev/null)

    if [ $? -ne 0 ]; then
        print_error "Failed to scan table $table_name"
        return 1
    fi

    local item_count=$(echo "$scan_result" | jq '.Items | length')

    if [ "$item_count" -eq 0 ]; then
        print_status "Table $table_description is already empty"
        return 0
    fi

    print_status "Found $item_count items to delete from $table_description"

    # Get table key schema to identify primary key attributes
    local key_schema=$(aws dynamodb describe-table \
        --table-name "$table_name" \
        --region "$AWS_REGION" \
        --query 'Table.KeySchema' \
        --output json 2>/dev/null)

    if [ $? -ne 0 ]; then
        print_error "Failed to get key schema for table $table_name"
        return 1
    fi

    # Extract key attribute names
    local hash_key=$(echo "$key_schema" | jq -r '.[] | select(.KeyType=="HASH") | .AttributeName')
    local range_key=$(echo "$key_schema" | jq -r '.[] | select(.KeyType=="RANGE") | .AttributeName // empty')

    # Delete each item
    local deleted_count=0
    echo "$scan_result" | jq -c '.Items[]' | while read -r item; do
        # Build the key for deletion
        local delete_key="{\"$hash_key\": $(echo "$item" | jq ".$hash_key")"

        if [ -n "$range_key" ] && [ "$range_key" != "null" ]; then
            delete_key="$delete_key, \"$range_key\": $(echo "$item" | jq ".$range_key")"
        fi

        delete_key="$delete_key}"

        # Delete the item
        if aws dynamodb delete-item \
            --table-name "$table_name" \
            --key "$delete_key" \
            --region "$AWS_REGION" > /dev/null 2>&1; then
            ((deleted_count++))
        else
            print_error "Failed to delete item from $table_name"
        fi
    done

    print_status "✅ Deleted $item_count items from $table_description"
}

put_item() {
    local table_name=$1
    local item_json=$2
    local item_description=$3

    # Check if item already exists for certain tables (skip in force mode)
    local should_check_exists=false
    local key_condition=""

    # Skip existence check in force mode since we cleared all data
    if [ "$FORCE_MODE" != true ]; then
        case "$table_name" in
            *Users)
                should_check_exists=true
                local user_id=$(echo "$item_json" | jq -r '.id.S // empty')
                if [ -n "$user_id" ]; then
                    key_condition='{"id":{"S":"'$user_id'"}}'
                fi
                ;;
            *UserProfiles)
                should_check_exists=true
                local user_id=$(echo "$item_json" | jq -r '.userId.S // empty')
                if [ -n "$user_id" ]; then
                    key_condition='{"userId":{"S":"'$user_id'"}}'
                fi
                ;;
            *Posts)
                should_check_exists=true
                local post_id=$(echo "$item_json" | jq -r '.id.S // empty')
                if [ -n "$post_id" ]; then
                    key_condition='{"id":{"S":"'$post_id'"}}'
                fi
                ;;
            *Media)
                should_check_exists=true
                local media_id=$(echo "$item_json" | jq -r '.id.S // empty')
                if [ -n "$media_id" ]; then
                    key_condition='{"id":{"S":"'$media_id'"}}'
                fi
                ;;
        esac

        # Check if item exists and skip if it does
        if [ "$should_check_exists" = true ] && [ -n "$key_condition" ]; then
            if item_exists "$table_name" "$key_condition"; then
                print_status "$item_description already exists, skipping"
                return 0
            fi
        fi
    fi

    print_status "Adding $item_description to $table_name..."
    
    local temp_file=$(mktemp)
    echo "$item_json" > "$temp_file"
    
    if timeout 30 aws dynamodb put-item \
        --table-name "$table_name" \
        --region "$AWS_REGION" \
        --item file://"$temp_file" > /dev/null 2>&1; then
        print_status "✅ Successfully added $item_description"
    else
        print_error "❌ Failed to add $item_description"
        # Show the actual error for debugging
        echo "Error details:" >&2
        timeout 30 aws dynamodb put-item \
            --table-name "$table_name" \
            --region "$AWS_REGION" \
            --item file://"$temp_file" 2>&1 | head -3 >&2
        rm -f "$temp_file"
        return 1
    fi
    
    rm -f "$temp_file"
    return 0
}

# Function to clear all Cognito users (except system users)
clear_cognito_users() {
    local user_pool_id=$1

    print_status "Clearing Cognito users from User Pool: $user_pool_id"

    # Get all users
    local users_result=$(aws cognito-idp list-users \
        --user-pool-id "$user_pool_id" \
        --region "$AWS_REGION" \
        --output json 2>/dev/null)

    if [ $? -ne 0 ]; then
        print_error "Failed to list users from Cognito User Pool"
        return 1
    fi

    local user_count=$(echo "$users_result" | jq '.Users | length')

    if [ "$user_count" -eq 0 ]; then
        print_status "Cognito User Pool is already empty"
        return 0
    fi

    print_status "Found $user_count users to delete from Cognito"

    # Delete each user
    local deleted_count=0
    echo "$users_result" | jq -c '.Users[]' | while read -r user; do
        local username=$(echo "$user" | jq -r '.Username')

        if aws cognito-idp admin-delete-user \
            --user-pool-id "$user_pool_id" \
            --username "$username" \
            --region "$AWS_REGION" > /dev/null 2>&1; then
            ((deleted_count++))
            print_status "Deleted Cognito user: $username"
        else
            print_error "Failed to delete Cognito user: $username"
        fi
    done

    print_status "✅ Deleted $user_count users from Cognito User Pool"
}

# Function to clear all S3 media files
clear_s3_media() {
    print_status "Clearing all media files from S3..."

    local aws_region=${AWS_REGION:-us-west-2}

    # List all objects in the bucket
    local objects_result=$(aws s3api list-objects-v2 \
        --bucket "$S3_BUCKET_NAME" \
        --region "$aws_region" \
        --output json 2>/dev/null)

    if [ $? -ne 0 ]; then
        print_error "Failed to list objects in S3 bucket"
        return 1
    fi

    local object_count=$(echo "$objects_result" | jq -r '.Contents | length // 0')

    # Handle null or empty results
    if [ -z "$object_count" ] || [ "$object_count" = "null" ] || [ "$object_count" -eq 0 ]; then
        print_status "S3 bucket is already empty"
        return 0
    fi

    print_status "Found $object_count files to delete from S3"

    # Delete all objects
    if aws s3 rm s3://"$S3_BUCKET_NAME" \
        --region "$aws_region" \
        --recursive > /dev/null 2>&1; then
        print_status "✅ Deleted all files from S3 bucket"
    else
        print_error "Failed to delete files from S3 bucket"
        return 1
    fi
}

# Function to clear all data when force mode is enabled
clear_all_data() {
    print_header "FORCE MODE: Clearing all existing data..."

    # Get User Pool ID for Cognito cleanup
    local user_pool_id
    if user_pool_id=$(get_user_pool_id); then
        clear_cognito_users "$user_pool_id"
    else
        print_warning "Could not find User Pool, skipping Cognito cleanup"
    fi
    echo

    # Clear all DynamoDB tables
    clear_table_data "$USERS_TABLE" "Users"
    echo
    clear_table_data "$USER_PROFILES_TABLE" "User Profiles"
    echo
    clear_table_data "$POSTS_TABLE" "Posts"
    echo
    clear_table_data "$MEDIA_TABLE" "Media"
    echo
    clear_table_data "$COMMENTS_TABLE" "Comments"
    echo
    clear_table_data "$LIKES_TABLE" "Likes"
    echo
    clear_table_data "$FOLLOWS_TABLE" "Follows"
    echo
    clear_table_data "$CHANNELS_TABLE" "Channels"
    echo
    clear_table_data "$CHANNEL_MEMBERS_TABLE" "Channel Members"
    echo

    # Clear S3 media files
    clear_s3_media
    echo

    print_status "🗑️  All existing data has been cleared!"
}

# Test AWS connection
test_aws_connection() {
    print_status "Testing AWS connection..."

    # Test AWS connection with verbose error output
    local aws_output
    if aws_output=$(aws sts get-caller-identity 2>&1); then
        local account_id=$(echo "$aws_output" | grep -o '"Account": "[^"]*"' | cut -d'"' -f4)
        local region=$(aws configure get region 2>/dev/null || echo "${AWS_REGION:-us-west-2}")
        print_status "✅ AWS CLI connection successful"
        print_status "Account ID: $account_id"
        print_status "Region: $region"
        return 0
    else
        print_error "❌ Failed to connect to AWS"
        print_error "AWS CLI output: $aws_output"
        print_status "Troubleshooting:"
        print_status "  1. Check AWS credentials: aws configure list"
        print_status "  2. Test manually: aws sts get-caller-identity"
        print_status "  3. Check region: aws configure get region"
        return 1
    fi
}

# Check if required tables exist
check_tables() {
    print_status "Checking if required tables exist..."

    local tables=("$USERS_TABLE" "$USER_PROFILES_TABLE" "$POSTS_TABLE" "$MEDIA_TABLE" "$COMMENTS_TABLE" "$LIKES_TABLE" "$FOLLOWS_TABLE" "$CHANNELS_TABLE" "$CHANNEL_MEMBERS_TABLE")
    local missing_tables=()

    print_status "Table names being checked:"
    for table in "${tables[@]}"; do
        print_status "  - $table"
    done
    echo
    
    for table in "${tables[@]}"; do
        # Check if table exists by trying to describe it and checking the output
        table_status=$(aws dynamodb describe-table --table-name "$table" --region "$AWS_REGION" --query 'Table.TableStatus' --output text 2>/dev/null)
        exit_code=$?

        if [ $exit_code -eq 0 ] && [ -n "$table_status" ] && [ "$table_status" != "None" ]; then
            print_status "✅ Table $table exists (Status: $table_status)"
        else
            print_warning "❌ Table $table does not exist or is not accessible"
            missing_tables+=("$table")
        fi
    done
    
    if [ ${#missing_tables[@]} -gt 0 ]; then
        print_error "Missing tables: ${missing_tables[*]}"
        print_error "Make sure your CDK application is deployed: ./deploy.sh development"
        return 1
    fi
    
    return 0
}

# Check S3 connectivity
check_s3_connectivity() {
    print_status "Checking S3 connectivity..."

    # Check if S3 bucket information is available
    if [ -z "$S3_BUCKET_NAME" ]; then
        print_error "S3 bucket name not available. Make sure get_s3_infrastructure_info() was called successfully."
        return 1
    fi

    local aws_region=${AWS_REGION:-us-west-2}

    print_status "S3 Configuration:"
    print_status "  Bucket: $S3_BUCKET_NAME"
    print_status "  Region: $aws_region"
    print_status "  Media URL: $MEDIA_BASE_URL"

    # Test S3 connectivity
    print_status "Testing S3 bucket access..."

    # Test bucket access
    if aws s3 ls "s3://$S3_BUCKET_NAME" --region "$aws_region" 2>/dev/null; then
        print_status "✅ S3 bucket '$S3_BUCKET_NAME' is accessible"

        # Test upload capability with a small test file
        local test_file=$(mktemp)
        echo "test" > "$test_file"
        local test_key="test/connectivity-test-$(date +%s).txt"

        if aws s3 cp "$test_file" "s3://$S3_BUCKET_NAME/$test_key" --region "$aws_region" 2>/dev/null; then
            print_status "✅ S3 upload test successful"

            # Clean up test file
            aws s3 rm "s3://$S3_BUCKET_NAME/$test_key" --region "$aws_region" 2>/dev/null
            print_status "✅ S3 delete test successful"
        else
            print_warning "❌ S3 upload test failed - check permissions"
        fi

        rm -f "$test_file"
    else
        print_error "❌ Cannot access S3 bucket '$S3_BUCKET_NAME'"
        print_error "Please check your AWS credentials and bucket configuration"
        return 1
    fi

    return 0
}

# Create Cognito users
create_cognito_users() {
    print_header "Creating Cognito users..."

    # Get User Pool ID
    local user_pool_id
    if ! user_pool_id=$(get_user_pool_id); then
        return 1
    fi

    print_status "Using User Pool ID: $user_pool_id"
    echo

    # Create all test users
    create_cognito_user "<EMAIL>" "DevPassword123!" "GameFlex Developer" "$user_pool_id"
    create_cognito_user "<EMAIL>" "AdminPassword123!" "GameFlex Admin" "$user_pool_id"
    create_cognito_user "<EMAIL>" "JohnPassword123!" "John Doe" "$user_pool_id"
    create_cognito_user "<EMAIL>" "JanePassword123!" "Jane Smith" "$user_pool_id"
    create_cognito_user "<EMAIL>" "MikePassword123!" "Mike Wilson" "$user_pool_id"

    print_status "Cognito users created successfully!"

    # Export user pool ID for use in other functions
    export USER_POOL_ID="$user_pool_id"
}

# Main execution starts here
main() {
    if [ "$FORCE_MODE" = true ]; then
        print_header "Starting comprehensive GameFlex data seeding with FORCE MODE..."
    else
        print_header "Starting comprehensive GameFlex data seeding..."
    fi
    echo

    # Load environment variables first
    if ! load_env_file; then
        print_error "❌ Failed to load environment configuration"
        print_status "Please ensure you have the required .env files with your secrets"
        exit 1
    fi

    # Get configuration for CDK deployment
    STACK_PREFIX=$(get_stack_prefix)
    # AWS_REGION is already set from environment loading, don't override it
    print_status "Using stack prefix for CDK: $STACK_PREFIX"
    print_status "Using region from environment: ${AWS_REGION:-us-west-2}"

    # Environment variables (with fallbacks)
    ENVIRONMENT=${ENVIRONMENT:-development}
    PROJECT_NAME=${PROJECT_NAME:-gameflex}

    # Table names (using stack prefix from CDK config)
    USERS_TABLE="${STACK_PREFIX}-Users"
    USER_PROFILES_TABLE="${STACK_PREFIX}-UserProfiles"
    POSTS_TABLE="${STACK_PREFIX}-Posts"
    MEDIA_TABLE="${STACK_PREFIX}-Media"
    COMMENTS_TABLE="${STACK_PREFIX}-Comments"
    LIKES_TABLE="${STACK_PREFIX}-Likes"
    FOLLOWS_TABLE="${STACK_PREFIX}-Follows"
    CHANNELS_TABLE="${STACK_PREFIX}-Channels"
    CHANNEL_MEMBERS_TABLE="${STACK_PREFIX}-ChannelMembers"

    echo

    # Test AWS connection
    if ! test_aws_connection; then
        exit 1
    fi
    echo

    # Get S3 infrastructure information from CDK deployment
    if ! get_s3_infrastructure_info; then
        print_error "❌ Failed to get S3 infrastructure information"
        print_status "Make sure your CDK stack is deployed:"
        print_status "  cd backend && ./deploy.sh --environment $ENVIRONMENT"
        exit 1
    fi
    echo

    # Populate AWS Secrets Manager
    if ! populate_secrets_manager; then
        print_error "❌ Failed to populate AWS Secrets Manager"
        print_status "This might be because:"
        print_status "  1. CDK stack not deployed yet (run './deploy.sh development' first)"
        print_status "  2. AWS credentials not configured"
        print_status "  3. Wrong AWS region"
        print_status ""
        print_status "Continuing with database seeding..."
    fi
    echo

    # Check if tables exist
    if ! check_tables; then
        exit 1
    fi
    echo

    # Check S3 connectivity
    if ! check_s3_connectivity; then
        exit 1
    fi
    echo

    # Clear all data if force mode is enabled
    if [ "$FORCE_MODE" = true ]; then
        if ! clear_all_data; then
            exit 1
        fi
        echo
    fi

    # Create Cognito users first
    if ! create_cognito_users; then
        exit 1
    fi
    echo

    # Debug: Show configuration
    print_status "Using configuration:"
    print_status "  Stack Prefix: $STACK_PREFIX"
    print_status "  Environment: $ENVIRONMENT"
    print_status "  Project: $PROJECT_NAME"
    print_status "  Region: $AWS_REGION"
    print_status "  Users Table: $USERS_TABLE"
    print_status "  Posts Table: $POSTS_TABLE"
    print_status "  Media Table: $MEDIA_TABLE"
    print_status "  S3 Bucket: $S3_BUCKET_NAME"
    print_status "  Media URL: $MEDIA_BASE_URL"
    print_status "  User Pool ID: $USER_POOL_ID"
    echo

    # Start seeding
    seed_users
    echo
    seed_user_profiles
    echo
    seed_media
    echo
    seed_posts
    echo
    seed_comments
    echo
    seed_likes
    echo
    seed_follows
    echo
    seed_channels
    echo
    seed_channel_members
    echo
    upload_media_files_to_s3
    echo

    print_status "🎉 Complete seeding finished successfully!"
    print_summary
}

# Seed Users table
seed_users() {
    print_header "Seeding Users table..."

    # Get actual Cognito user IDs
    local dev_cognito_id=$(get_cognito_user_id "<EMAIL>" "$USER_POOL_ID")
    local admin_cognito_id=$(get_cognito_user_id "<EMAIL>" "$USER_POOL_ID")
    local john_cognito_id=$(get_cognito_user_id "<EMAIL>" "$USER_POOL_ID")
    local jane_cognito_id=$(get_cognito_user_id "<EMAIL>" "$USER_POOL_ID")
    local mike_cognito_id=$(get_cognito_user_id "<EMAIL>" "$USER_POOL_ID")

    put_item "$USERS_TABLE" '{
        "id": {"S": "'$dev_cognito_id'"},
        "cognitoUserId": {"S": "'$dev_cognito_id'"},
        "email": {"S": "<EMAIL>"},
        "username": {"S": "developer"},
        "displayName": {"S": "GameFlex Developer"},
        "bio": {"S": "Development account for testing GameFlex features"},
        "isActive": {"BOOL": true},
        "isVerified": {"BOOL": true},
        "createdAt": {"S": "2024-01-01T00:00:00Z"},
        "updatedAt": {"S": "2024-01-01T00:00:00Z"}
    }' "developer user"

    put_item "$USERS_TABLE" '{
        "id": {"S": "550e8400-e29b-41d4-a716-************"},
        "cognitoUserId": {"S": "'$admin_cognito_id'"},
        "email": {"S": "<EMAIL>"},
        "username": {"S": "admin"},
        "displayName": {"S": "GameFlex Admin"},
        "bio": {"S": "Administrator account with full access"},
        "isActive": {"BOOL": true},
        "isVerified": {"BOOL": true},
        "createdAt": {"S": "2024-01-01T00:00:00Z"},
        "updatedAt": {"S": "2024-01-01T00:00:00Z"}
    }' "admin user"

    put_item "$USERS_TABLE" '{
        "id": {"S": "********-0000-0000-0000-********0003"},
        "cognitoUserId": {"S": "'$john_cognito_id'"},
        "email": {"S": "<EMAIL>"},
        "username": {"S": "johndoe"},
        "displayName": {"S": "John Doe"},
        "bio": {"S": "Gaming enthusiast and content creator."},
        "isActive": {"BOOL": true},
        "isVerified": {"BOOL": true},
        "createdAt": {"S": "2024-01-01T00:00:00Z"},
        "updatedAt": {"S": "2024-01-01T00:00:00Z"}
    }' "john doe user"

    put_item "$USERS_TABLE" '{
        "id": {"S": "********-0000-0000-0000-********0004"},
        "cognitoUserId": {"S": "'$jane_cognito_id'"},
        "email": {"S": "<EMAIL>"},
        "username": {"S": "janesmith"},
        "displayName": {"S": "Jane Smith"},
        "bio": {"S": "Professional gamer and streamer."},
        "isActive": {"BOOL": true},
        "isVerified": {"BOOL": true},
        "createdAt": {"S": "2024-01-01T00:00:00Z"},
        "updatedAt": {"S": "2024-01-01T00:00:00Z"}
    }' "jane smith user"

    put_item "$USERS_TABLE" '{
        "id": {"S": "********-0000-0000-0000-********0005"},
        "cognitoUserId": {"S": "'$mike_cognito_id'"},
        "email": {"S": "<EMAIL>"},
        "username": {"S": "mikewilson"},
        "displayName": {"S": "Mike Wilson"},
        "bio": {"S": "Casual gamer who loves sharing gaming moments."},
        "isActive": {"BOOL": true},
        "isVerified": {"BOOL": true},
        "createdAt": {"S": "2024-01-01T00:00:00Z"},
        "updatedAt": {"S": "2024-01-01T00:00:00Z"}
    }' "mike wilson user"

    print_status "Users seeded successfully!"
}

# Seed UserProfiles table
seed_user_profiles() {
    print_header "Seeding UserProfiles table..."

    # Get dev user ID for profile
    local dev_user_id=$(get_cognito_user_id "<EMAIL>" "$USER_POOL_ID")

    put_item "$USER_PROFILES_TABLE" '{
        "userId": {"S": "'$dev_user_id'"},
        "firstName": {"S": "Dev"},
        "lastName": {"S": "User"},
        "country": {"S": "United States"},
        "timezone": {"S": "America/New_York"},
        "language": {"S": "en"},
        "createdAt": {"S": "2024-01-01T00:00:00Z"},
        "updatedAt": {"S": "2024-01-01T00:00:00Z"}
    }' "dev profile"

    put_item "$USER_PROFILES_TABLE" '{
        "userId": {"S": "550e8400-e29b-41d4-a716-************"},
        "firstName": {"S": "Admin"},
        "lastName": {"S": "User"},
        "country": {"S": "United States"},
        "timezone": {"S": "America/Los_Angeles"},
        "language": {"S": "en"},
        "createdAt": {"S": "2024-01-01T00:00:00Z"},
        "updatedAt": {"S": "2024-01-01T00:00:00Z"}
    }' "admin profile"

    put_item "$USER_PROFILES_TABLE" '{
        "userId": {"S": "********-0000-0000-0000-********0003"},
        "firstName": {"S": "John"},
        "lastName": {"S": "Doe"},
        "country": {"S": "United States"},
        "timezone": {"S": "America/New_York"},
        "language": {"S": "en"},
        "createdAt": {"S": "2024-01-01T00:00:00Z"},
        "updatedAt": {"S": "2024-01-01T00:00:00Z"}
    }' "john profile"

    put_item "$USER_PROFILES_TABLE" '{
        "userId": {"S": "********-0000-0000-0000-********0004"},
        "firstName": {"S": "Jane"},
        "lastName": {"S": "Smith"},
        "country": {"S": "United States"},
        "timezone": {"S": "America/Los_Angeles"},
        "language": {"S": "en"},
        "createdAt": {"S": "2024-01-01T00:00:00Z"},
        "updatedAt": {"S": "2024-01-01T00:00:00Z"}
    }' "jane profile"

    put_item "$USER_PROFILES_TABLE" '{
        "userId": {"S": "********-0000-0000-0000-********0005"},
        "firstName": {"S": "Mike"},
        "lastName": {"S": "Wilson"},
        "country": {"S": "United States"},
        "timezone": {"S": "America/Chicago"},
        "language": {"S": "en"},
        "createdAt": {"S": "2024-01-01T00:00:00Z"},
        "updatedAt": {"S": "2024-01-01T00:00:00Z"}
    }' "mike profile"

    print_status "User profiles seeded successfully!"
}

# Seed Media table
seed_media() {
    print_header "Seeding Media table..."

    put_item "$MEDIA_TABLE" '{
        "id": {"S": "30000000-0000-0000-0000-********0001"},
        "fileName": {"S": "cod_screenshot.jpg"},
        "fileType": {"S": "image/jpeg"},
        "fileSize": {"N": "245760"},
        "mediaType": {"S": "image"},
        "userId": {"S": "********-0000-0000-0000-********0003"},
        "s3Key": {"S": "media/user/********-0000-0000-0000-********0003/cod_screenshot.jpg"},
        "bucketName": {"S": "'$S3_BUCKET_NAME'"},
        "url": {"S": "'$MEDIA_BASE_URL'/media/user/********-0000-0000-0000-********0003/cod_screenshot.jpg"},
        "status": {"S": "approved"},
        "staging_s3_key": {"S": "staging/media/********-0000-0000-0000-********0003/30000000-0000-0000-0000-********0001.jpg"},
        "final_s3_key": {"S": "media/user/********-0000-0000-0000-********0003/cod_screenshot.jpg"},
        "final_url": {"S": "'$MEDIA_BASE_URL'/media/user/********-0000-0000-0000-********0003/cod_screenshot.jpg"},
        "createdAt": {"S": "2024-12-28T14:00:00Z"},
        "updatedAt": {"S": "2024-12-28T14:00:00Z"}
    }' "cod screenshot media"

    put_item "$MEDIA_TABLE" '{
        "id": {"S": "30000000-0000-0000-0000-********0002"},
        "fileName": {"S": "diablo_screenshot.webp"},
        "fileType": {"S": "image/webp"},
        "fileSize": {"N": "189440"},
        "mediaType": {"S": "image"},
        "userId": {"S": "********-0000-0000-0000-********0004"},
        "s3Key": {"S": "media/user/********-0000-0000-0000-********0004/diablo_screenshot.webp"},
        "bucketName": {"S": "'$S3_BUCKET_NAME'"},
        "url": {"S": "'$MEDIA_BASE_URL'/media/user/********-0000-0000-0000-********0004/diablo_screenshot.webp"},
        "status": {"S": "approved"},
        "staging_s3_key": {"S": "staging/media/********-0000-0000-0000-********0004/30000000-0000-0000-0000-********0002.webp"},
        "final_s3_key": {"S": "media/user/********-0000-0000-0000-********0004/diablo_screenshot.webp"},
        "final_url": {"S": "'$MEDIA_BASE_URL'/media/user/********-0000-0000-0000-********0004/diablo_screenshot.webp"},
        "createdAt": {"S": "2024-12-27T16:00:00Z"},
        "updatedAt": {"S": "2024-12-27T16:00:00Z"}
    }' "diablo screenshot media"

    # Channel icon media entries
    put_item "$MEDIA_TABLE" '{
        "id": {"S": "50000000-0000-0000-0000-********0001"},
        "fileName": {"S": "fortnite.png"},
        "fileType": {"S": "image/png"},
        "fileSize": {"N": "32768"},
        "mediaType": {"S": "image"},
        "userId": {"S": "system"},
        "s3Key": {"S": "media/channels/40000000-0000-0000-0000-********0001/icon/fortnite.png"},
        "bucketName": {"S": "'$S3_BUCKET_NAME'"},
        "url": {"S": "'$MEDIA_BASE_URL'/media/channels/40000000-0000-0000-0000-********0001/icon/fortnite.png"},
        "status": {"S": "approved"},
        "staging_s3_key": {"S": "staging/media/system/50000000-0000-0000-0000-********0001.png"},
        "final_s3_key": {"S": "media/channels/40000000-0000-0000-0000-********0001/icon/fortnite.png"},
        "final_url": {"S": "'$MEDIA_BASE_URL'/media/channels/40000000-0000-0000-0000-********0001/icon/fortnite.png"},
        "createdAt": {"S": "2024-12-28T10:00:00Z"},
        "updatedAt": {"S": "2024-12-28T10:00:00Z"}
    }' "fortnite channel icon"

    put_item "$MEDIA_TABLE" '{
        "id": {"S": "50000000-0000-0000-0000-********0002"},
        "fileName": {"S": "valorant.png"},
        "fileType": {"S": "image/png"},
        "fileSize": {"N": "32768"},
        "mediaType": {"S": "image"},
        "userId": {"S": "system"},
        "s3Key": {"S": "media/channels/40000000-0000-0000-0000-********0002/icon/valorant.png"},
        "bucketName": {"S": "'$S3_BUCKET_NAME'"},
        "url": {"S": "'$MEDIA_BASE_URL'/media/channels/40000000-0000-0000-0000-********0002/icon/valorant.png"},
        "status": {"S": "approved"},
        "staging_s3_key": {"S": "staging/media/system/50000000-0000-0000-0000-********0002.png"},
        "final_s3_key": {"S": "media/channels/40000000-0000-0000-0000-********0002/icon/valorant.png"},
        "final_url": {"S": "'$MEDIA_BASE_URL'/media/channels/40000000-0000-0000-0000-********0002/icon/valorant.png"},
        "createdAt": {"S": "2024-12-28T11:00:00Z"},
        "updatedAt": {"S": "2024-12-28T11:00:00Z"}
    }' "valorant channel icon"

    put_item "$MEDIA_TABLE" '{
        "id": {"S": "50000000-0000-0000-0000-********0003"},
        "fileName": {"S": "minecraft.png"},
        "fileType": {"S": "image/png"},
        "fileSize": {"N": "32768"},
        "mediaType": {"S": "image"},
        "userId": {"S": "system"},
        "s3Key": {"S": "media/channels/40000000-0000-0000-0000-********0003/icon/minecraft.png"},
        "bucketName": {"S": "'$S3_BUCKET_NAME'"},
        "url": {"S": "'$MEDIA_BASE_URL'/media/channels/40000000-0000-0000-0000-********0003/icon/minecraft.png"},
        "status": {"S": "approved"},
        "staging_s3_key": {"S": "staging/media/system/50000000-0000-0000-0000-********0003.png"},
        "final_s3_key": {"S": "media/channels/40000000-0000-0000-0000-********0003/icon/minecraft.png"},
        "final_url": {"S": "'$MEDIA_BASE_URL'/media/channels/40000000-0000-0000-0000-********0003/icon/minecraft.png"},
        "createdAt": {"S": "2024-12-28T12:00:00Z"},
        "updatedAt": {"S": "2024-12-28T12:00:00Z"}
    }' "minecraft channel icon"

    put_item "$MEDIA_TABLE" '{
        "id": {"S": "50000000-0000-0000-0000-********0004"},
        "fileName": {"S": "league_of_legends.png"},
        "fileType": {"S": "image/png"},
        "fileSize": {"N": "32768"},
        "mediaType": {"S": "image"},
        "userId": {"S": "system"},
        "s3Key": {"S": "media/channels/40000000-0000-0000-0000-********0004/icon/league_of_legends.png"},
        "bucketName": {"S": "'$S3_BUCKET_NAME'"},
        "url": {"S": "'$MEDIA_BASE_URL'/media/channels/40000000-0000-0000-0000-********0004/icon/league_of_legends.png"},
        "status": {"S": "approved"},
        "staging_s3_key": {"S": "staging/media/system/50000000-0000-0000-0000-********0004.png"},
        "final_s3_key": {"S": "media/channels/40000000-0000-0000-0000-********0004/icon/league_of_legends.png"},
        "final_url": {"S": "'$MEDIA_BASE_URL'/media/channels/40000000-0000-0000-0000-********0004/icon/league_of_legends.png"},
        "createdAt": {"S": "2024-12-28T13:00:00Z"},
        "updatedAt": {"S": "2024-12-28T13:00:00Z"}
    }' "league of legends channel icon"

    put_item "$MEDIA_TABLE" '{
        "id": {"S": "50000000-0000-0000-0000-********0005"},
        "fileName": {"S": "call_of_duty.png"},
        "fileType": {"S": "image/png"},
        "fileSize": {"N": "32768"},
        "mediaType": {"S": "image"},
        "userId": {"S": "system"},
        "s3Key": {"S": "media/channels/40000000-0000-0000-0000-********0005/icon/call_of_duty.png"},
        "bucketName": {"S": "'$S3_BUCKET_NAME'"},
        "url": {"S": "'$MEDIA_BASE_URL'/media/channels/40000000-0000-0000-0000-********0005/icon/call_of_duty.png"},
        "status": {"S": "approved"},
        "staging_s3_key": {"S": "staging/media/system/50000000-0000-0000-0000-********0005.png"},
        "final_s3_key": {"S": "media/channels/40000000-0000-0000-0000-********0005/icon/call_of_duty.png"},
        "final_url": {"S": "'$MEDIA_BASE_URL'/media/channels/40000000-0000-0000-0000-********0005/icon/call_of_duty.png"},
        "createdAt": {"S": "2024-12-28T14:00:00Z"},
        "updatedAt": {"S": "2024-12-28T14:00:00Z"}
    }' "call of duty channel icon"

    print_status "Media seeded successfully!"
}

# Seed Posts table (using new structure with media_id references)
seed_posts() {
    print_header "Seeding Posts table..."

    put_item "$POSTS_TABLE" '{
        "id": {"S": "20000000-0000-0000-0000-********0001"},
        "authorId": {"S": "********-0000-0000-0000-********0003"},
        "userId": {"S": "********-0000-0000-0000-********0003"},
        "content": {"S": "Just had an epic Call of Duty session! Check out this clutch moment 🎮🔥"},
        "mediaId": {"S": "30000000-0000-0000-0000-********0001"},
        "likes": {"N": "12"},
        "comments": {"N": "4"},
        "status": {"S": "published"},
        "active": {"BOOL": true},
        "createdAt": {"S": "2024-12-28T14:30:00Z"},
        "updatedAt": {"S": "2024-12-28T14:30:00Z"}
    }' "john cod post"

    put_item "$POSTS_TABLE" '{
        "id": {"S": "20000000-0000-0000-0000-********0002"},
        "authorId": {"S": "********-0000-0000-0000-********0004"},
        "userId": {"S": "********-0000-0000-0000-********0004"},
        "content": {"S": "Finally defeated this boss in Diablo! The loot was totally worth the grind 💀⚔️"},
        "mediaId": {"S": "30000000-0000-0000-0000-********0002"},
        "likes": {"N": "18"},
        "comments": {"N": "6"},
        "status": {"S": "published"},
        "active": {"BOOL": true},
        "createdAt": {"S": "2024-12-27T16:45:00Z"},
        "updatedAt": {"S": "2024-12-27T16:45:00Z"}
    }' "jane diablo post"

    # Add a text-only post
    put_item "$POSTS_TABLE" '{
        "id": {"S": "20000000-0000-0000-0000-********0003"},
        "authorId": {"S": "********-0000-0000-0000-********0005"},
        "userId": {"S": "********-0000-0000-0000-********0005"},
        "content": {"S": "Anyone else excited for the new gaming releases this month? What are you most looking forward to? 🎮"},
        "likes": {"N": "5"},
        "comments": {"N": "2"},
        "status": {"S": "published"},
        "active": {"BOOL": true},
        "createdAt": {"S": "2024-12-26T10:15:00Z"},
        "updatedAt": {"S": "2024-12-26T10:15:00Z"}
    }' "mike text post"

    print_status "Posts seeded successfully!"
}

# Seed Comments table
seed_comments() {
    print_header "Seeding Comments table..."

    put_item "$COMMENTS_TABLE" '{
        "id": {"S": "40000000-0000-0000-0000-********0001"},
        "postId": {"S": "20000000-0000-0000-0000-********0001"},
        "userId": {"S": "********-0000-0000-0000-********0004"},
        "content": {"S": "Nice clutch! What loadout were you using?"},
        "likeCount": {"N": "3"},
        "isActive": {"BOOL": true},
        "createdAt": {"S": "2024-12-28T15:15:00Z"},
        "updatedAt": {"S": "2024-12-28T15:15:00Z"}
    }' "comment 1"

    put_item "$COMMENTS_TABLE" '{
        "id": {"S": "40000000-0000-0000-0000-********0002"},
        "postId": {"S": "20000000-0000-0000-0000-********0001"},
        "userId": {"S": "********-0000-0000-0000-********0005"},
        "content": {"S": "That was insane! 🔥"},
        "likeCount": {"N": "1"},
        "isActive": {"BOOL": true},
        "createdAt": {"S": "2024-12-28T16:20:00Z"},
        "updatedAt": {"S": "2024-12-28T16:20:00Z"}
    }' "comment 2"

    put_item "$COMMENTS_TABLE" '{
        "id": {"S": "40000000-0000-0000-0000-********0003"},
        "postId": {"S": "20000000-0000-0000-0000-********0002"},
        "userId": {"S": "********-0000-0000-0000-********0003"},
        "content": {"S": "Congrats! What difficulty level?"},
        "likeCount": {"N": "2"},
        "isActive": {"BOOL": true},
        "createdAt": {"S": "2024-12-27T17:30:00Z"},
        "updatedAt": {"S": "2024-12-27T17:30:00Z"}
    }' "comment 3"

    put_item "$COMMENTS_TABLE" '{
        "id": {"S": "40000000-0000-0000-0000-********0004"},
        "postId": {"S": "20000000-0000-0000-0000-********0002"},
        "userId": {"S": "********-0000-0000-0000-********0005"},
        "content": {"S": "The loot looks amazing! 💎"},
        "likeCount": {"N": "4"},
        "isActive": {"BOOL": true},
        "createdAt": {"S": "2024-12-27T18:15:00Z"},
        "updatedAt": {"S": "2024-12-27T18:15:00Z"}
    }' "comment 4"

    put_item "$COMMENTS_TABLE" '{
        "id": {"S": "40000000-0000-0000-0000-********0005"},
        "postId": {"S": "20000000-0000-0000-0000-********0003"},
        "userId": {"S": "********-0000-0000-0000-********0003"},
        "content": {"S": "I am really excited for the new RPG releases!"},
        "likeCount": {"N": "1"},
        "isActive": {"BOOL": true},
        "createdAt": {"S": "2024-12-26T11:00:00Z"},
        "updatedAt": {"S": "2024-12-26T11:00:00Z"}
    }' "comment 5"

    print_status "Comments seeded successfully!"
}

# Seed Likes table
seed_likes() {
    print_header "Seeding Likes table..."

    # Likes for post 1 (John's COD post)
    put_item "$LIKES_TABLE" '{
        "postId": {"S": "20000000-0000-0000-0000-********0001"},
        "userId": {"S": "********-0000-0000-0000-********0004"},
        "createdAt": {"S": "2024-12-28T14:35:00Z"}
    }' "like 1"

    put_item "$LIKES_TABLE" '{
        "postId": {"S": "20000000-0000-0000-0000-********0001"},
        "userId": {"S": "********-0000-0000-0000-********0005"},
        "createdAt": {"S": "2024-12-28T14:40:00Z"}
    }' "like 2"

    # Get dev user ID for likes
    local dev_user_id=$(get_cognito_user_id "<EMAIL>" "$USER_POOL_ID")

    put_item "$LIKES_TABLE" '{
        "postId": {"S": "20000000-0000-0000-0000-********0001"},
        "userId": {"S": "'$dev_user_id'"},
        "createdAt": {"S": "2024-12-28T14:45:00Z"}
    }' "like 3 (dev user likes post 1)"



    # Likes for post 2 (Jane's Diablo post)
    put_item "$LIKES_TABLE" '{
        "postId": {"S": "20000000-0000-0000-0000-********0002"},
        "userId": {"S": "********-0000-0000-0000-********0003"},
        "createdAt": {"S": "2024-12-27T16:50:00Z"}
    }' "like 4"

    put_item "$LIKES_TABLE" '{
        "postId": {"S": "20000000-0000-0000-0000-********0002"},
        "userId": {"S": "********-0000-0000-0000-********0005"},
        "createdAt": {"S": "2024-12-27T17:00:00Z"}
    }' "like 5"

    put_item "$LIKES_TABLE" '{
        "postId": {"S": "20000000-0000-0000-0000-********0002"},
        "userId": {"S": "'$dev_user_id'"},
        "createdAt": {"S": "2024-12-27T17:15:00Z"}
    }' "like 6 (dev user likes post 2)"

    # Likes for post 3 (Mike's text post)
    put_item "$LIKES_TABLE" '{
        "postId": {"S": "20000000-0000-0000-0000-********0003"},
        "userId": {"S": "********-0000-0000-0000-********0003"},
        "createdAt": {"S": "2024-12-26T10:30:00Z"}
    }' "like 7"

    put_item "$LIKES_TABLE" '{
        "postId": {"S": "20000000-0000-0000-0000-********0003"},
        "userId": {"S": "********-0000-0000-0000-********0004"},
        "createdAt": {"S": "2024-12-26T11:15:00Z"}
    }' "like 8"

    print_status "Likes seeded successfully!"
}

# Seed Follows table
seed_follows() {
    print_header "Seeding Follows table..."

    # Get dev user ID for follows
    local dev_user_id=$(get_cognito_user_id "<EMAIL>" "$USER_POOL_ID")

    put_item "$FOLLOWS_TABLE" '{
        "followerId": {"S": "'$dev_user_id'"},
        "followingId": {"S": "550e8400-e29b-41d4-a716-************"},
        "createdAt": {"S": "2024-01-01T00:00:00Z"}
    }' "dev follows admin"

    put_item "$FOLLOWS_TABLE" '{
        "followerId": {"S": "********-0000-0000-0000-********0003"},
        "followingId": {"S": "********-0000-0000-0000-********0004"},
        "createdAt": {"S": "2024-01-02T00:00:00Z"}
    }' "john follows jane"

    put_item "$FOLLOWS_TABLE" '{
        "followerId": {"S": "********-0000-0000-0000-********0004"},
        "followingId": {"S": "********-0000-0000-0000-********0005"},
        "createdAt": {"S": "2024-01-03T00:00:00Z"}
    }' "jane follows mike"

    put_item "$FOLLOWS_TABLE" '{
        "followerId": {"S": "********-0000-0000-0000-********0005"},
        "followingId": {"S": "'$dev_user_id'"},
        "createdAt": {"S": "2024-01-04T00:00:00Z"}
    }' "mike follows dev"

    put_item "$FOLLOWS_TABLE" '{
        "followerId": {"S": "********-0000-0000-0000-********0003"},
        "followingId": {"S": "********-0000-0000-0000-********0005"},
        "createdAt": {"S": "2024-01-05T00:00:00Z"}
    }' "john follows mike"

    print_status "Follows seeded successfully!"
}

# Upload media files to S3
upload_media_files_to_s3() {
    print_header "Uploading media files to S3..."

    # Function to upload file to S3
    upload_s3_file() {
        local file_path=$1
        local s3_key=$2
        local content_type=$3

        if [ ! -f "$file_path" ]; then
            print_warning "Media file not found: $file_path (skipping)"
            return 0
        fi

        local aws_region=${AWS_REGION:-us-west-2}

        # Check if file already exists in S3
        if aws s3 ls "s3://$S3_BUCKET_NAME/$s3_key" --region "$aws_region" 2>/dev/null; then
            print_status "File $s3_key already exists in S3, skipping"
            return 0
        fi

        print_status "Uploading $file_path to S3: s3://$S3_BUCKET_NAME/$s3_key"

        if aws s3 cp "$file_path" "s3://$S3_BUCKET_NAME/$s3_key" \
            --region "$aws_region" \
            --content-type "$content_type" > /dev/null 2>&1; then
            print_status "✅ Successfully uploaded $s3_key to S3"
        else
            print_warning "❌ Failed to upload $s3_key to S3 (continuing anyway)"
        fi
    }

    # Get the script directory to find assets
    SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
    ASSETS_DIR="$(dirname "$SCRIPT_DIR")/assets/media"

    # Upload media files for seeded posts
    print_status "Looking for media files in: $ASSETS_DIR"

    upload_s3_file \
        "$ASSETS_DIR/cod_screenshot.jpg" \
        "media/user/********-0000-0000-0000-********0003/cod_screenshot.jpg" \
        "image/jpeg"

    upload_s3_file \
        "$ASSETS_DIR/diablo_screenshot.webp" \
        "media/user/********-0000-0000-0000-********0004/diablo_screenshot.webp" \
        "image/webp"

    # Upload channel icons
    print_status "Uploading channel icons..."

    upload_s3_file \
        "$ASSETS_DIR/channels/icons/fortnite.png" \
        "media/channels/40000000-0000-0000-0000-********0001/icon/fortnite.png" \
        "image/png"

    upload_s3_file \
        "$ASSETS_DIR/channels/icons/valorant.png" \
        "media/channels/40000000-0000-0000-0000-********0002/icon/valorant.png" \
        "image/png"

    upload_s3_file \
        "$ASSETS_DIR/channels/icons/minecraft.png" \
        "media/channels/40000000-0000-0000-0000-********0003/icon/minecraft.png" \
        "image/png"

    upload_s3_file \
        "$ASSETS_DIR/channels/icons/league_of_legends.png" \
        "media/channels/40000000-0000-0000-0000-********0004/icon/league_of_legends.png" \
        "image/png"

    upload_s3_file \
        "$ASSETS_DIR/channels/icons/call_of_duty.png" \
        "media/channels/40000000-0000-0000-0000-********0005/icon/call_of_duty.png" \
        "image/png"

    print_status "S3 media upload completed!"
}

# Seed Channels table
seed_channels() {
    print_header "Seeding Channels table..."

    put_item "$CHANNELS_TABLE" '{
        "id": {"S": "40000000-0000-0000-0000-********0001"},
        "name": {"S": "Fortnite"},
        "description": {"S": "Tips, tricks, and epic moments from the world of Fortnite"},
        "ownerId": {"S": "********-0000-0000-0000-********0003"},
        "isPublic": {"BOOL": true},
        "isActive": {"BOOL": true},
        "memberCount": {"N": "3"},
        "iconMediaId": {"S": "50000000-0000-0000-0000-********0001"},
        "createdAt": {"S": "2024-12-28T10:00:00Z"},
        "updatedAt": {"S": "2024-12-28T10:00:00Z"}
    }' "fortnite channel"

    put_item "$CHANNELS_TABLE" '{
        "id": {"S": "40000000-0000-0000-0000-********0002"},
        "name": {"S": "Valorant"},
        "description": {"S": "Tactical FPS discussions and strategies"},
        "ownerId": {"S": "********-0000-0000-0000-********0004"},
        "isPublic": {"BOOL": true},
        "isActive": {"BOOL": true},
        "memberCount": {"N": "2"},
        "iconMediaId": {"S": "50000000-0000-0000-0000-********0002"},
        "createdAt": {"S": "2024-12-28T11:00:00Z"},
        "updatedAt": {"S": "2024-12-28T11:00:00Z"}
    }' "valorant channel"

    put_item "$CHANNELS_TABLE" '{
        "id": {"S": "40000000-0000-0000-0000-********0003"},
        "name": {"S": "Minecraft"},
        "description": {"S": "Creative builds and survival tips"},
        "ownerId": {"S": "********-0000-0000-0000-********0005"},
        "isPublic": {"BOOL": true},
        "isActive": {"BOOL": true},
        "memberCount": {"N": "4"},
        "iconMediaId": {"S": "50000000-0000-0000-0000-********0003"},
        "createdAt": {"S": "2024-12-28T12:00:00Z"},
        "updatedAt": {"S": "2024-12-28T12:00:00Z"}
    }' "minecraft channel"

    put_item "$CHANNELS_TABLE" '{
        "id": {"S": "40000000-0000-0000-0000-********0004"},
        "name": {"S": "League of Legends"},
        "description": {"S": "MOBA strategies and esports news"},
        "ownerId": {"S": "********-0000-0000-0000-********0006"},
        "isPublic": {"BOOL": true},
        "isActive": {"BOOL": true},
        "memberCount": {"N": "2"},
        "iconMediaId": {"S": "50000000-0000-0000-0000-********0004"},
        "createdAt": {"S": "2024-12-28T13:00:00Z"},
        "updatedAt": {"S": "2024-12-28T13:00:00Z"}
    }' "league of legends channel"

    put_item "$CHANNELS_TABLE" '{
        "id": {"S": "40000000-0000-0000-0000-********0005"},
        "name": {"S": "Call of Duty"},
        "description": {"S": "Tactical warfare and epic multiplayer moments"},
        "ownerId": {"S": "********-0000-0000-0000-********0007"},
        "isPublic": {"BOOL": true},
        "isActive": {"BOOL": true},
        "memberCount": {"N": "3"},
        "iconMediaId": {"S": "50000000-0000-0000-0000-********0005"},
        "createdAt": {"S": "2024-12-28T14:00:00Z"},
        "updatedAt": {"S": "2024-12-28T14:00:00Z"}
    }' "call of duty channel"

    print_status "Channels seeded successfully!"
}

# Seed Channel Members table
seed_channel_members() {
    print_header "Seeding Channel Members table..."

    # Fortnite channel members
    put_item "$CHANNEL_MEMBERS_TABLE" '{
        "channelId": {"S": "40000000-0000-0000-0000-********0001"},
        "userId": {"S": "********-0000-0000-0000-********0003"},
        "role": {"S": "owner"},
        "joined_at": {"S": "2024-12-28T10:00:00Z"}
    }' "john as fortnite owner"

    put_item "$CHANNEL_MEMBERS_TABLE" '{
        "channelId": {"S": "40000000-0000-0000-0000-********0001"},
        "userId": {"S": "********-0000-0000-0000-********0004"},
        "role": {"S": "member"},
        "joined_at": {"S": "2024-12-28T10:30:00Z"}
    }' "jane in fortnite"

    put_item "$CHANNEL_MEMBERS_TABLE" '{
        "channelId": {"S": "40000000-0000-0000-0000-********0001"},
        "userId": {"S": "********-0000-0000-0000-********0005"},
        "role": {"S": "member"},
        "joined_at": {"S": "2024-12-28T11:00:00Z"}
    }' "mike in fortnite"

    # Valorant channel members
    put_item "$CHANNEL_MEMBERS_TABLE" '{
        "channelId": {"S": "40000000-0000-0000-0000-********0002"},
        "userId": {"S": "********-0000-0000-0000-********0004"},
        "role": {"S": "owner"},
        "joined_at": {"S": "2024-12-28T11:00:00Z"}
    }' "jane as valorant owner"

    put_item "$CHANNEL_MEMBERS_TABLE" '{
        "channelId": {"S": "40000000-0000-0000-0000-********0002"},
        "userId": {"S": "********-0000-0000-0000-********0006"},
        "role": {"S": "member"},
        "joined_at": {"S": "2024-12-28T11:30:00Z"}
    }' "alice in valorant"

    # Minecraft channel members
    put_item "$CHANNEL_MEMBERS_TABLE" '{
        "channelId": {"S": "40000000-0000-0000-0000-********0003"},
        "userId": {"S": "********-0000-0000-0000-********0005"},
        "role": {"S": "owner"},
        "joined_at": {"S": "2024-12-28T12:00:00Z"}
    }' "mike as minecraft owner"

    put_item "$CHANNEL_MEMBERS_TABLE" '{
        "channelId": {"S": "40000000-0000-0000-0000-********0003"},
        "userId": {"S": "********-0000-0000-0000-********0003"},
        "role": {"S": "member"},
        "joined_at": {"S": "2024-12-28T12:15:00Z"}
    }' "john in minecraft"

    put_item "$CHANNEL_MEMBERS_TABLE" '{
        "channelId": {"S": "40000000-0000-0000-0000-********0003"},
        "userId": {"S": "********-0000-0000-0000-********0007"},
        "role": {"S": "member"},
        "joined_at": {"S": "2024-12-28T12:30:00Z"}
    }' "bob in minecraft"

    put_item "$CHANNEL_MEMBERS_TABLE" '{
        "channelId": {"S": "40000000-0000-0000-0000-********0003"},
        "userId": {"S": "********-0000-0000-0000-********0008"},
        "role": {"S": "member"},
        "joined_at": {"S": "2024-12-28T12:45:00Z"}
    }' "charlie in minecraft"

    # League of Legends channel members
    put_item "$CHANNEL_MEMBERS_TABLE" '{
        "channelId": {"S": "40000000-0000-0000-0000-********0004"},
        "userId": {"S": "********-0000-0000-0000-********0006"},
        "role": {"S": "owner"},
        "joined_at": {"S": "2024-12-28T13:00:00Z"}
    }' "alice as lol owner"

    put_item "$CHANNEL_MEMBERS_TABLE" '{
        "channelId": {"S": "40000000-0000-0000-0000-********0004"},
        "userId": {"S": "********-0000-0000-0000-********0008"},
        "role": {"S": "member"},
        "joined_at": {"S": "2024-12-28T13:30:00Z"}
    }' "charlie in lol"

    # Call of Duty channel members
    put_item "$CHANNEL_MEMBERS_TABLE" '{
        "channelId": {"S": "40000000-0000-0000-0000-********0005"},
        "userId": {"S": "********-0000-0000-0000-********0007"},
        "role": {"S": "owner"},
        "joined_at": {"S": "2024-12-28T14:00:00Z"}
    }' "bob as call of duty owner"

    put_item "$CHANNEL_MEMBERS_TABLE" '{
        "channelId": {"S": "40000000-0000-0000-0000-********0005"},
        "userId": {"S": "********-0000-0000-0000-********0004"},
        "role": {"S": "member"},
        "joined_at": {"S": "2024-12-28T14:15:00Z"}
    }' "jane in call of duty"

    put_item "$CHANNEL_MEMBERS_TABLE" '{
        "channelId": {"S": "40000000-0000-0000-0000-********0005"},
        "userId": {"S": "********-0000-0000-0000-********0005"},
        "role": {"S": "member"},
        "joined_at": {"S": "2024-12-28T14:30:00Z"}
    }' "mike in call of duty"

    print_status "Channel members seeded successfully!"
}

# Print summary
print_summary() {
    print_status "Summary:"
    print_status "  📊 Seeded 5 users with profiles"
    print_status "  📝 Seeded 3 posts (2 with media, 1 text-only)"
    print_status "  💬 Seeded 5 comments"
    print_status "  ❤️  Seeded 8 likes"
    print_status "  👥 Seeded 5 follow relationships"
    print_status "  🎮 Seeded 5 gaming channels"
    print_status "  👤 Seeded 13 channel memberships"
    print_status "  📸 Seeded 7 media items (2 post media + 5 channel icons)"
    print_status "  🔗 Uploaded media files to S3"
    print_status ""
    print_status "Your GameFlex backend is now ready for testing!"
    print_status "API Gateway URL: Check your CDK deployment outputs"
    print_status ""
    print_status "S3 Media Configuration:"
    print_status "  Bucket: $S3_BUCKET_NAME"
    print_status "  Media URL: $MEDIA_BASE_URL"
    print_status ""
    print_status "Test the posts endpoint:"
    print_status "  curl https://your-api-gateway-url/posts"
}

# Check if jq is installed
if ! command -v jq &> /dev/null; then
    print_error "jq is required but not installed. Please install jq first."
    print_error "Ubuntu/Debian: sudo apt-get install jq"
    print_error "macOS: brew install jq"
    exit 1
fi

# Run main function if script is executed directly
if [[ "${BASH_SOURCE[0]}" == "${0}" ]]; then
    main "$@"
fi
