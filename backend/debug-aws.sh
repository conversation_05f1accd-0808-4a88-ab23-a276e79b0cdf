#!/bin/bash

# Debug script to help troubleshoot AWS connection issues

echo "🔍 AWS Connection Debug Information"
echo "=================================="

# Check AWS CLI installation
echo "1. AWS CLI Version:"
if command -v aws >/dev/null 2>&1; then
    aws --version
else
    echo "❌ AWS CLI not installed"
    exit 1
fi

echo ""
echo "2. AWS Configuration:"
aws configure list

echo ""
echo "3. AWS Credentials Test:"
if aws sts get-caller-identity >/dev/null 2>&1; then
    echo "✅ AWS credentials are working"
    aws sts get-caller-identity --output table
else
    echo "❌ AWS credentials test failed"
    echo ""
    echo "Troubleshooting steps:"
    echo "1. Run: aws configure"
    echo "2. Or set environment variables:"
    echo "   export AWS_ACCESS_KEY_ID=your_key"
    echo "   export AWS_SECRET_ACCESS_KEY=your_secret"
    echo "   export AWS_DEFAULT_REGION=us-west-2"
fi

echo ""
echo "4. Environment Variables:"
echo "AWS_ACCESS_KEY_ID: ${AWS_ACCESS_KEY_ID:+SET}"
echo "AWS_SECRET_ACCESS_KEY: ${AWS_SECRET_ACCESS_KEY:+SET}"
echo "AWS_DEFAULT_REGION: ${AWS_DEFAULT_REGION:-NOT_SET}"
echo "AWS_REGION: ${AWS_REGION:-NOT_SET}"

echo ""
echo "5. Testing specific region (us-west-2):"
if aws sts get-caller-identity --region us-west-2 >/dev/null 2>&1; then
    echo "✅ us-west-2 region accessible"
else
    echo "❌ us-west-2 region not accessible"
fi

echo ""
echo "6. Available regions:"
aws ec2 describe-regions --output table --query 'Regions[?RegionName==`us-west-2` || RegionName==`us-east-1`]' 2>/dev/null || echo "❌ Cannot list regions"

echo ""
echo "7. Secrets Manager test (us-west-2):"
if aws secretsmanager list-secrets --region us-west-2 --max-items 1 >/dev/null 2>&1; then
    echo "✅ Secrets Manager accessible in us-west-2"
else
    echo "❌ Secrets Manager not accessible in us-west-2"
fi
