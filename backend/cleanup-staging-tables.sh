#!/bin/bash

# Cleanup staging DynamoDB tables
export AWS_ACCESS_KEY_ID=********************
export AWS_SECRET_ACCESS_KEY=An5pe0X0vdyPZXWIRbIYu/dcYdBd4o/BMGnp1yke
export AWS_DEFAULT_REGION=us-west-2

tables=(
    "gameflex-staging-ChannelMembers"
    "gameflex-staging-Channels"
    "gameflex-staging-Comments"
    "gameflex-staging-Follows"
    "gameflex-staging-Likes"
    "gameflex-staging-Media"
    "gameflex-staging-Posts"
    "gameflex-staging-Reflexes"
    "gameflex-staging-UserProfiles"
)

echo "Disabling deletion protection for all staging tables..."
for table in "${tables[@]}"; do
    echo "Processing $table..."
    aws dynamodb update-table --table-name "$table" --no-deletion-protection-enabled --region us-west-2 > /dev/null 2>&1
    if [ $? -eq 0 ]; then
        echo "✅ Disabled deletion protection for $table"
    else
        echo "❌ Failed to disable deletion protection for $table"
    fi
done

echo ""
echo "Deleting all staging tables..."
for table in "${tables[@]}"; do
    echo "Deleting $table..."
    aws dynamodb delete-table --table-name "$table" --region us-west-2 > /dev/null 2>&1
    if [ $? -eq 0 ]; then
        echo "✅ Deletion initiated for $table"
    else
        echo "❌ Failed to delete $table"
    fi
done

echo ""
echo "All table deletions initiated. Tables will be deleted in the background."
