import { getHttpClient } from './utils/http-client';
import { HealthResponse } from './types/api.types';

describe('Health API Tests', () => {
  let httpClient: ReturnType<typeof getHttpClient>;

  beforeAll(() => {
    // Initialize HTTP client if not already done
    try {
      httpClient = getHttpClient();
    } catch (error) {
      // Initialize if not already initialized
      const { initializeHttpClient } = require('./utils/http-client');
      const { testConfig } = require('./config/test.config');
      httpClient = initializeHttpClient({
        baseURL: testConfig.apiBaseUrl,
        timeout: testConfig.timeout,
      });
    }

    if (process.env.VERBOSE_TESTS === 'true') {
      console.log('🏥 Starting Health API tests...');
    }
  });

  describe('GET /health', () => {
    it('should return health status successfully', async () => {
      const response = await httpClient.get<HealthResponse>('/health');

      expect(response).toBeValidApiResponse();
      expect(response.status).toBe(200);
      expect(response.data.status).toBe('healthy');
      expect(response.data.timestamp).toBeDefined();
      expect(response.data.environment).toBeDefined();
      expect(response.data.version).toBeDefined();
      expect(response.data.services).toBeDefined();
      expect(response.data.services.api).toBe('healthy');
      expect(response.data.uptime).toBeDefined();
      expect(response.data.memory).toBeDefined();
      expect(typeof response.data.timestamp).toBe('string');
      expect(typeof response.data.environment).toBe('string');
      expect(typeof response.data.version).toBe('string');
      expect(typeof response.data.uptime).toBe('number');
    });

    it('should not require authentication', async () => {
      // Ensure no auth token is set
      httpClient.removeAuthToken();

      const response = await httpClient.get<HealthResponse>('/health');

      expect(response).toBeValidApiResponse();
      expect(response.status).toBe(200);
      expect(response.data.status).toBe('healthy');
    });
  });
});
