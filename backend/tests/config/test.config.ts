import { TestConfig } from '../types/api.types';
import * as dotenv from 'dotenv';

// Load test environment variables
dotenv.config({ path: '.env.test' });

// Load environment variables
const getEnvVar = (name: string, defaultValue?: string): string => {
  const value = process.env[name] || defaultValue;
  if (!value) {
    throw new Error(`Environment variable ${name} is required`);
  }
  return value;
};

// Generate unique identifiers for this test run
const timestamp = Date.now();
const randomId = Math.random().toString(36).substring(2, 8);
const uniqueId = `${timestamp}-${randomId}`;

export const testConfig: TestConfig = {
  // API Configuration - using custom domain without /v1
  apiBaseUrl: getEnvVar('TEST_API_BASE_URL', 'https://dev.api.gameflex.io'),

  // Test User Configuration - Always unique for each test run
  testUserEmail: `test-user-${uniqueId}@example.com`,
  testUserPassword: getEnvVar('TEST_USER_PASSWORD', 'TestPassword123!'),
  testUserUsername: `testuser${uniqueId}`,
  testUserFirstName: getEnvVar('TEST_USER_FIRST_NAME', 'Test'),
  testUserLastName: getEnvVar('TEST_USER_LAST_NAME', 'User'),

  // Test Configuration
  timeout: parseInt(getEnvVar('TEST_TIMEOUT', '30000'), 10),
};

// Validate configuration
export function validateTestConfig(): void {
  // Always show config for debugging purposes
  console.log('🔧 Test Configuration:');
  console.log(`  API Base URL: ${testConfig.apiBaseUrl}`);
  console.log(`  Test User Email: ${testConfig.testUserEmail}`);
  console.log(`  Test User Username: ${testConfig.testUserUsername}`);
  console.log(`  Timeout: ${testConfig.timeout}ms`);

  // Basic validation
  if (!testConfig.apiBaseUrl.startsWith('http')) {
    throw new Error('TEST_API_BASE_URL must be a valid HTTP URL');
  }

  if (!testConfig.testUserEmail.includes('@')) {
    throw new Error('TEST_USER_EMAIL must be a valid email address');
  }

  if (testConfig.testUserPassword.length < 8) {
    throw new Error('TEST_USER_PASSWORD must be at least 8 characters long');
  }

  console.log('✅ Test configuration validated');
}
