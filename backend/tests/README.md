# TypeScript API Tests

This directory contains comprehensive TypeScript tests for the GameFlex backend API.

## Features

- **TypeScript**: Full type safety and IntelliSense support
- **Auth-First Testing**: Authentication tests always run first to create a test user
- **Shared Test Context**: Test user and tokens are shared across all test suites
- **HTTP Client**: Axios-based client with request/response logging
- **Test Sequencing**: Custom Jest sequencer ensures proper test execution order

## Structure

```
tests/
├── auth.test.ts              # Authentication tests (runs first)
├── posts.test.ts             # Posts API tests (example)
├── config/
│   └── test.config.ts        # Test configuration
├── setup/
│   ├── global.setup.ts       # Global test setup
│   ├── global.teardown.ts    # Global test teardown
│   ├── jest.setup.ts         # Jest configuration
│   └── test-sequencer.ts     # Custom test sequencer
├── types/
│   └── api.types.ts          # TypeScript type definitions
└── utils/
    ├── http-client.ts        # HTTP client for API requests
    └── test-context.ts       # Global test context manager
```

## Setup

1. **Configure Environment**: Update `.env.test` with your API Gateway URL:
   ```bash
   TEST_API_BASE_URL=https://dev.api.gameflex.io
   ```

2. **Install Dependencies**: Dependencies are already installed with the main project.

## Running Tests

```bash
# Run all tests (auth runs first automatically)
npm test

# Run only auth tests
npm run test:auth

# Run specific test file
npm run test:posts

# Run tests in watch mode
npm run test:watch

# Run tests with coverage
npm run test:coverage

# Run old JavaScript tests
npm run test:old
```

## Test Flow

1. **Global Setup**: Validates configuration and initializes HTTP client
2. **Auth Tests**: Creates a test user and signs them in
3. **Other Tests**: Use the authenticated test user for API calls
4. **Global Teardown**: Cleans up test context

## Key Features

### Shared Test User

The auth tests create a test user that is automatically available to all subsequent tests:

```typescript
import { TestContext } from './utils/test-context';

// Get the authenticated test user
const testUser = TestContext.getTestUser();
const accessToken = TestContext.getAccessToken();
```

### HTTP Client

All tests use a shared HTTP client with automatic logging:

```typescript
import { getHttpClient } from './utils/http-client';

const httpClient = getHttpClient();
const response = await httpClient.post('/posts', postData);
```

### Type Safety

Full TypeScript support with API response types:

```typescript
import { SignInResponse } from './types/api.types';

const response = await httpClient.post<SignInResponse>('/auth/signin', credentials);
// response.data is fully typed
```

## Adding New Tests

1. Create a new test file (e.g., `users.test.ts`)
2. Import required utilities and types
3. Use the shared test context for authentication
4. Follow the existing patterns for HTTP requests

Example:

```typescript
import { getHttpClient } from './utils/http-client';
import { TestContext } from './utils/test-context';

describe('Users API Tests', () => {
  let httpClient: ReturnType<typeof getHttpClient>;

  beforeAll(() => {
    if (!TestContext.isSetupComplete()) {
      throw new Error('Auth tests must run first');
    }
    
    httpClient = getHttpClient();
    httpClient.setAuthToken(TestContext.getAccessToken());
  });

  it('should get user profile', async () => {
    const response = await httpClient.get('/users/me');
    expect(response.status).toBe(200);
  });
});
```

## Environment Variables

- `TEST_API_BASE_URL`: Your API Gateway base URL
- `TEST_USER_EMAIL`: Email for test user (auto-generated if not set)
- `TEST_USER_PASSWORD`: Password for test user
- `TEST_USER_USERNAME`: Username for test user (auto-generated if not set)
- `TEST_TIMEOUT`: Test timeout in milliseconds

## Notes

- Auth tests must always run first (enforced by test sequencer)
- Test user is created once and reused across all tests
- All API requests are logged for debugging
- Tests are designed to work with the actual deployed API
- Old JavaScript tests are preserved in `tests-old/` directory
