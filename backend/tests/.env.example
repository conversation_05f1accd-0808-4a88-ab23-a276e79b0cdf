# Test Environment Configuration
# Copy this file to .env.test and update the values

# API Configuration - using custom domain without /v1
TEST_API_BASE_URL=https://dev.api.gameflex.io

# Test User Configuration (will be created during tests)
TEST_USER_EMAIL=<EMAIL>
TEST_USER_PASSWORD=TestPassword123!
TEST_USER_USERNAME=testuser
TEST_USER_FIRST_NAME=Test
TEST_USER_LAST_NAME=User

# Test Configuration
TEST_TIMEOUT=30000

# AWS Configuration (if needed for direct AWS calls)
AWS_REGION=us-west-2
AWS_PROFILE=default
