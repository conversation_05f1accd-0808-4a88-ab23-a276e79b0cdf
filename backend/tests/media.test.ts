import { getHttpClient } from './utils/http-client';
import { TestContext } from './utils/test-context';
import {
  TestUser,
  UploadMediaRequest,
  UploadMediaResponse
} from './types/api.types';
import * as fs from 'fs';
import * as path from 'path';
import axios from 'axios';

// Helper function to get test image data
const getTestImageData = () => {
  const imagePath = path.join(__dirname, '../assets/media/cod_screenshot.jpg');
  if (!fs.existsSync(imagePath)) {
    throw new Error(`Test image not found at ${imagePath}`);
  }
  const imageBuffer = fs.readFileSync(imagePath);
  const stats = fs.statSync(imagePath);
  return {
    buffer: imageBuffer,
    size: stats.size,
    fileName: 'cod_screenshot.jpg',
    fileType: 'image/jpeg'
  };
};

// Helper function to upload file to presigned URL
const uploadFileToPresignedUrl = async (uploadUrl: string, fileBuffer: Buffer, contentType: string) => {
  const response = await axios.put(uploadUrl, fileBuffer, {
    headers: {
      'Content-Type': contentType,
    },
    maxBodyLength: Infinity,
    maxContentLength: Infinity,
  });
  return response;
};

// Helper function to wait for media processing
const waitForMediaProcessing = async (httpClient: any, mediaId: string, maxWaitTime = 30000) => {
  const startTime = Date.now();
  while (Date.now() - startTime < maxWaitTime) {
    try {
      const response = await httpClient.get(`/media/${mediaId}`);
      if (response.data.media.status !== 'pending_upload' && response.data.media.status !== 'processing') {
        return response.data.media;
      }
    } catch (error) {
      // Continue waiting if media not found yet
    }
    await new Promise(resolve => setTimeout(resolve, 2000)); // Wait 2 seconds
  }
  throw new Error(`Media processing did not complete within ${maxWaitTime}ms`);
};

describe('Media API Tests', () => {
  let httpClient: ReturnType<typeof getHttpClient>;
  let testUser: TestUser;
  let testMediaId: string;

  beforeAll(async () => {
    // Initialize HTTP client if not already done
    try {
      httpClient = getHttpClient();
    } catch (error) {
      // Initialize if not already initialized
      const { initializeHttpClient } = require('./utils/http-client');
      const { testConfig } = require('./config/test.config');
      httpClient = initializeHttpClient({
        baseURL: testConfig.apiBaseUrl,
        timeout: testConfig.timeout,
      });
    }

    // Create a test user for media tests if not already available
    if (!TestContext.isSetupComplete()) {
      // Create unique test user data
      testUser = TestContext.createTestUserData();

      // Sign up the user (without username)
      const signupResponse = await httpClient.post('/auth/signup', {
        email: testUser.email,
        password: testUser.password,
        firstName: testUser.firstName,
        lastName: testUser.lastName,
      });

      expect(signupResponse.status).toBe(201);
      testUser.id = signupResponse.data.user.id;

      // Sign in to get tokens
      const signinResponse = await httpClient.post('/auth/signin', {
        email: testUser.email,
        password: testUser.password,
      });

      expect(signinResponse.status).toBe(200);
      testUser.tokens = {
        accessToken: signinResponse.data.tokens.accessToken,
        refreshToken: signinResponse.data.tokens.refreshToken,
        idToken: signinResponse.data.tokens.idToken,
      };

      // Set auth token for set-username request
      httpClient.setAuthToken(testUser.tokens.accessToken);

      // Set username (required after signin)
      const setUsernameResponse = await httpClient.post('/auth/set-username', {
        username: testUser.username,
      });

      expect(setUsernameResponse.status).toBe(200);

      // Set the test user in context
      TestContext.setTestUser(testUser);
      TestContext.updateTokens(testUser.tokens!);
      TestContext.setSetupComplete();
    } else {
      testUser = TestContext.getTestUser();
    }

    // Set auth token for authenticated requests
    const accessToken = testUser.tokens?.accessToken;
    if (!accessToken) {
      throw new Error('Access token not available after user setup');
    }
    httpClient.setAuthToken(accessToken);

    if (process.env.VERBOSE_TESTS === 'true') {
      console.log('🖼️ Starting Media API tests...');
    }
  });

  describe('POST /media/upload', () => {
    it('should complete full media upload workflow with real image', async () => {
      try {
        // Get test image data
        const testImage = getTestImageData();

        const mediaData = {
          fileName: testImage.fileName,
          fileType: testImage.fileType,
          fileSize: testImage.size,
          mediaType: 'image'
        };

        // Step 1: Get presigned upload URL
        const uploadResponse = await httpClient.post('/media/upload', mediaData);

        expect(uploadResponse.status).toBe(200);
        expect(uploadResponse.data.message).toBeDefined();
        expect(uploadResponse.data.mediaId).toBeDefined();
        expect(uploadResponse.data.uploadUrl).toBeDefined();
        expect(uploadResponse.data.media).toBeDefined();
        expect(uploadResponse.data.media.fileName).toBe(mediaData.fileName);
        expect(uploadResponse.data.media.fileType).toBe(mediaData.fileType);
        expect(uploadResponse.data.media.fileSize).toBe(mediaData.fileSize);
        expect(uploadResponse.data.media.status).toBe('pending_upload');
        expect(uploadResponse.data.media.staging_s3_key).toBeDefined();
        expect(uploadResponse.data.media.final_s3_key).toBeDefined();
        expect(uploadResponse.data.media.url).toBe(''); // No public URL until approved

        testMediaId = uploadResponse.data.mediaId;

        // Step 2: Upload actual file to S3 using presigned URL
        const s3UploadResponse = await uploadFileToPresignedUrl(
          uploadResponse.data.uploadUrl,
          testImage.buffer,
          testImage.fileType
        );

        expect(s3UploadResponse.status).toBe(200);

        // Step 3: Wait for automatic AI processing to complete
        // Note: In real environment, S3 events trigger processing automatically
        // For testing, we'll check if the media status updates
        const processedMedia = await waitForMediaProcessing(httpClient, testMediaId);

        expect(processedMedia.id).toBe(testMediaId);
        expect(['approved', 'rejected_nsfw', 'failed']).toContain(processedMedia.status);

        if (processedMedia.status === 'approved') {
          expect(processedMedia.url).toBeDefined();
          expect(processedMedia.url).not.toBe('');
        }
      } catch (error: any) {
        // If there's an error, it should be a proper HTTP error
        console.log('Media upload error:', error.response?.status, error.response?.data);
        throw new Error(`Media upload should succeed but got error: ${error.response?.status} ${JSON.stringify(error.response?.data)}`);
      }
    });

    it('should return 400 when required fields are missing', async () => {
      const invalidData = {
        fileName: 'test.jpg',
        // Missing fileType and fileSize
      };

      try {
        await httpClient.post('/media/upload', invalidData);
        throw new Error('Expected request to fail');
      } catch (error: any) {
        console.log('Media validation error:', error.response?.status, error.response?.data);
        expect([400, 403]).toContain(error.response.status);
        expect(error.response.data.error || error.response.data.message).toBeDefined();
      }
    });

    it('should return 400 for invalid content type', async () => {
      const invalidData = {
        fileName: 'test.exe',
        fileType: 'application/x-executable',
        fileSize: 1024,
      };

      try {
        await httpClient.post('/media/upload', invalidData);
        throw new Error('Expected request to fail');
      } catch (error: any) {
        expect([400, 403]).toContain(error.response.status);
        expect(error.response.data.error || error.response.data.message).toBeDefined();
      }
    });

    it('should return 400 for file too large', async () => {
      const invalidData = {
        fileName: 'large-file.jpg',
        fileType: 'image/jpeg',
        fileSize: 50 * 1024 * 1024, // 50MB - should exceed 30MB limit
      };

      try {
        await httpClient.post('/media/upload', invalidData);
        throw new Error('Expected request to fail');
      } catch (error: any) {
        if (error.response) {
          expect([400, 403, 413]).toContain(error.response.status);
          expect(error.response.data.error || error.response.data.message).toBeDefined();
        } else {
          // Network error or timeout - this is acceptable for large file test
          expect(error.message).toBeDefined();
        }
      }
    });

    it('should return 401 when no auth token is provided', async () => {
      // Temporarily remove auth token
      httpClient.removeAuthToken();

      try {
        await httpClient.post('/media/upload', {
          fileName: 'test.jpg',
          fileType: 'image/jpeg',
          fileSize: 1024,
        });
        throw new Error('Expected request to fail');
      } catch (error: any) {
        // API Gateway may return 400 or 401 for missing authorization
        expect([400, 401, 403]).toContain(error.response.status);
      }

      // Restore auth token
      httpClient.setAuthToken(TestContext.getAccessToken());
    });
  });

  describe('GET /media/{id}', () => {
    it('should get media by ID successfully', async () => {
      if (!testMediaId) {
        console.warn('Skipping media by ID test - test media not created');
        return;
      }

      const response = await httpClient.get(`/media/${testMediaId}`);

      expect(response).toBeValidApiResponse();
      expect(response.status).toBe(200);
      expect(response.data.media).toBeDefined();
      expect(response.data.media.id).toBe(testMediaId);
      expect(response.data.media.userId).toBe(testUser.id);
    });

    it('should return 404 for non-existent media', async () => {
      try {
        await httpClient.get('/media/non-existent-media-id');
        throw new Error('Expected request to fail');
      } catch (error: any) {
        expect([400, 403, 404]).toContain(error.response.status);
      }
    });

    it('should return 401 when no auth token is provided', async () => {
      if (!testMediaId) {
        console.warn('Skipping auth test - test media not created');
        return;
      }

      // Temporarily remove auth token
      httpClient.removeAuthToken();

      try {
        const response = await httpClient.get(`/media/${testMediaId}`);
        // If we get here, the request succeeded when it should have failed
        throw new Error('Expected request to fail but it succeeded');
      } catch (error: any) {
        expect([400, 401, 403]).toContain(error.response.status);
      }

      // Restore auth token
      httpClient.setAuthToken(TestContext.getAccessToken());
    });
  });

  describe('PUT /media/{id}', () => {
    it('should update media status successfully', async () => {
      if (!testMediaId) {
        console.warn('Skipping media update test - test media not created');
        return;
      }

      const updateData = {
        status: 'uploaded',
      };

      const response = await httpClient.put(`/media/${testMediaId}`, updateData);

      expect(response).toBeValidApiResponse();
      expect(response.status).toBe(200);
      expect(response.data.message).toBeDefined();
      expect(response.data.media).toBeDefined();
      expect(response.data.media.status).toBe('uploaded');
    });
  });

  describe('DELETE /media/{id}', () => {
    it('should delete media successfully', async () => {
      if (!testMediaId) {
        console.warn('Skipping media delete test - test media not created');
        return;
      }

      const response = await httpClient.delete(`/media/${testMediaId}`);

      expect(response).toBeValidApiResponse();
      expect(response.status).toBe(200);
      expect(response.data.message).toBeDefined();
    });

    it('should return 404 when trying to delete non-existent media', async () => {
      try {
        await httpClient.delete('/media/non-existent-media-id');
        throw new Error('Expected request to fail');
      } catch (error: any) {
        expect([400, 403, 404]).toContain(error.response.status);
      }
    });
  });

  describe('Authentication Required', () => {
    it('should return 401 when no auth token is provided for protected endpoints', async () => {
      // Temporarily remove auth token
      httpClient.removeAuthToken();

      const protectedEndpoints = [
        () => httpClient.post('/media/upload', { fileName: 'test.jpg', fileType: 'image/jpeg', fileSize: 1024 }),
      ];

      for (const endpoint of protectedEndpoints) {
        try {
          await endpoint();
          throw new Error('Expected request to fail');
        } catch (error: any) {
          expect([400, 401, 403]).toContain(error.response.status);
        }
      }

      // Restore auth token
      httpClient.setAuthToken(TestContext.getAccessToken());
    });
  });
});
