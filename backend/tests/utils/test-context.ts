import { GlobalTestContext, TestUser } from '../types/api.types';
import { testConfig } from '../config/test.config';

// Global test context - shared across all tests
const globalContext: GlobalTestContext = {
  testUser: null,
  apiBaseUrl: testConfig.apiBaseUrl,
  isSetup: false,
};

export class TestContext {
  static getContext(): GlobalTestContext {
    return globalContext;
  }

  static setTestUser(user: TestUser): void {
    globalContext.testUser = user;
    if (process.env.VERBOSE_TESTS === 'true') {
      console.log('👤 Test user set:', {
        id: user.id,
        email: user.email,
        username: user.username,
      });
    }
  }

  static getTestUser(): TestUser {
    if (!globalContext.testUser) {
      throw new Error('Test user not initialized. Auth tests must run first.');
    }
    return globalContext.testUser;
  }

  static hasTestUser(): boolean {
    return globalContext.testUser !== null;
  }

  static setSetupComplete(): void {
    globalContext.isSetup = true;
    // Only log in verbose mode
    if (process.env.VERBOSE_TESTS === 'true') {
      console.log('✅ Global test setup completed');
    }
  }

  static isSetupComplete(): boolean {
    return globalContext.isSetup;
  }

  static getAccessToken(): string {
    const user = this.getTestUser();
    if (!user.tokens?.accessToken) {
      throw new Error('Access token not available. User must be signed in.');
    }
    return user.tokens.accessToken;
  }

  static getRefreshToken(): string {
    const user = this.getTestUser();
    if (!user.tokens?.refreshToken) {
      throw new Error('Refresh token not available. User must be signed in.');
    }
    return user.tokens.refreshToken;
  }

  static updateTokens(tokens: { accessToken: string; refreshToken?: string; idToken: string }): void {
    if (!globalContext.testUser) {
      throw new Error('Cannot update tokens: test user not initialized');
    }

    globalContext.testUser.tokens = {
      accessToken: tokens.accessToken,
      refreshToken: tokens.refreshToken || globalContext.testUser.tokens?.refreshToken || '',
      idToken: tokens.idToken,
    };

    if (process.env.VERBOSE_TESTS === 'true') {
      console.log('🔄 Test user tokens updated');
    }
  }

  static reset(): void {
    globalContext.testUser = null;
    globalContext.isSetup = false;
    if (process.env.VERBOSE_TESTS === 'true') {
      console.log('🔄 Test context reset');
    }
  }

  static createTestUserData(): TestUser {
    return {
      email: testConfig.testUserEmail,
      password: testConfig.testUserPassword,
      username: testConfig.testUserUsername,
      firstName: testConfig.testUserFirstName,
      lastName: testConfig.testUserLastName,
    };
  }
}
