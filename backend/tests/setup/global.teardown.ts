import { TestContext } from '../utils/test-context';

export default async function globalTeardown(): Promise<void> {
  if (process.env.VERBOSE_TESTS === 'true') {
    console.log('🧹 Starting global test teardown...');
  }

  try {
    // Reset test context
    TestContext.reset();

    if (process.env.VERBOSE_TESTS === 'true') {
      console.log('✅ Global teardown completed successfully');
    }
  } catch (error) {
    console.error('❌ Global teardown failed:', error);
    // Don't throw here to avoid masking test failures
  }
}
