import Sequencer from '@jest/test-sequencer';
import { Test } from '@jest/test-result';

export default class AuthFirstSequencer extends Sequencer {
  sort(tests: Test[]): Test[] {
    // Sort tests to ensure auth tests run first
    const authTests: Test[] = [];
    const otherTests: Test[] = [];

    tests.forEach((test) => {
      if (test.path.includes('auth.test.ts') || test.path.includes('/auth/')) {
        authTests.push(test);
      } else {
        otherTests.push(test);
      }
    });

    // Sort auth tests by priority (setup first)
    authTests.sort((a, b) => {
      if (a.path.includes('auth.test.ts')) return -1;
      if (b.path.includes('auth.test.ts')) return 1;
      return 0;
    });

    // Sort other tests alphabetically
    otherTests.sort((a, b) => a.path.localeCompare(b.path));

    console.log('📋 Test execution order:');
    [...authTests, ...otherTests].forEach((test, index) => {
      const testName = test.path.split('/').pop() || test.path;
      console.log(`  ${index + 1}. ${testName}`);
    });

    return [...authTests, ...otherTests];
  }
}
