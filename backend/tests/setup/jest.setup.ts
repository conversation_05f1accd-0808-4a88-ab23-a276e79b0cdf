// Jest setup file - runs before each test file

// Extend Jest matchers if needed
expect.extend({
  toBeValidApiResponse(received: any) {
    const pass = received &&
      typeof received.status === 'number' &&
      received.status >= 200 &&
      received.status < 600;

    if (pass) {
      return {
        message: () => `expected ${received} not to be a valid API response`,
        pass: true,
      };
    } else {
      return {
        message: () => `expected ${received} to be a valid API response with status code`,
        pass: false,
      };
    }
  },
});

// Global test timeout
jest.setTimeout(30000);

// Console setup for better test output (only when verbose)
if (process.env.VERBOSE_TESTS === 'true') {
  const originalConsoleLog = console.log;
  console.log = (...args: any[]) => {
    const timestamp = new Date().toISOString();
    originalConsoleLog(`[${timestamp}]`, ...args);
  };
  console.log('🔧 Jest setup completed');
}

// Suppress AWS SDK warnings in tests
process.env.AWS_SDK_JS_SUPPRESS_MAINTENANCE_MODE_MESSAGE = '1';

// TypeScript declaration for custom matchers
declare global {
  namespace jest {
    interface Matchers<R> {
      toBeValidApiResponse(): R;
    }
  }
}
