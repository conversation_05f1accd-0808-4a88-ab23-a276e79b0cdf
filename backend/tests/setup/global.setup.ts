import { validateTestConfig, testConfig } from '../config/test.config';
import { initializeHttpClient } from '../utils/http-client';

export default async function globalSetup(): Promise<void> {
  if (process.env.VERBOSE_TESTS === 'true') {
    console.log('🚀 Starting global test setup...');
  }

  try {
    // Validate test configuration
    validateTestConfig();

    // Initialize HTTP client
    initializeHttpClient({
      baseURL: testConfig.apiBaseUrl,
      timeout: testConfig.timeout,
    });

    if (process.env.VERBOSE_TESTS === 'true') {
      console.log('✅ Global setup completed successfully');
    }
  } catch (error) {
    console.error('❌ Global setup failed:', error);
    throw error;
  }
}
