name: docker

on:
  pull_request:
    branches: master
  push:
    branches: master
    tags:
      - v*

jobs:
  buildx:
    runs-on: ubuntu-latest
    steps:
      -
        name: Checkout
        uses: actions/checkout@v2
      -
        name: Prepare
        id: prepare
        run: |
          DOCKER_IMAGE=openxbox/xbox-webapi-python
          DOCKER_PLATFORMS=linux/amd64,linux/arm/v7,linux/arm64
          VERSION=edge

          if [[ $GITHUB_REF == refs/tags/* ]]; then
            VERSION=${GITHUB_REF#refs/tags/v}
          fi
          if [ "${{ github.event_name }}" = "schedule" ]; then
            VERSION=nightly
          fi

          TAGS="--tag ${DOCKER_IMAGE}:${VERSION}"
          if [[ $VERSION =~ ^[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}$ ]]; then
            TAGS="$TAGS --tag ${DOCKER_IMAGE}:latest"
          fi

          echo ::set-output name=docker_image::${DOCKER_IMAGE}
          echo ::set-output name=version::${VERSION}
          echo ::set-output name=buildx_args::--platform ${DOCKER_PLATFORMS} \
            --build-arg VERSION=${VERSION} \
            --build-arg BUILD_DATE=$(date -u +'%Y-%m-%dT%H:%M:%SZ') \
            --build-arg VCS_REF=${GITHUB_SHA::8} \
            ${TAGS} --file ./Dockerfile .
      -
        name: Set up QEMU
        uses: docker/setup-qemu-action@v1
        with:
          platforms: all
      -
        name: Set up Docker Buildx
        id: buildx
        uses: docker/setup-buildx-action@v1
        with:
          version: latest
      -
        name: Available platforms
        run: echo ${{ steps.buildx.outputs.platforms }}
      -
        name: Docker Buildx (build)
        run: |
          docker buildx build --output "type=image,push=false" ${{ steps.prepare.outputs.buildx_args }}
      -
        name: Login to DockerHub
        if: success() && github.event_name != 'pull_request'
        uses: docker/login-action@v1
        with:
          username: ${{ secrets.DOCKER_USERNAME }}
          password: ${{ secrets.DOCKER_PASSWORD }}
      -
        name: Docker Buildx (push)
        if: success() && github.event_name != 'pull_request'
        run: |
          docker buildx build --output "type=image,push=true" ${{ steps.prepare.outputs.buildx_args }}
      -
        name: Update Dockerhub description
        if: success() && github.event_name != 'pull_request'
        uses: peter-evans/dockerhub-description@v2
        env:
          DOCKERHUB_USERNAME: ${{ secrets.DOCKER_USERNAME }}
          DOCKERHUB_PASSWORD: ${{ secrets.DOCKER_PASSWORD }}
          DOCKERHUB_REPOSITORY: ${{ steps.prepare.outputs.docker_image }}
          README_FILEPATH: ./README.md
      -
        name: Inspect image
        if: always() && github.event_name != 'pull_request'
        run: |
          docker buildx imagetools inspect ${{ steps.prepare.outputs.docker_image }}:${{ steps.prepare.outputs.version }}
