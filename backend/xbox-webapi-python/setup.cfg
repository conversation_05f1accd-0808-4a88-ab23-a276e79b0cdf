[bumpversion]
current_version = 2.1.0
commit = True
tag = True

[bumpversion:file:pyproject.toml]
search = version = "{current_version}"
replace = version = "{new_version}"

[bumpversion:file:xbox/webapi/__init__.py]
search = __version__ = "{current_version}"
replace = __version__ = "{new_version}"

[bumpversion:file:docs/conf.py]
search = release = '{current_version}'
replace = release = '{new_version}'

[bdist_wheel]
universal = 1

[isort]
profile = black
force_sort_within_sections = true
known_first_party = xbox,tests
forced_separate = tests
combine_as_imports = true

[aliases]
test = pytest
