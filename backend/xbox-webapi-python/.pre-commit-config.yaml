repos:
- repo: https://github.com/astral-sh/ruff-pre-commit
  # Ruff version.
  rev: v0.1.6
  hooks:
    # Run the linter.
    - id: ruff
      args: [ --fix ]
    # Run the formatter.
    - id: ruff-format
- repo: https://github.com/asottile/pyupgrade
  rev: v3.15.0
  hooks:
    - id: pyupgrade
      args: [--py38-plus]
- repo: https://github.com/psf/black
  rev: 23.11.0
  hooks:
    - id: black
      args:
        - --safe
        - --quiet
      files: ^((xbox|tests)/.+)?[^/]+\.py$
- repo: https://github.com/PyCQA/bandit
  rev: 1.7.5
  hooks:
    - id: bandit
      args:
        - --configfile=pyproject.toml
        - --quiet
        - --format=custom
      files: ^(xbox|tests)/.+\.py$
- repo: https://github.com/PyCQA/isort
  rev: 5.12.0
  hooks:
    - id: isort
- repo: https://github.com/pre-commit/pre-commit-hooks
  rev: v4.5.0
  hooks:
    - id: check-executables-have-shebangs
      stages: [manual]
    - id: check-json