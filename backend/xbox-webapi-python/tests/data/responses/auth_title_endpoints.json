{"EndPoints": [{"Protocol": "https", "Host": "xlink.xboxlive.com", "HostType": "fqdn", "RelyingParty": "http://xlink.xboxlive.com", "TokenType": "JWT", "SignaturePolicyIndex": 0}, {"Protocol": "https", "Host": "*.dfhosted.net", "HostType": "wildcard", "RelyingParty": "http://xlink.xboxlive.com", "TokenType": "JWT", "SignaturePolicyIndex": 0}, {"Protocol": "https", "Host": "musicdelivery-ssl.xboxlive.com", "HostType": "fqdn", "RelyingParty": "http://music.xboxlive.com", "TokenType": "JWT", "SignaturePolicyIndex": 0}, {"Protocol": "https", "Host": "cloudcollection-ssl.xboxlive.com", "HostType": "fqdn", "RelyingParty": "http://music.xboxlive.com", "TokenType": "JWT", "SignaturePolicyIndex": 0}, {"Protocol": "https", "Host": "music.xboxlive.com", "HostType": "fqdn", "RelyingParty": "http://music.xboxlive.com", "TokenType": "JWT", "SignaturePolicyIndex": 0}, {"Protocol": "https", "Host": "websockets.platform.bing.com", "HostType": "fqdn", "RelyingParty": "http://platform.bing.com/", "TokenType": "JWT", "SignaturePolicyIndex": 0}, {"Protocol": "https", "Host": "websockets.platform.bing-int.com", "HostType": "fqdn", "RelyingParty": "http://platform.bing.com/", "TokenType": "JWT", "SignaturePolicyIndex": 0}, {"Protocol": "https", "Host": "inventory.xboxlive.com", "HostType": "fqdn", "RelyingParty": "http://licensing.xboxlive.com", "TokenType": "JWT", "SignaturePolicyIndex": 0}, {"Protocol": "https", "Host": "licensing.xboxlive.com", "HostType": "fqdn", "RelyingParty": "http://licensing.xboxlive.com", "TokenType": "JWT", "SignaturePolicyIndex": 0}, {"Protocol": "https", "Host": "accountstroubleshooter.xboxlive.com", "HostType": "fqdn", "RelyingParty": "http://accounts.xboxlive.com", "TokenType": "JWT", "SignaturePolicyIndex": 0}, {"Protocol": "https", "Host": "gamertag.xboxlive.com", "HostType": "fqdn", "RelyingParty": "http://accounts.xboxlive.com", "TokenType": "JWT", "SignaturePolicyIndex": 0}, {"Protocol": "https", "Host": "help.ui.xboxlive.com", "HostType": "fqdn", "RelyingParty": "http://uxservices.xboxlive.com", "TokenType": "JWT", "SignaturePolicyIndex": 0}, {"Protocol": "https", "Host": "*.ui.xboxlive.com", "HostType": "wildcard"}, {"Protocol": "https", "Host": "data-vef.xboxlive.com", "HostType": "fqdn", "RelyingParty": "http://data-vef.xboxlive.com", "TokenType": "JWT", "SignaturePolicyIndex": 1}, {"Protocol": "https", "Host": "update.xboxlive.com", "HostType": "fqdn", "RelyingParty": "http://update.xboxlive.com", "TokenType": "JWT", "SignaturePolicyIndex": 0}, {"Protocol": "https", "Host": "update-cdn.xboxlive.com", "HostType": "fqdn", "RelyingParty": "http://update.xboxlive.com", "TokenType": "JWT", "SignaturePolicyIndex": 0}, {"Protocol": "https", "Host": "packages.xboxlive.com", "HostType": "fqdn", "RelyingParty": "http://update.xboxlive.com", "TokenType": "JWT", "SignaturePolicyIndex": 0}, {"Protocol": "https", "Host": "instance.mgt.xboxlive.com", "HostType": "fqdn", "RelyingParty": "http://instance.mgt.xboxlive.com", "TokenType": "JWT", "SignaturePolicyIndex": 1}, {"Protocol": "https", "Host": "device.mgt.xboxlive.com", "HostType": "fqdn", "RelyingParty": "http://device.mgt.xboxlive.com", "TokenType": "JWT", "SignaturePolicyIndex": 1}, {"Protocol": "https", "Host": "device.mgt.xboxlive.com", "HostType": "fqdn", "Path": "/registrations/bestv", "RelyingParty": "http://bestv.device.mgt.xboxlive.com", "TokenType": "JWT", "SignaturePolicyIndex": 1}, {"Protocol": "https", "Host": "device.mgt.xboxlive.com", "HostType": "fqdn", "Path": "/devices/current/unlock", "RelyingParty": "http://unlock.device.mgt.xboxlive.com", "TokenType": "JWT", "SignaturePolicyIndex": 1}, {"Protocol": "https", "Host": "xkms.xboxlive.com", "HostType": "fqdn", "RelyingParty": "http://xkms.xboxlive.com", "TokenType": "JWT", "MinTlsVersion": "1.2", "SignaturePolicyIndex": 0}, {"Protocol": "https", "Host": "privileges.xboxlive.com", "HostType": "fqdn", "RelyingParty": "http://banning.xboxlive.com", "TokenType": "JWT", "SignaturePolicyIndex": 1}, {"Protocol": "https", "Host": "privileges.xboxlive.com", "HostType": "fqdn", "Path": "/upsell", "RelyingParty": "http://xboxlive.com", "TokenType": "JWT", "SignaturePolicyIndex": 1}, {"Protocol": "https", "Host": "attestation.xboxlive.com", "HostType": "fqdn", "RelyingParty": "http://attestation.xboxlive.com", "TokenType": "JWT", "SignaturePolicyIndex": 1}, {"Protocol": "https", "Host": "settings.xboxlive.com", "HostType": "fqdn", "RelyingParty": "http://xboxlive.com", "TokenType": "JWT", "ServerCertIndex": [0, 1], "SignaturePolicyIndex": 1}, {"Protocol": "https", "Host": "*.experimentation.xboxlive.com", "HostType": "wildcard", "RelyingParty": "http://experimentation.xboxlive.com/", "TokenType": "JWT", "SignaturePolicyIndex": 1}, {"Protocol": "https", "Host": "*.xboxlive.com", "HostType": "wildcard", "RelyingParty": "http://xboxlive.com", "TokenType": "JWT", "SignaturePolicyIndex": 0}, {"Protocol": "https", "Host": "xboxlive.com", "HostType": "fqdn", "RelyingParty": "http://xboxlive.com", "TokenType": "JWT", "SignaturePolicyIndex": 0}, {"Protocol": "http", "Host": "*.xboxlive.com", "HostType": "wildcard"}, {"Protocol": "https", "Host": "xaaa.bbtv.cn", "HostType": "fqdn", "Path": "/xboxsms/OOBEService/AuthorizationStatus", "RelyingParty": "http://bestvrp.bestv.com/", "SubRelyingParty": "http://www.bestv.com.cn/", "TokenType": "JWT", "SignaturePolicyIndex": 0}, {"Protocol": "https", "Host": "*.data.microsoft.com", "HostType": "wildcard", "RelyingParty": "http://vortex.microsoft.com", "TokenType": "JWT"}, {"Protocol": "https", "Host": "*.vortex-win-sandbox.data.microsoft.com", "HostType": "wildcard", "RelyingParty": "http://vortex-sbx.microsoft.com", "TokenType": "JWT"}, {"Protocol": "https", "Host": "*.vortex-sandbox.data.microsoft.com", "HostType": "wildcard", "RelyingParty": "http://vortex-sbx.microsoft.com", "TokenType": "JWT"}, {"Protocol": "https", "Host": "vortex-events.xboxlive.com", "HostType": "fqdn", "RelyingParty": "http://events.xboxlive.com", "TokenType": "JWT"}, {"Protocol": "https", "Host": "*.pipe.int.trafficmanager.net", "HostType": "wildcard", "RelyingParty": "http://vortex-sbx.microsoft.com", "TokenType": "JWT"}, {"Protocol": "https", "Host": "*.events-sandbox.data.microsoft.com", "HostType": "wildcard", "RelyingParty": "http://vortex-sbx.microsoft.com", "TokenType": "JWT"}, {"Protocol": "https", "Host": "musicimage.xboxlive.com", "HostType": "fqdn"}, {"Protocol": "https", "Host": "*.xboxservices.com", "HostType": "wildcard", "RelyingParty": "http://mp.microsoft.com/", "TokenType": "JWT"}, {"Protocol": "https", "Host": "assets.xboxservices.com", "HostType": "fqdn"}, {"Protocol": "https", "Host": "*.mp.microsoft.com", "HostType": "wildcard", "RelyingParty": "http://mp.microsoft.com/", "TokenType": "JWT"}, {"Protocol": "https", "Host": "account.microsoft.com", "HostType": "fqdn", "RelyingParty": "http://mp.microsoft.com/", "TokenType": "JWT"}, {"Protocol": "https", "Host": "*.account.microsoft.com", "HostType": "wildcard", "RelyingParty": "http://mp.microsoft.com/", "TokenType": "JWT"}, {"Protocol": "https", "Host": "controls.cp.microsoft.com", "HostType": "fqdn", "RelyingParty": "http://mp.microsoft.com/", "TokenType": "JWT"}, {"Protocol": "https", "Host": "controls.platform.account.www.microsoft.com", "HostType": "fqdn", "RelyingParty": "http://mp.microsoft.com/", "TokenType": "JWT"}, {"Protocol": "https", "Host": "licensing.mp.microsoft.com", "HostType": "fqdn", "RelyingParty": "http://licensing.xboxlive.com", "TokenType": "JWT", "SignaturePolicyIndex": 0}, {"Protocol": "https", "Host": "collections.mp.microsoft.com", "HostType": "fqdn", "RelyingParty": "http://licensing.xboxlive.com", "TokenType": "JWT", "SignaturePolicyIndex": 0}, {"Protocol": "https", "Host": "api.twitch.tv", "HostType": "fqdn", "RelyingParty": "https://twitchxboxrp.twitch.tv/", "TokenType": "JWT"}, {"Protocol": "https", "Host": "xdes.xboxlive.com", "HostType": "fqdn", "RelyingParty": "http://xdes.xboxlive.com/", "TokenType": "JWT"}, {"Protocol": "https", "Host": "skypexbox.skype.com", "HostType": "fqdn", "RelyingParty": "http://xboxliverp.skype.com/", "TokenType": "JWT"}, {"Protocol": "https", "Host": "*.gameservices.xboxlive.com", "HostType": "wildcard", "RelyingParty": "https://gameservices.xboxlive.com/", "TokenType": "JWT"}, {"Protocol": "https", "Host": "ssl.bing.com", "HostType": "fqdn", "Path": "/speechreco/xbox/accessibility", "RelyingParty": "http://platform.bing.com/", "TokenType": "JWT"}, {"Protocol": "https", "Host": "speech.bing.com", "HostType": "fqdn", "RelyingParty": "http://platform.bing.com/", "TokenType": "JWT"}, {"Protocol": "https", "Host": "beam.pro", "HostType": "fqdn", "RelyingParty": "http://beam.pro/", "TokenType": "JWT"}, {"Protocol": "https", "Host": "*.beam.pro", "HostType": "wildcard", "RelyingParty": "http://beam.pro/", "TokenType": "JWT"}, {"Protocol": "https", "Host": "mixer.com", "HostType": "fqdn", "RelyingParty": "http://beam.pro/", "TokenType": "JWT"}, {"Protocol": "https", "Host": "*.mixer.com", "HostType": "wildcard", "RelyingParty": "http://beam.pro/", "TokenType": "JWT"}, {"Protocol": "https", "Host": "zto.dds.microsoft.com", "HostType": "fqdn", "RelyingParty": "http://dds.microsoft.com", "TokenType": "JWT"}, {"Protocol": "https", "Host": "user.mgt.xboxlive.com", "HostType": "fqdn", "RelyingParty": "http://accounts.xboxlive.com", "TokenType": "JWT"}, {"Protocol": "https", "Host": "gssv-auth-prod.xboxlive.com", "HostType": "fqdn", "RelyingParty": "http://gssv.xboxlive.com/", "TokenType": "JWT"}, {"Protocol": "https", "Host": "gssv-auth-strs.xboxlive.com", "HostType": "fqdn", "RelyingParty": "http://gssv.xboxlive.com/", "TokenType": "JWT"}, {"Protocol": "https", "Host": "settings-sandbox.data.microsoft.com", "HostType": "fqdn", "RelyingParty": "http://onesettings-xbox-rp.com/", "TokenType": "JWT"}, {"Protocol": "https", "Host": "settings-win.data.microsoft.com", "HostType": "fqdn", "RelyingParty": "http://onesettings-xbox-rp.com/", "TokenType": "JWT", "ServerCertIndex": [2]}, {"Protocol": "https", "Host": "settings-ppe.data.microsoft.com", "HostType": "fqdn", "RelyingParty": "http://onesettings-xbox-rp.com/", "TokenType": "JWT"}, {"Protocol": "https", "Host": "settings.data.microsoft.com", "HostType": "fqdn", "RelyingParty": "http://onesettings-xbox-rp.com/", "TokenType": "JWT", "ServerCertIndex": [3, 0]}, {"Protocol": "https", "Host": "playfabapi.com", "HostType": "fqdn", "RelyingParty": "http://playfab.xboxlive.com/", "TokenType": "JWT"}, {"Protocol": "https", "Host": "sisu.xboxlive.com", "HostType": "fqdn", "RelyingParty": "http://sisu.xboxlive.com/", "TokenType": "JWT", "SignaturePolicyIndex": 0}, {"Protocol": "https", "Host": "beta-sisu.xboxlive.com", "HostType": "fqdn", "RelyingParty": "http://sisu.xboxlive.com/", "TokenType": "JWT", "SignaturePolicyIndex": 0}, {"Protocol": "https", "Host": "*.gamepass.com", "HostType": "wildcard", "RelyingParty": "http://xboxlive.com", "TokenType": "JWT", "SignaturePolicyIndex": 0}, {"Protocol": "https", "Host": "downloadnotifications.xboxlive.com", "HostType": "fqdn", "RelyingParty": "http://download-notification.gamepass.com/", "TokenType": "JWT", "SignaturePolicyIndex": 0}, {"Protocol": "https", "Host": "xflight.xboxlive.com", "HostType": "fqdn", "RelyingParty": "http://xflight.xboxlive.com/", "TokenType": "JWT", "SignaturePolicyIndex": 0}, {"Protocol": "https", "Host": "xrap.xboxlive.com", "HostType": "fqdn", "RelyingParty": "rp://rap.xboxflight.com/", "TokenType": "JWT", "SignaturePolicyIndex": 0}], "SignaturePolicies": [{"Version": 1, "SupportedAlgorithms": ["ES256"], "MaxBodyBytes": 8192}, {"Version": 1, "SupportedAlgorithms": ["ES256"], "MaxBodyBytes": 4294967295}], "Certs": [{"Thumbprint": "54D9D20239080C32316ED9FF980A48988F4ADF2D", "IsIssuer": true, "RootCertIndex": 0}, {"Thumbprint": "D5A9ACDB80066D0E67FF65A939BBBC952F8ED171", "RootCertIndex": 0}, {"Thumbprint": "8F43288AD272F3103B6FB1428485EA3014C0BCFE", "IsIssuer": true, "RootCertIndex": 1}, {"Thumbprint": "AD898AC73DF333EB60AC1F5FC6C4B2219DDB79B7", "IsIssuer": true, "RootCertIndex": 0}], "RootCerts": ["MIIDdzCCAl+gAwIBAgIEAgAAuTANBgkqhkiG9w0BAQUFADBaMQswCQYDVQQGEwJJRTESMBAGA1UEChMJQmFsdGltb3JlMRMwEQYDVQQLEwpDeWJlclRydXN0MSIwIAYDVQQDExlCYWx0aW1vcmUgQ3liZXJUcnVzdCBSb290MB4XDTAwMDUxMjE4NDYwMFoXDTI1MDUxMjIzNTkwMFowWjELMAkGA1UEBhMCSUUxEjAQBgNVBAoTCUJhbHRpbW9yZTETMBEGA1UECxMKQ3liZXJUcnVzdDEiMCAGA1UEAxMZQmFsdGltb3JlIEN5YmVyVHJ1c3QgUm9vdDCCASIwDQYJKoZIhvcNAQEBBQADggEPADCCAQoCggEBAKMEuyKrmD1X6CZymrV51Cni4eiVgLGw41uOKymaZN+hXe2wCQVt2yguzmKiYv60iNoS6zjrIZ3AQSsBUnuId9Mcj8e6uYi1agnnc+gRQKfRzMpijS3ljwumUNKoUMMo6vWrJYeKmpYcqWe4PwzV9/lSEy/CG9VwcPCPwBLKBsua4dnKM3p31vjsufFoREJIE9LAwqSuXmD+tqYF/LTdB1kC1FkYmGP1pWPgkAx9XbIGevOF6uvUA65ehD5f/xXtabz5OTZydc93Uk3zyZAsuT3lySNTPx8kmCFcB5kpvcY67Oduhjprl3RjM71oGDHweI12v/yejl0qhqdNkNwnGjkCAwEAAaNFMEMwHQYDVR0OBBYEFOWdWTCCR1jMrPoIVDaGezq1BE3wMBIGA1UdEwEB/wQIMAYBAf8CAQMwDgYDVR0PAQH/BAQDAgEGMA0GCSqGSIb3DQEBBQUAA4IBAQCFDF2O5G9RaEIFoN27TyclhAO992T9Ldcw46QQF+vaKSm2eT929hkTI7gQCvlYpNRhcL0EYWoSihfVCr3FvDB81ukMJY2GQE/szKN+OMY3EU/t3WgxjkzSswF07r51XgdIGn9w/xZchMB5hbgF/X++ZRGjD8ACtPhSNzkE1akxehi/oCr0Epn3o0WC4zxe9Z2etciefC7IpJ5OCBRLbf1wbWsaY71k5h+3zvDyny67G7fyUIhzksLi4xaNmjICq44Y3ekQEe5+NauQrz4wlHrQMz2nZQ/1/I6eYs9HRCwBXbsdtTLSR9I4LtD+gdwyah617jzV/OeBHRnDJELqYzmp", "MIIF7TCCA9WgAwIBAgIQP4vItfyfspZDtWnWbELhRDANBgkqhkiG9w0BAQsFADCBiDELMAkGA1UEBhMCVVMxEzARBgNVBAgTCldhc2hpbmd0b24xEDAOBgNVBAcTB1JlZG1vbmQxHjAcBgNVBAoTFU1pY3Jvc29mdCBDb3Jwb3JhdGlvbjEyMDAGA1UEAxMpTWljcm9zb2Z0IFJvb3QgQ2VydGlmaWNhdGUgQXV0aG9yaXR5IDIwMTEwHhcNMTEwMzIyMjIwNTI4WhcNMzYwMzIyMjIxMzA0WjCBiDELMAkGA1UEBhMCVVMxEzARBgNVBAgTCldhc2hpbmd0b24xEDAOBgNVBAcTB1JlZG1vbmQxHjAcBgNVBAoTFU1pY3Jvc29mdCBDb3Jwb3JhdGlvbjEyMDAGA1UEAxMpTWljcm9zb2Z0IFJvb3QgQ2VydGlmaWNhdGUgQXV0aG9yaXR5IDIwMTEwggIiMA0GCSqGSIb3DQEBAQUAA4ICDwAwggIKAoICAQCygEGqNThNE3IyaCJNuLLx/9VSvGzH9dJKjDbu0cJcfoyKrq8TKG/Ac+M6ztAlqFo6be+ouFmrEyNozQwph9FvgFyPRH9dkAFSWKxRxV8qh9zc2AodwQO5e7BW6KPeZGHCnvjzfLnsDbVU/ky2ZU+I8JxImQxCCwl8MVkXeQZ4KI2JOkwDJb5xalwL54RgpJki49KvhKSn+9GY7Qyp3pSJ4Q6g3MDOmT3qCFK7VnnkH4S6Hri0xElcTzFLh93dBWcmmYDgcRGjuKVB4qRTufcyKYMME782XgSzS0NHL2vikR7TmE/dQgfI6B0S/Jmpaz6SfsjWaTr8ZL22CZ3K/QwLopt3YEsDlKQwaRLWQi3BQUzK3Kr9j1uDRprZ/LHR47PJf0h6zSTwQY9cdNCssBAgBkm3xy0hyFfj0IbzA2j70M5xwYmZSmQBbP3sMJHPQTySx+W6hh1hhMdfgzlirrSSL0fzC/hV66AfWdC7dJse0Hbm8ukG1xDo+mTeacY1logC8Ea4PyeZb8txiSk190gWAjWP1Xl8TQLPX+uKg09FcYj5qQ1OcunCnAfPSRtOBA5jUYxe2ADBVSy2xuDCZU7JNDn1nLPEfuhhbhNfFcRf2X7tHc7uROzLLoax7Dj2cO2rXBPB2Q8Nx4CyVe0096yb5MPa50c8prWPMd/FS6/r8QIDAQABo1EwTzALBgNVHQ8EBAMCAYYwDwYDVR0TAQH/BAUwAwEB/zAdBgNVHQ4EFgQUci06AjGQQ7kUBU7h6qfHMdEjiTQwEAYJKwYBBAGCNxUBBAMCAQAwDQYJKoZIhvcNAQELBQADggIBAH9yzw+3xRXbm8BJyiZb/p4T5tPw0tuXX/JLP02zrhmu7deXoKzvqTqjwkGw5biRnhOBJAPmCf0/V0A5ISRW0RAvS0CpNoZLtFNXmvvxfomPEf4YbFGq6O0JlbXlccmh6Yd1phV/yX43VF50k8XDZ8wNT2uoFwxtCJJ+i92Bqi1wIcM9BhS7vyRep4TXPw8hIr1LAAbblxzYXtTFC1yHblCk6MM4pPvLLMWSZpuFXst6bJN8gClYW1e1QGm6CHmmZGIVnYeWRbVmIyADixxzoNOieTPgUFmG2y/lAiXqcyqfABTINseSO+lOAOzYVgm5M0kS0lQLAausR7aRKX1MtHWAUgHoyoL2n8ysnI8X6i8msKtyrAv+nlEex0NVZ09Rs1fWtuzuUrc66U7h14GIvE+OdbtLqPA1qibUZ2dJsnBMO5PcHd94kIZysjik0dySTclY6ysSXNQ7roxrsIPlAT/4CTL2kzU0Iq/dNw13CYArzUgA8YyZGUcFAenRv9FO0OYoQzeZpApKCNmacXPSqs0xE2N2oTdvkjgefRI8ZjLny23h/FKJ3crWZgWalmG+oijHHKOnNlA8OqTfSm7mhzvO6/DggTedEzxSjr25HTTGHdUKaj2YKXCMiSrRq4IQSB/c9O+lxbtVGjhjhE63bK2VVOxlIhBJF7jAHscPrFRH"]}