[project]
name = "xbox-webapi"
version = "2.1.0"
description = "A library to authenticate with Windows Live/Xbox Live and use their API"
authors = [
    {name = "OpenXbox"},
]
dependencies = [
    "appdirs",
    "ecdsa",
    "httpx",
    "ms_cv",
    "pydantic==2.*",
]
requires-python = ">=3.8"
readme = "README.md"
license = {text = "GPL"}
keywords = ["xbox one live api"]
classifiers = [
    "Development Status :: 4 - Beta",
    "Intended Audience :: Developers",
    "License :: OSI Approved :: MIT License",
    "Programming Language :: Python :: 3",
    "Programming Language :: Python :: 3.8",
    "Programming Language :: Python :: 3.9",
    "Programming Language :: Python :: 3.10",
    "Programming Language :: Python :: 3.11",
    "Programming Language :: Python :: 3.12",
    "Topic :: Software Development :: Libraries :: Python Modules",
]

[project.urls]
Homepage = "https://github.com/OpenXbox/xbox-webapi-python"

[project.optional-dependencies]
dev = [
# Tagging releases
    "bump2version",
# Testing
    "coverage",
    "pytest",
    "pytest-asyncio",
    "pytest-cov",
    "pytest-runner",
    "respx",
# Packaging
    "twine",
    "watchdog",
    "wheel",
# Pre-commit / Linting
    "ruff==0.1.6",
    "pre-commit==3.5.0",
    "pyupgrade==3.15.0",
    "black==23.11.0",
#   "flake8-docstrings==1.5.0",
    "pydocstyle==6.1.1",
    "bandit==1.7.5",
    "isort==5.12.0",
]
docs = [
    "Sphinx",
    "sphinx-mdinclude",
    "sphinx_rtd_theme",
]

[project.scripts]
xbox-authenticate = "xbox.webapi.scripts.authenticate:main"
xbox-change-gt = "xbox.webapi.scripts.change_gamertag:main"
xbox-friends = "xbox.webapi.scripts.friends:main"
xbox-searchlive = "xbox.webapi.scripts.search:main"
xbox-xal = "xbox.webapi.scripts.xal:main"

[tool.ruff]
# Exclude a variety of commonly ignored directories.
exclude = [
    ".bzr",
    ".direnv",
    ".eggs",
    ".git",
    ".git-rewrite",
    ".hg",
    ".mypy_cache",
    ".nox",
    ".pants.d",
    ".pytype",
    ".ruff_cache",
    ".svn",
    ".tox",
    ".venv",
    "__pypackages__",
    "_build",
    "buck-out",
    "build",
    "docs",
    "dist",
    "node_modules",
    "venv",
]

# Same as Black.
line-length = 88
indent-width = 4

# Assume Python 3.8
target-version = "py38"

[tool.ruff.lint]
select = ["E4", "E7", "E9", "F"]
ignore = []

fixable = ["ALL"]
unfixable = []
# Allow unused variables when underscore-prefixed.
dummy-variable-rgx = "^(_+|(_+[a-zA-Z0-9_]*[a-zA-Z0-9]+?))$"

[tool.ruff.format]
# Like Black, use double quotes for strings.
quote-style = "double"
# Like Black, indent with spaces, rather than tabs.
indent-style = "space"
# Like Black, respect magic trailing commas.
skip-magic-trailing-comma = false
# Like Black, automatically detect the appropriate line ending.
line-ending = "auto"

[tool.bandit]
exclude_dirs = ["tests"]
tests = [
  "B108",
  "B306",
  "B307",
  "B313",
  "B314",
  "B315",
  "B316",
  "B317",
  "B318",
  "B319",
  "B320",
  "B602",
  "B604"
]

[tool.black]
target-version = ["py37", "py38", "py39", "py310", "py311"]
exclude = 'generated'

[tool.setuptools.packages.find]
where = ["."]
include = ["xbox.webapi.*"]

[build-system]
requires = ["setuptools>=61", "wheel"]
build-backend = "setuptools.build_meta"
