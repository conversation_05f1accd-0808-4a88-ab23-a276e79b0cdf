# GitLab CI/CD Variables Configuration

This document outlines all the environment variables that need to be configured in GitLab for the backend CI/CD pipeline to work properly.

## Required Variables

### AWS Credentials

#### Development Environment
- `AWS_ACCESS_KEY_ID_DEV` - AWS Access Key ID for development environment
- `AWS_SECRET_ACCESS_KEY_DEV` - AWS Secret Access Key for development environment (Protected, Masked)

#### Staging Environment
- `AWS_ACCESS_KEY_ID_STAGING` - AWS Access Key ID for staging environment
- `AWS_SECRET_ACCESS_KEY_STAGING` - AWS Secret Access Key for staging environment (Protected, Masked)

#### Production Environment
- `AWS_ACCESS_KEY_ID_PROD` - AWS Access Key ID for production environment (Protected, Masked)
- `AWS_SECRET_ACCESS_KEY_PROD` - AWS Secret Access Key for production environment (Protected, Masked)

### Optional Variables

#### Security Configuration
- `SAST_ANALYZER_IMAGE_TAG` - Override SAST analyzer image tag (default: "latest")
- `SAST_EXCLUDED_PATHS` - Additional paths to exclude from SAST scanning
- `DS_EXCLUDED_PATHS` - Paths to exclude from dependency scanning

#### Build Configuration
- `NODE_VERSION` - Override Node.js version (default: "18")

## Variable Configuration in GitLab

### Setting Variables

1. Go to your GitLab project
2. Navigate to **Settings** > **CI/CD**
3. Expand the **Variables** section
4. Add each variable with the following settings:

### Variable Settings

| Variable | Type | Protected | Masked | Environment Scope |
|----------|------|-----------|--------|-------------------|
| `AWS_ACCESS_KEY_ID_DEV` | Variable | ✓ | ✗ | All |
| `AWS_SECRET_ACCESS_KEY_DEV` | Variable | ✓ | ✓ | All |
| `AWS_ACCESS_KEY_ID_STAGING` | Variable | ✓ | ✗ | All |
| `AWS_SECRET_ACCESS_KEY_STAGING` | Variable | ✓ | ✓ | All |
| `AWS_ACCESS_KEY_ID_PROD` | Variable | ✓ | ✗ | All |
| `AWS_SECRET_ACCESS_KEY_PROD` | Variable | ✓ | ✓ | All |

### Variable Descriptions

- **Protected**: Only available to protected branches/tags
- **Masked**: Value is hidden in job logs
- **Environment Scope**: Limits variable to specific environments

## Security Best Practices

### Variable Security

1. **Never commit secrets to code** - Use GitLab variables instead
2. **Use masked variables** for sensitive data like API keys
3. **Use protected variables** for production credentials
4. **Rotate credentials regularly** - Update variables when credentials change
5. **Limit scope** - Use environment-specific variables when possible

## Troubleshooting

### Common Issues

1. **Missing Variables**: Check that all required variables are set in GitLab
2. **Permission Denied**: Verify AWS IAM policies and credentials
3. **Masked Variable Issues**: Ensure masked variables don't contain special characters
4. **Protected Branch Access**: Make sure protected variables are accessible to your branch

### Debugging

To debug variable issues:

1. Add a debug job to print non-sensitive variables:
```yaml
debug-variables:
  stage: prepare
  script:
    - echo "AWS_DEFAULT_REGION: $AWS_DEFAULT_REGION"
    - echo "PROJECT_NAME: $PROJECT_NAME"
    - echo "CI_COMMIT_BRANCH: $CI_COMMIT_BRANCH"
    - echo "CI_ENVIRONMENT_NAME: $CI_ENVIRONMENT_NAME"
```

2. Check GitLab CI/CD logs for variable-related errors
3. Verify AWS credentials using AWS CLI in a test job

## Environment-Specific Configuration

### Development
- Automatic deployment on `develop` branch
- Less restrictive security policies
- Shared development resources

### Staging
- Manual deployment from `main` branch
- Production-like environment
- Requires approval for deployment

### Production
- Manual deployment on tags only
- Strict security policies
- Requires multiple approvals
- Rollback capabilities

## Next Steps

1. Set up all required variables in GitLab
2. Test the pipeline with a small change
3. Verify security scanning is working
4. Configure notification webhooks (optional)
5. Set up monitoring and alerting (optional)
