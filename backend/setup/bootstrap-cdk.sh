#!/bin/bash

# GameFlex CDK Bootstrap Script
# This script bootstraps CDK for individual environments using environment-specific credentials
#
# Prerequisites:
# 1. Run bootstrap.sh first to create GitLab CI users and policies
# 2. Add environment-specific AWS credentials to .env file
#
# Usage:
#   ./bootstrap-cdk.sh                    # Interactive menu to select environment
#   ./bootstrap-cdk.sh ENV                # Bootstrap specific environment (dev/staging/prod)

set -e  # Exit on any error

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Script directory
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"

# Function to print colored output
print_info() {
    echo -e "${BLUE}ℹ️  $1${NC}"
}

print_success() {
    echo -e "${GREEN}✅ $1${NC}"
}

print_warning() {
    echo -e "${YELLOW}⚠️  $1${NC}"
}

print_error() {
    echo -e "${RED}❌ $1${NC}"
}

print_header() {
    echo -e "${BLUE}"
    echo "=============================================="
    echo "  GameFlex Bootstrap Setup"
    echo "=============================================="
    echo -e "${NC}"
}



# Function to load environment variables from .env file
load_env_vars() {
    local env_suffix=$1
    local env_file="$SCRIPT_DIR/../.env"

    if [[ ! -f "$env_file" ]]; then
        print_error ".env file not found at $env_file"
        print_info "Please create a .env file with your AWS credentials:"
        print_info "AWS_ACCESS_KEY_ID_DEV=your_dev_access_key"
        print_info "AWS_SECRET_ACCESS_KEY_DEV=your_dev_secret_key"
        print_info "AWS_REGION_DEV=us-west-2"
        print_info ""
        print_info "AWS_ACCESS_KEY_ID_STAGING=your_staging_access_key"
        print_info "AWS_SECRET_ACCESS_KEY_STAGING=your_staging_secret_key"
        print_info "AWS_REGION_STAGING=us-west-2"
        print_info ""
        print_info "AWS_ACCESS_KEY_ID_PROD=your_prod_access_key"
        print_info "AWS_SECRET_ACCESS_KEY_PROD=your_prod_secret_key"
        print_info "AWS_REGION_PROD=us-east-1"
        exit 1
    fi

    # Source the .env file
    set -a  # automatically export all variables
    source "$env_file"
    set +a  # stop automatically exporting

    # Set the environment-specific variables
    case $env_suffix in
        "DEV")
            export AWS_ACCESS_KEY_ID="$AWS_ACCESS_KEY_ID_DEV"
            export AWS_SECRET_ACCESS_KEY="$AWS_SECRET_ACCESS_KEY_DEV"
            export AWS_DEFAULT_REGION="$AWS_REGION_DEV"
            ;;
        "STAGING")
            export AWS_ACCESS_KEY_ID="$AWS_ACCESS_KEY_ID_STAGING"
            export AWS_SECRET_ACCESS_KEY="$AWS_SECRET_ACCESS_KEY_STAGING"
            export AWS_DEFAULT_REGION="$AWS_REGION_STAGING"
            ;;
        "PROD")
            export AWS_ACCESS_KEY_ID="$AWS_ACCESS_KEY_ID_PROD"
            export AWS_SECRET_ACCESS_KEY="$AWS_SECRET_ACCESS_KEY_PROD"
            export AWS_DEFAULT_REGION="$AWS_REGION_PROD"
            ;;
        *)
            print_error "Invalid environment suffix: $env_suffix"
            exit 1
            ;;
    esac

    # Validate that the required variables are set
    if [[ -z "$AWS_ACCESS_KEY_ID" || -z "$AWS_SECRET_ACCESS_KEY" || -z "$AWS_DEFAULT_REGION" ]]; then
        print_error "Missing required AWS credentials for environment $env_suffix"
        print_info "Required variables: AWS_ACCESS_KEY_ID_$env_suffix, AWS_SECRET_ACCESS_KEY_$env_suffix, AWS_REGION_$env_suffix"
        exit 1
    fi

    # Validate AWS_ACCOUNT_ID is set (should be loaded from .env)
    if [[ -z "$AWS_ACCOUNT_ID" ]]; then
        print_error "Missing AWS_ACCOUNT_ID in .env file"
        print_info "Please add AWS_ACCOUNT_ID=************ to your .env file"
        exit 1
    fi

    # Set ACCOUNT_ID for compatibility with existing code
    export ACCOUNT_ID="$AWS_ACCOUNT_ID"

    print_success "Loaded AWS credentials for $env_suffix environment"
    print_info "Account ID: $ACCOUNT_ID"
    print_info "Region: $AWS_DEFAULT_REGION"
    print_info "Access Key: ${AWS_ACCESS_KEY_ID:0:8}..."
}

# Function to validate AWS credentials and permissions
validate_credentials() {
    print_info "Validating AWS credentials..."

    if ! aws sts get-caller-identity >/dev/null 2>&1; then
        print_error "AWS credentials are not valid or not configured"
        return 1
    fi

    local caller_identity=$(aws sts get-caller-identity)
    local current_account=$(echo "$caller_identity" | jq -r '.Account' 2>/dev/null || echo "unknown")
    local current_user=$(echo "$caller_identity" | jq -r '.Arn' 2>/dev/null || echo "unknown")

    print_success "AWS credentials validated"
    print_info "Account: $current_account"
    print_info "User/Role: $current_user"

    if [ "$current_account" != "$ACCOUNT_ID" ]; then
        print_warning "Current account ($current_account) doesn't match expected account ($ACCOUNT_ID)"
        if [[ "$SKIP_CONFIRMATIONS" != "true" ]]; then
            read -p "Continue anyway? (y/N): " -n 1 -r
            echo
            if [[ ! $REPLY =~ ^[Yy]$ ]]; then
                print_error "Aborted by user"
                exit 1
            fi
        else
            print_info "Skipping confirmation (--yes flag provided)"
        fi
    fi
}

# Function to check CDK bootstrap permissions
check_cdk_permissions() {
    local region="$1"
    local environment="$2"
    print_info "Checking CDK bootstrap permissions..."

    # Test CloudFormation permissions
    print_info "Testing CloudFormation permissions..."
    if aws cloudformation list-stacks --region "$region" >/dev/null 2>&1; then
        print_success "CloudFormation list permission: OK"
    else
        print_error "CloudFormation list permission: FAILED"
        print_info "You need cloudformation:ListStacks permission"
        return 1
    fi

    # Test S3 permissions (CDK needs to create asset buckets)
    print_info "Testing S3 permissions..."
    if aws s3 ls >/dev/null 2>&1; then
        print_success "S3 list permission: OK"
    else
        print_error "S3 list permission: FAILED"
        print_info "You need s3:ListAllMyBuckets permission"
        return 1
    fi

    # Test IAM permissions (CDK creates roles)
    print_info "Testing IAM permissions..."

    # Debug: Show current AWS identity
    print_info "Current AWS identity:"
    aws sts get-caller-identity 2>/dev/null || print_warning "Could not get caller identity"

    # Skip IAM list test and assume AdministratorAccess is working
    print_success "IAM permissions: Assuming AdministratorAccess is available"
    print_info "CDK bootstrap will validate all permissions during execution"

    # Test if user has the required CDK policies
    local user_arn=$(aws sts get-caller-identity --query 'Arn' --output text)
    local user_name=$(echo "$user_arn" | cut -d'/' -f2)

    print_info "Checking attached policies for user: $user_name"

    # Check for our custom policies that might interfere with bootstrap
    local env_policy_name
    case "$environment" in
        "development") env_policy_name="GameFlexDevelopmentCIPolicy" ;;
        "staging") env_policy_name="GameFlexStagingCIPolicy" ;;
        "production") env_policy_name="GameFlexProductionCIPolicy" ;;
    esac

    local has_env_policy=false
    local policy_check=$(aws iam list-attached-user-policies --user-name "$user_name" --output text 2>/dev/null)

    if [ -n "$policy_check" ] && echo "$policy_check" | grep -q "$env_policy_name"; then
        print_success "Found environment policy: $env_policy_name"
        print_info "Environment policy includes CDK bootstrap permissions"
        has_env_policy=true
    elif [ -z "$policy_check" ]; then
        # If we can't list policies, assume environment policy is present
        # This happens when using environment-specific credentials with restricted permissions
        print_info "Cannot list attached policies (expected with environment-specific credentials)"
        print_info "Assuming environment policy is present: $env_policy_name"
        print_info "Environment policies include CDK bootstrap permissions"
        has_env_policy=true
    else
        print_info "Environment policy not found: $env_policy_name"
    fi

    # Check for AdministratorAccess
    local has_admin_access=false

    # Check directly attached policies
    if aws iam list-attached-user-policies --user-name "$user_name" --query "AttachedPolicies[?PolicyName=='AdministratorAccess']" --output text 2>/dev/null | grep -q "AdministratorAccess"; then
        print_success "Found AdministratorAccess policy (directly attached)"
        has_admin_access=true
    fi

    # Check group-based policies
    if [ "$has_admin_access" = false ]; then
        local user_groups=$(aws iam list-groups-for-user --user-name "$user_name" --query "Groups[].GroupName" --output text 2>/dev/null)
        if [ $? -eq 0 ] && [ -n "$user_groups" ]; then
            for group in $user_groups; do
                if aws iam list-attached-group-policies --group-name "$group" --query "AttachedPolicies[?PolicyName=='AdministratorAccess']" --output text 2>/dev/null | grep -q "AdministratorAccess"; then
                    print_success "Found AdministratorAccess policy via group: $group"
                    has_admin_access=true
                    break
                fi
            done
        else
            print_info "Cannot check group policies due to permission restrictions"
            if [ "$has_env_policy" = true ]; then
                print_info "This is likely due to the environment policy's deny statements"
            fi
        fi
    fi

    # Check if environment policy has CDK bootstrap permissions as fallback
    if [ "$has_admin_access" = false ] && [ "$has_env_policy" = true ]; then
        print_info "No AdministratorAccess found, but environment policy detected"
        print_info "Environment policies include CDK bootstrap permissions - proceeding with bootstrap"
        print_warning "If bootstrap fails, you may need to temporarily attach AdministratorAccess"
    elif [ "$has_admin_access" = false ]; then
        print_error "No sufficient CDK bootstrap permissions found"
        print_info "CDK bootstrap requires either AdministratorAccess or environment-specific policies with CDK permissions"
        return 1
    fi

    if [ "$has_env_policy" = true ]; then
        print_warning "Environment policy detected - bootstrap may fail due to explicit deny statements"
        echo
        if [[ "$SKIP_CONFIRMATIONS" != "true" ]]; then
            read -p "Do you want to continue anyway? (y/N): " -n 1 -r
            echo
            if [[ ! $REPLY =~ ^[Yy]$ ]]; then
                print_info "Please detach the environment policy and run the script again"
                return 1
            fi
        else
            print_info "Skipping confirmation (--yes flag provided)"
        fi
    fi

    return 0
}

# Function to cleanup failed CDK bootstrap stack
cleanup_failed_cdk_stack() {
    local stack_name="$1"
    local region="$2"

    print_info "Attempting to cleanup failed CDK bootstrap stack: $stack_name"

    # List of common CDK bootstrap IAM roles that might be stuck
    local roles=(
        "cdk-gfdev-cfn-exec-role-************-us-west-2"
        "cdk-gfdev-image-publishing-role-************-us-west-2"
        "cdk-gfdev-file-publishing-role-************-us-west-2"
        "cdk-gfdev-lookup-role-************-us-west-2"
        "CloudFormationExecutionRole"
        "ImagePublishingRole"
        "FilePublishingRole"
        "LookupRole"
    )

    print_info "Checking for stuck IAM roles..."

    for role in "${roles[@]}"; do
        if aws iam get-role --role-name "$role" >/dev/null 2>&1; then
            print_info "Found stuck role: $role"
            print_info "Attempting to delete role: $role"

            # First, detach all policies
            local attached_policies=$(aws iam list-attached-role-policies --role-name "$role" --query 'AttachedPolicies[].PolicyArn' --output text 2>/dev/null)
            if [ -n "$attached_policies" ]; then
                for policy_arn in $attached_policies; do
                    print_info "Detaching policy: $policy_arn"
                    aws iam detach-role-policy --role-name "$role" --policy-arn "$policy_arn" 2>/dev/null || true
                done
            fi

            # Delete inline policies
            local inline_policies=$(aws iam list-role-policies --role-name "$role" --query 'PolicyNames' --output text 2>/dev/null)
            if [ -n "$inline_policies" ]; then
                for policy_name in $inline_policies; do
                    print_info "Deleting inline policy: $policy_name"
                    aws iam delete-role-policy --role-name "$role" --policy-name "$policy_name" 2>/dev/null || true
                done
            fi

            # Finally, delete the role
            if aws iam delete-role --role-name "$role" 2>/dev/null; then
                print_success "Successfully deleted role: $role"
            else
                print_warning "Could not delete role: $role (may not exist or permission denied)"
            fi
        fi
    done

    # Now try to delete the CloudFormation stack
    print_info "Attempting to delete CloudFormation stack: $stack_name"
    if aws cloudformation delete-stack --stack-name "$stack_name" --region "$region" 2>/dev/null; then
        print_info "Stack deletion initiated. Waiting for completion..."

        # Wait for stack deletion to complete (with timeout)
        local max_wait=600  # 10 minutes
        local wait_time=0

        while [ $wait_time -lt $max_wait ]; do
            # Check if stack still exists
            local stack_exists=$(aws cloudformation describe-stacks --stack-name "$stack_name" --region "$region" 2>/dev/null)
            local exit_code=$?

            if [ $exit_code -ne 0 ]; then
                # Stack no longer exists - deletion completed
                print_success "Stack deletion completed - stack no longer exists"
                return 0
            fi

            # Stack still exists, check its status
            local stack_status=$(echo "$stack_exists" | jq -r '.Stacks[0].StackStatus' 2>/dev/null || echo "UNKNOWN")

            if [ "$stack_status" = "DELETE_COMPLETE" ]; then
                print_success "Stack deletion completed"
                return 0
            elif [ "$stack_status" = "DELETE_FAILED" ]; then
                print_error "Stack deletion failed again with status: $stack_status"
                return 1
            elif [ "$stack_status" = "DELETE_IN_PROGRESS" ]; then
                print_info "Stack deletion in progress (waiting...)"
            else
                print_info "Stack status: $stack_status (waiting...)"
            fi

            sleep 15
            wait_time=$((wait_time + 15))
        done

        print_warning "Stack deletion timed out after 5 minutes"
        return 1
    else
        print_warning "Could not initiate stack deletion"
        return 1
    fi
}

# Function to suggest policy fixes
suggest_policy_fix() {
    local env_name="$1"
    print_info "CDK Bootstrap Permission Requirements:"
    echo "----------------------------------------"
    echo "CDK bootstrap requires extensive AWS permissions including:"
    echo "  • CloudFormation: Create/Update/Delete stacks"
    echo "  • S3: Create buckets and manage objects"
    echo "  • IAM: Create roles and policies"
    echo "  • SSM: Create parameters"
    echo "  • ECR: Create repositories (for container assets)"
    echo
    print_info "Recommended solutions:"
    echo "1. Attach AdministratorAccess policy temporarily:"
    echo "   aws iam attach-user-policy --user-name YOUR_USERNAME --policy-arn arn:aws:iam::aws:policy/AdministratorAccess"
    echo
    echo "2. Or create a custom CDK bootstrap policy with these permissions:"
    echo "   - cloudformation:*"
    echo "   - s3:*"
    echo "   - iam:*"
    echo "   - ssm:*"
    echo "   - ecr:*"
    echo
    echo "3. After bootstrap, you can remove AdministratorAccess and use the environment-specific policies"
    local policy_name
    case "$env_name" in
        "development") policy_name="GameFlexDevelopmentCIPolicy" ;;
        "staging") policy_name="GameFlexStagingCIPolicy" ;;
        "production") policy_name="GameFlexProductionCIPolicy" ;;
        *) policy_name="GameFlex${env_name}CIPolicy" ;;
    esac
    echo "   from the setup guide ($policy_name)"
    echo "----------------------------------------"
}


# Function to run CDK with debug information
debug_cdk_environment() {
    print_info "CDK Environment Debug Information:"
    echo "----------------------------------------"

    echo "Node.js version:"
    node --version 2>/dev/null || echo "Node.js not found"

    echo "npm version:"
    npm --version 2>/dev/null || echo "npm not found"

    echo "CDK version:"
    cdk --version 2>/dev/null || echo "CDK not found"

    echo "AWS CLI version:"
    aws --version 2>/dev/null || echo "AWS CLI not found"

    echo "Current AWS identity:"
    aws sts get-caller-identity 2>/dev/null || echo "AWS credentials not configured"

    echo "Environment variables:"
    echo "AWS_DEFAULT_REGION: ${AWS_DEFAULT_REGION:-not set}"
    echo "AWS_REGION: ${AWS_REGION:-not set}"
    echo "AWS_ACCESS_KEY_ID: ${AWS_ACCESS_KEY_ID:+***set***}"
    echo "AWS_SECRET_ACCESS_KEY: ${AWS_SECRET_ACCESS_KEY:+***set***}"

    echo "----------------------------------------"
}

# Function to bootstrap CDK
bootstrap_cdk() {
    local env_name="$1"
    local region="$2"
    local stack_name="$3"
    local description="$4"
    local qualifier="$5"

    print_info "Starting CDK bootstrap for $env_name environment..."
    print_info "Region: $region"
    print_info "Stack Name: $stack_name"
    print_info "Qualifier: $qualifier"
    echo

    # Check if Node.js and npm are installed
    if ! command -v node >/dev/null 2>&1; then
        print_error "Node.js is not installed. Please install Node.js first."
        exit 1
    fi

    if ! command -v npm >/dev/null 2>&1; then
        print_error "npm is not installed. Please install npm first."
        exit 1
    fi

    # Check if CDK is available
    print_info "Checking CDK installation..."

    # Check if global CDK is available first
    if command -v cdk >/dev/null 2>&1; then
        local cdk_version=$(cdk --version 2>/dev/null || echo "version check failed")
        print_info "CDK Version: $cdk_version"
        print_info "CDK Location: $(which cdk)"
    else
        print_warning "Global CDK not found, checking npx..."

        if ! command -v npx >/dev/null 2>&1; then
            print_error "npx is not installed. Please install Node.js and npm first."
            exit 1
        fi

        local cdk_version=$(npx cdk --version 2>/dev/null || echo "not installed")
        if [ "$cdk_version" = "not installed" ]; then
            print_warning "CDK not found. Installing CDK globally..."
            npm install -g aws-cdk
            cdk_version=$(cdk --version 2>/dev/null || echo "installation failed")
        fi

        print_info "CDK Version: $cdk_version"
    fi
    echo

    # Show debug information
    debug_cdk_environment

    # Create a temporary log file for capturing output
    local log_file="/tmp/cdk-bootstrap-$$.log"

    # Determine the CloudFormation execution policy based on environment
    local cf_execution_policy
    case "$env_name" in
        "development") cf_execution_policy="arn:aws:iam::$ACCOUNT_ID:policy/GameFlexDevelopmentCFNExecutionPolicy" ;;
        "staging") cf_execution_policy="arn:aws:iam::$ACCOUNT_ID:policy/GameFlexStagingCFNExecutionPolicy" ;;
        "production") cf_execution_policy="arn:aws:iam::$ACCOUNT_ID:policy/GameFlexProductionCFNExecutionPolicy" ;;
    esac

    # Run CDK bootstrap with real-time output
    print_info "Running CDK bootstrap command..."
    echo "Command: cdk bootstrap aws://$ACCOUNT_ID/$region --toolkit-stack-name $stack_name --description \"$description\" --qualifier $qualifier --cloudformation-execution-policies $cf_execution_policy --verbose"
    echo
    print_info "Bootstrap output:"
    echo "----------------------------------------"

    # Check if stack already exists first (check both new and old naming patterns)
    print_info "Checking if CDK toolkit stack already exists..."

    # Check for the new stack name
    local existing_stack=$(aws cloudformation describe-stacks \
        --stack-name "$stack_name" \
        --region "$region" \
        --query 'Stacks[0].StackStatus' \
        --output text 2>/dev/null || echo "NOT_FOUND")

    # Note: We don't check for old application stacks like gameflex-staging
    # as they are not CDK toolkit stacks and should not interfere with bootstrap

    # Report what we found
    if [ "$existing_stack" != "NOT_FOUND" ]; then
        print_warning "CDK toolkit stack '$stack_name' already exists with status: $existing_stack"
    fi

    # Handle existing stacks
    if [ "$existing_stack" != "NOT_FOUND" ]; then
        print_warning "Stack '$stack_name' already exists with status: $existing_stack"

        # Handle DELETE_FAILED status specially
        if [ "$existing_stack" = "DELETE_FAILED" ] || [ "$existing_stack" = "ROLLBACK_FAILED" ]; then
            print_error "Stack is in a failed state: $existing_stack"
            print_warning "CloudFormation cannot update or recreate a stack in DELETE_FAILED state."
            print_info "This usually happens when IAM roles couldn't be deleted due to permission restrictions."
            echo
            print_info "SOLUTION: Use administrator credentials to clean up the failed stack"
            echo "----------------------------------------"
            echo "1. Configure AWS CLI with administrator credentials:"
            echo "   aws configure --profile admin"
            echo "   export AWS_PROFILE=admin"
            echo
            echo "2. Run the cleanup script:"
            echo "   ./setup/cleanup-failed-cdk.sh"
            echo
            echo "3. Then re-run this bootstrap script:"
            echo "   ./setup/bootstrap-cdk.sh dev"
            echo "----------------------------------------"
            echo
            echo "Alternatively, you can manually delete the stack from AWS Console:"
            echo "• Go to CloudFormation -> Stacks"
            echo "• Delete stack: $stack_name"
            echo "• If deletion fails, manually delete the stuck IAM roles first"
            echo

            if [[ "$SKIP_CONFIRMATIONS" = "true" ]]; then
                print_error "Cannot proceed with --yes flag when stack is in DELETE_FAILED state"
                print_info "Manual cleanup with administrator credentials is required"
                exit 1
            else
                read -p "Do you want to exit and fix this manually? (Y/n): " -n 1 -r
                echo
                if [[ ! $REPLY =~ ^[Nn]$ ]]; then
                    print_info "Exiting for manual cleanup"
                    exit 1
                fi

                print_warning "Attempting bootstrap anyway (will likely fail)..."
                FORCE_FLAG="--force"
            fi
        else
            echo "Options:"
            echo "1) Use existing stack (recommended)"
            echo "2) Update existing stack"
            echo "3) Force recreate stack"

            if [[ "$SKIP_CONFIRMATIONS" = "true" ]]; then
                choice=2
                print_info "Auto-selecting option 2 (--yes flag provided)"
            else
                read -p "Choose option (1-3): " choice
            fi
        fi

        case $choice in
            1)
                print_info "Using existing stack."
                # Verify the existing stack is working
                print_info "Verifying existing stack..."
                local stack_status=$(aws cloudformation describe-stacks \
                    --stack-name "$stack_name" \
                    --region "$region" \
                    --query 'Stacks[0].StackStatus' \
                    --output text 2>/dev/null || echo "ERROR")

                if [ "$stack_status" = "CREATE_COMPLETE" ] || [ "$stack_status" = "UPDATE_COMPLETE" ]; then
                    print_success "Existing stack is healthy: $stack_status"
                    return 0
                else
                    print_warning "Existing stack status: $stack_status"
                    print_info "Proceeding with update..."
                fi
                ;;
            2)
                print_info "Will update existing stack..."
                ;;
            3)
                print_warning "Will force recreate stack..."
                FORCE_FLAG="--force"
                ;;
            *)
                print_info "Invalid choice. Using existing stack."
                return 0
                ;;
        esac
    fi

    # Run the CDK bootstrap command with better output handling
    print_info "Executing CDK bootstrap..."

    # Set environment variables for better debugging
    export CDK_DEBUG=true
    export AWS_SDK_LOAD_CONFIG=1

    # Determine the CloudFormation execution policy based on environment
    local cf_execution_policy
    case "$env_name" in
        "development") cf_execution_policy="arn:aws:iam::$ACCOUNT_ID:policy/GameFlexDevelopmentCFNExecutionPolicy" ;;
        "staging") cf_execution_policy="arn:aws:iam::$ACCOUNT_ID:policy/GameFlexStagingCFNExecutionPolicy" ;;
        "production") cf_execution_policy="arn:aws:iam::$ACCOUNT_ID:policy/GameFlexProductionCFNExecutionPolicy" ;;
    esac

    # Create the command as a string for better debugging
    local cmd="cdk bootstrap aws://$ACCOUNT_ID/$region --toolkit-stack-name $stack_name --description \"$description\" --qualifier $qualifier --cloudformation-execution-policies $cf_execution_policy --verbose ${FORCE_FLAG:-}"

    # Run the command and capture both stdout and stderr
    print_info "Running: $cmd"
    echo

    # Use a more direct approach to capture output
    local output
    local exit_code

    # Run the CDK command with simplified execution
    print_info "Running CDK bootstrap command..."
    echo "Command: $cmd"
    echo

    # Create a simple wrapper script to ensure we get output
    local wrapper_script="/tmp/cdk-wrapper-$$.sh"
    cat > "$wrapper_script" << EOF
#!/bin/bash
set -e
export CDK_DEBUG=true
export AWS_SDK_LOAD_CONFIG=1
echo "Starting CDK bootstrap..."
$cmd
echo "CDK bootstrap command completed with exit code: \$?"
EOF

    chmod +x "$wrapper_script"

    # Run the wrapper script
    if bash "$wrapper_script"; then
        local exit_code=0
    else
        local exit_code=$?
    fi

    # Clean up
    rm -f "$wrapper_script"

    echo "----------------------------------------"
    print_info "CDK command exit code: $exit_code"

    if [ "$exit_code" -eq 0 ]; then
        print_success "CDK bootstrap completed successfully!"

        # Verify the stack was created
        print_info "Verifying stack creation..."
        sleep 3

        local stack_status=$(aws cloudformation describe-stacks \
            --stack-name "$stack_name" \
            --region "$region" \
            --query 'Stacks[0].StackStatus' \
            --output text 2>/dev/null || echo "NOT_FOUND")

        if [ "$stack_status" = "CREATE_COMPLETE" ] || [ "$stack_status" = "UPDATE_COMPLETE" ]; then
            print_success "Stack verification successful: $stack_status"

            # Show stack details
            print_info "Stack details:"
            aws cloudformation describe-stacks \
                --stack-name "$stack_name" \
                --region "$region" \
                --query 'Stacks[0].{Name:StackName,Status:StackStatus,Created:CreationTime}' \
                --output table
        else
            print_warning "Stack status: $stack_status"
            print_info "You can check the stack status in the AWS Console"
        fi

    else
        print_error "CDK bootstrap failed with exit code: $exit_code"
        echo

        # Most likely cause is insufficient permissions
        print_warning "CDK bootstrap failure is usually caused by insufficient AWS permissions."
        echo

        # Show policy suggestions
        suggest_policy_fix "$env_name"

        # Try to get more specific error information
        print_info "Checking for existing CDK stacks in region $region..."
        if aws cloudformation list-stacks \
            --region "$region" \
            --query 'StackSummaries[?contains(StackName, `CDK`) && StackStatus != `DELETE_COMPLETE`].{Name:StackName,Status:StackStatus}' \
            --output table 2>/dev/null; then
            echo
        else
            print_warning "Could not list CloudFormation stacks - this confirms permission issues"
        fi

        exit 1
    fi
}

# Function to show usage
show_usage() {
    echo "Usage: $0 [ENVIRONMENT]"
    echo ""
    echo "GameFlex CDK Bootstrap Script"
    echo ""
    echo "Arguments:"
    echo "  ENVIRONMENT             Environment to bootstrap (dev/staging/prod)"
    echo ""
    echo "Options:"
    echo "  -h, --help              Show this help message"
    echo ""
    echo "Examples:"
    echo "  $0                      # Interactive menu to select environment"
    echo "  $0 dev                  # Bootstrap CDK for development"
    echo "  $0 staging              # Bootstrap CDK for staging"
    echo "  $0 prod                 # Bootstrap CDK for production"
    echo ""
    echo "Prerequisites:"
    echo "  1. Run bootstrap.sh first to create GitLab CI users and policies"
    echo "  2. Add environment-specific AWS credentials to .env file"
    echo ""
    echo "Interactive Mode:"
    echo "  If no environment is specified, the script will show an interactive menu"
    echo "  to select which environment to bootstrap."
}



# Function to bootstrap CDK for a specific environment
bootstrap_environment() {
    local env_name=$1

    # Convert environment name to proper format
    case "$env_name" in
        "dev"|"development")
            ENV_NAME="development"
            ENV_SUFFIX="DEV"
            REGION="us-west-2"
            STACK_NAME="CDKToolkit-GameFlex-Development"
            DESCRIPTION="CDK Toolkit for GameFlex Development Environment"
            QUALIFIER="gfdev"
            ;;
        "staging")
            ENV_NAME="staging"
            ENV_SUFFIX="STAGING"
            REGION="us-west-2"
            STACK_NAME="CDKToolkit-GameFlex-Staging"
            DESCRIPTION="CDK Toolkit for GameFlex Staging Environment"
            QUALIFIER="gfstaging"
            ;;
        "prod"|"production")
            ENV_NAME="production"
            ENV_SUFFIX="PROD"
            REGION="us-east-1"
            STACK_NAME="CDKToolkit-GameFlex-Production"
            DESCRIPTION="CDK Toolkit for GameFlex Production Environment"
            QUALIFIER="gfprod"
            ;;
        *)
            print_error "Invalid environment: $env_name"
            print_info "Valid environments: dev, development, staging, prod, production"
            exit 1
            ;;
    esac

    print_header
    print_info "Bootstrapping CDK for $ENV_NAME environment..."
    print_info "Selected: $ENV_NAME environment in $REGION"
    echo

    # Load environment-specific credentials from .env file
    load_env_vars "$ENV_SUFFIX"
    echo

    # Validate credentials
    validate_credentials
    echo

    # Check CDK permissions
    check_cdk_permissions "$REGION" "$ENV_NAME"
    echo

    # Confirm before proceeding
    print_warning "About to bootstrap CDK with the following settings:"
    echo "  Environment: $ENV_NAME"
    echo "  Region: $REGION"
    echo "  Stack Name: $STACK_NAME"
    echo "  Account ID: $ACCOUNT_ID"
    echo
    if [[ "$SKIP_CONFIRMATIONS" != "true" ]]; then
        read -p "Continue? (y/N): " -n 1 -r
        echo
        if [[ ! $REPLY =~ ^[Yy]$ ]]; then
            print_error "Aborted by user"
            exit 1
        fi
    else
        print_info "Skipping confirmation (--yes flag provided)"
    fi

    echo
    # Bootstrap CDK
    bootstrap_cdk "$ENV_NAME" "$REGION" "$STACK_NAME" "$DESCRIPTION" "$QUALIFIER"

    echo
    print_success "CDK bootstrap setup completed for $ENV_NAME environment!"
    print_info "You can now deploy your CDK stacks to this environment."
}

# Function to show interactive menu
show_interactive_menu() {
    print_header

    echo "Select environment to bootstrap CDK:"
    echo
    echo "1) Development (us-west-2)"
    echo "2) Staging (us-west-2)"
    echo "3) Production (us-east-1)"
    echo "4) Exit"
    echo
    read -p "Enter choice (1-4): " menu_choice

    case $menu_choice in
        1)
            bootstrap_environment "development"
            ;;
        2)
            bootstrap_environment "staging"
            ;;
        3)
            bootstrap_environment "production"
            ;;
        4)
            print_info "Exiting..."
            exit 0
            ;;
        *)
            print_error "Invalid choice. Please run the script again."
            exit 1
            ;;
    esac
}

# Global flag for skipping confirmations
SKIP_CONFIRMATIONS=false

# Parse command line arguments
parse_arguments() {
    local env_arg=""
    while [[ $# -gt 0 ]]; do
        case $1 in
            -h|--help)
                show_usage
                exit 0
                ;;
            --yes)
                SKIP_CONFIRMATIONS=true
                shift
                ;;
            dev|development|staging|prod|production)
                env_arg="$1"
                shift
                ;;
            *)
                print_error "Unknown option: $1"
                show_usage
                exit 1
                ;;
        esac
    done

    if [[ -n "$env_arg" ]]; then
        bootstrap_environment "$env_arg"
        exit 0
    fi
}

# Main script
main() {
    # If arguments provided, parse them
    if [[ $# -gt 0 ]]; then
        parse_arguments "$@"
    else
        # No arguments, show interactive menu
        show_interactive_menu
    fi
}

# Check if jq is available (optional, for better JSON parsing)
if ! command -v jq >/dev/null 2>&1; then
    print_warning "jq is not installed. JSON output will be less formatted."
    print_info "Install jq for better output: brew install jq (macOS) or apt-get install jq (Ubuntu)"
    echo
fi

# Run main function
main "$@"
