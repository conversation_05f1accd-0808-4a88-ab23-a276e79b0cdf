#!/bin/bash

# Cleanup script for failed CDK bootstrap stacks
# This script requires administrator credentials to delete stuck IAM roles

set -e

# Color codes for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Print functions
print_error() {
    echo -e "${RED}❌ $1${NC}"
}

print_success() {
    echo -e "${GREEN}✅ $1${NC}"
}

print_warning() {
    echo -e "${YELLOW}⚠️  $1${NC}"
}

print_info() {
    echo -e "${BLUE}ℹ️  $1${NC}"
}

# Function to check if we have admin credentials
check_admin_credentials() {
    print_info "Checking AWS credentials..."
    
    # Check if we can list IAM roles (requires admin permissions)
    if aws iam list-roles --max-items 1 >/dev/null 2>&1; then
        print_success "Administrator credentials detected"
        return 0
    else
        print_error "Administrator credentials required"
        echo "This script needs administrator credentials to delete stuck IAM roles."
        echo "Please configure AWS CLI with administrator credentials and try again."
        echo ""
        echo "Example:"
        echo "  aws configure --profile admin"
        echo "  export AWS_PROFILE=admin"
        echo "  ./setup/cleanup-failed-cdk.sh"
        exit 1
    fi
}

# Function to cleanup failed CDK stack
cleanup_cdk_stack() {
    local stack_name="$1"
    local region="$2"
    local account_id="$3"
    
    print_info "Cleaning up failed CDK stack: $stack_name"
    print_info "Region: $region"
    print_info "Account: $account_id"
    
    # List of CDK bootstrap IAM roles that might be stuck
    local role_patterns=(
        "cdk-*-cfn-exec-role-${account_id}-${region}"
        "cdk-*-image-publishing-role-${account_id}-${region}"
        "cdk-*-file-publishing-role-${account_id}-${region}"
        "cdk-*-lookup-role-${account_id}-${region}"
    )
    
    # Also check for roles without the account/region suffix
    local simple_roles=(
        "CloudFormationExecutionRole"
        "ImagePublishingRole"
        "FilePublishingRole"
        "LookupRole"
    )
    
    print_info "Searching for stuck IAM roles..."
    
    # Get all roles and filter for CDK-related ones
    local all_roles=$(aws iam list-roles --query 'Roles[].RoleName' --output text 2>/dev/null || echo "")
    
    for role in $all_roles; do
        # Check if role matches CDK patterns
        if [[ $role == cdk-*-*-role-${account_id}-${region} ]] || [[ " ${simple_roles[@]} " =~ " ${role} " ]]; then
            print_info "Found potential stuck role: $role"
            
            # Check if role has the CDK stack in its description or tags
            local role_info=$(aws iam get-role --role-name "$role" 2>/dev/null || echo "")
            if [[ $role_info == *"CDK"* ]] || [[ $role_info == *"Bootstrap"* ]]; then
                print_warning "Deleting stuck CDK role: $role"
                
                # Detach all managed policies
                local attached_policies=$(aws iam list-attached-role-policies --role-name "$role" --query 'AttachedPolicies[].PolicyArn' --output text 2>/dev/null || echo "")
                for policy_arn in $attached_policies; do
                    print_info "Detaching policy: $policy_arn"
                    aws iam detach-role-policy --role-name "$role" --policy-arn "$policy_arn" 2>/dev/null || true
                done
                
                # Delete inline policies
                local inline_policies=$(aws iam list-role-policies --role-name "$role" --query 'PolicyNames' --output text 2>/dev/null || echo "")
                for policy_name in $inline_policies; do
                    print_info "Deleting inline policy: $policy_name"
                    aws iam delete-role-policy --role-name "$role" --policy-name "$policy_name" 2>/dev/null || true
                done
                
                # Delete the role
                if aws iam delete-role --role-name "$role" 2>/dev/null; then
                    print_success "Successfully deleted role: $role"
                else
                    print_warning "Could not delete role: $role"
                fi
            fi
        fi
    done
    
    # Now try to delete the CloudFormation stack
    print_info "Attempting to delete CloudFormation stack: $stack_name"
    if aws cloudformation delete-stack --stack-name "$stack_name" --region "$region" 2>/dev/null; then
        print_info "Stack deletion initiated. Waiting for completion..."
        
        # Wait for stack deletion
        local max_wait=300  # 5 minutes
        local wait_time=0
        local unknown_count=0  # Track consecutive UNKNOWN status

        print_info "Waiting for stack deletion to complete..."

        while [ $wait_time -lt $max_wait ]; do
            # Try to describe the stack
            local stack_output=$(aws cloudformation describe-stacks --stack-name "$stack_name" --region "$region" 2>&1)
            local exit_code=$?

            # If the command fails, check if it's because the stack doesn't exist
            if [ $exit_code -ne 0 ]; then
                # Check for various "stack not found" messages
                if echo "$stack_output" | grep -q -E "(does not exist|ValidationError|Stack.*not exist|No stack found)"; then
                    print_success "Stack successfully deleted (no longer exists)"
                    return 0
                else
                    print_warning "Error checking stack status: $stack_output"
                    # Continue waiting in case it's a temporary error
                fi
            else
                # Stack still exists, check its status
                local stack_status=$(echo "$stack_output" | jq -r '.Stacks[0].StackStatus' 2>/dev/null || echo "UNKNOWN")

                if [ "$stack_status" = "DELETE_COMPLETE" ]; then
                    print_success "Stack deletion completed"
                    # Wait a bit more to ensure it's fully gone
                    sleep 5
                    return 0
                elif [ "$stack_status" = "DELETE_FAILED" ]; then
                    print_error "Stack deletion failed with status: $stack_status"
                    return 1
                elif [ "$stack_status" = "DELETE_IN_PROGRESS" ]; then
                    unknown_count=0  # Reset unknown counter
                    print_info "Stack deletion in progress..."
                elif [ "$stack_status" = "UNKNOWN" ] || [ "$stack_status" = "null" ] || [ -z "$stack_status" ]; then
                    unknown_count=$((unknown_count + 1))
                    print_info "Stack status unknown - likely deleted (attempt $unknown_count)"

                    # After 3 consecutive UNKNOWN status, assume it's deleted
                    if [ $unknown_count -ge 3 ]; then
                        print_success "Stack appears to be deleted (status unknown for 3 checks)"
                        return 0
                    fi

                    # Try one more check to be sure
                    sleep 5
                    local recheck=$(aws cloudformation describe-stacks --stack-name "$stack_name" --region "$region" 2>&1)
                    if [ $? -ne 0 ]; then
                        print_success "Stack confirmed deleted"
                        return 0
                    else
                        print_info "Stack still exists but status unknown - continuing to wait..."
                    fi
                else
                    print_info "Stack status: $stack_status"
                fi
            fi

            sleep 10
            wait_time=$((wait_time + 10))
        done
        
        print_warning "Stack deletion timed out"
        return 1
    else
        print_warning "Could not initiate stack deletion"
        return 1
    fi
}

# Main script
main() {
    echo
    echo "=============================================="
    echo "  CDK Bootstrap Stack Cleanup"
    echo "=============================================="
    echo
    
    # Check for required tools
    if ! command -v aws &> /dev/null; then
        print_error "AWS CLI is required but not installed"
        exit 1
    fi
    
    if ! command -v jq &> /dev/null; then
        print_error "jq is required but not installed"
        exit 1
    fi
    
    # Check admin credentials
    check_admin_credentials
    
    # Get current AWS account and region
    local account_id=$(aws sts get-caller-identity --query Account --output text 2>/dev/null || echo "")
    local region="${2:-$(aws configure get region 2>/dev/null || echo "us-west-2")}"
    
    if [ -z "$account_id" ]; then
        print_error "Could not determine AWS account ID"
        exit 1
    fi
    
    print_info "AWS Account: $account_id"
    print_info "AWS Region: $region"
    
    # Stack name to clean up (use argument if provided, otherwise default)
    local stack_name="${1:-CDKToolkit-GameFlex-Development}"
    
    # Check if stack exists and is in failed state
    local stack_status=$(aws cloudformation describe-stacks --stack-name "$stack_name" --region "$region" --query 'Stacks[0].StackStatus' --output text 2>/dev/null || echo "NOT_FOUND")
    
    if [ "$stack_status" = "NOT_FOUND" ]; then
        print_success "Stack $stack_name does not exist - nothing to clean up"
        exit 0
    elif [ "$stack_status" = "DELETE_FAILED" ] || [ "$stack_status" = "ROLLBACK_FAILED" ]; then
        print_warning "Stack $stack_name is in failed state: $stack_status"
        echo
        read -p "Do you want to clean up this stack? (y/N): " -n 1 -r
        echo
        if [[ $REPLY =~ ^[Yy]$ ]]; then
            cleanup_cdk_stack "$stack_name" "$region" "$account_id"
        else
            print_info "Cleanup cancelled"
            exit 0
        fi
    else
        print_info "Stack $stack_name is in state: $stack_status"
        print_info "This script only cleans up failed stacks"
        exit 0
    fi
    
    echo
    print_success "Cleanup completed! You can now run bootstrap-cdk.sh again."
}

# Run main function
main "$@"
