# GameFlex Bootstrap and User Management

This directory contains the automated bootstrap and user management system for GameFlex backend infrastructure.

## Overview

The new system automates user and policy management using administrator credentials to:

1. **Create GitLab CI Users** - Automatically creates IAM users for each environment
2. **Apply Environment Policies** - Attaches strict, environment-specific IAM policies
3. **Generate Access Keys** - Creates access keys for GitLab CI/CD variables
4. **Individual CDK Bootstrap** - Users handle their own CDK bootstrapping with generated credentials

## Files

### Scripts
- **`bootstrap.sh`** - Main bootstrap script that handles user and policy setup (uses admin credentials)
- **`bootstrap-cdk.sh`** - CDK bootstrap script for individual environments (uses environment-specific credentials)

### Policies
- **`policies/development-policy.json`** - IAM policy for development environment
- **`policies/staging-policy.json`** - IAM policy for staging environment  
- **`policies/production-policy.json`** - IAM policy for production environment

### Documentation
- **`SETUP.md`** - Streamlined setup guide for the new system
- **`README.md`** - This file

### Legacy Files (Deprecated)
- **`setup-guide.md`** - Old detailed setup guide (replaced by SETUP.md)
- **`aws-security-checklist.md`** - Security checklist (still relevant)
- **`ci-variables.md`** - CI variables documentation (still relevant)
- **`security-policy.md`** - Security policy documentation (still relevant)

## Quick Start

1. **Configure administrator credentials** in `backend/.env`:
   ```bash
   AWS_ADMIN_ACCESS_KEY_ID=********************
   AWS_ADMIN_SECRET_ACCESS_KEY=An5pe0X0vdyPZXWIRbIYu/dcYdBd4o/BMGnp1yke
   AWS_ACCOUNT_ID=************
   ```

2. **Run the user bootstrap script**:
   ```bash
   cd backend
   ./setup/bootstrap.sh
   ```

3. **Configure GitLab CI/CD variables** with the generated access keys

4. **Add environment-specific credentials** to your `.env` file:
   ```bash
   # Add the generated access keys to .env
   AWS_ACCESS_KEY_ID_DEV=AKIA...
   AWS_SECRET_ACCESS_KEY_DEV=...
   AWS_ACCESS_KEY_ID_STAGING=AKIA...
   AWS_SECRET_ACCESS_KEY_STAGING=...
   AWS_ACCESS_KEY_ID_PROD=AKIA...
   AWS_SECRET_ACCESS_KEY_PROD=...
   ```

5. **Bootstrap CDK for each environment**:
   ```bash
   # Interactive menu
   ./setup/bootstrap-cdk.sh

   # Or directly specify environment
   ./setup/bootstrap-cdk.sh dev
   ./setup/bootstrap-cdk.sh staging
   ./setup/bootstrap-cdk.sh prod
   ```

## Key Improvements

### Automated User Management
- No more manual IAM user creation
- Automatic policy attachment and updates
- Environment-specific access key generation

### Environment-Based Security
- Strict resource-based restrictions using naming patterns
- Regional access controls
- Cross-environment access prevention

### Simplified Setup Process
- Single command bootstrap
- Automatic dependency validation
- Clear error messages and guidance

### Policy Tracking
- Version-controlled policy files
- Automatic policy updates
- Policy version management

## Environment Isolation

Each environment has strict isolation:

### Development (`gameflex-dev-*`, `gameflex-development-*`)
- Region: `us-west-2`
- User: `gameflex-ci-development`
- Policy: `GameFlexDevelopmentCIPolicy`

### Staging (`gameflex-staging-*`)
- Region: `us-west-2`
- User: `gameflex-ci-staging`
- Policy: `GameFlexStagingCIPolicy`

### Production (`gameflex-prod-*`, `gameflex-production-*`)
- Region: `us-east-1`
- User: `gameflex-ci-production`
- Policy: `GameFlexProductionCIPolicy`

## Security Features

### Resource Scoping
All permissions are scoped to specific resource patterns:
```json
"Resource": [
  "arn:aws:lambda:*:*:function:gameflex-dev-*",
  "arn:aws:dynamodb:*:*:table/gameflex-dev-*"
]
```

### Explicit Denies
Cross-environment access is explicitly denied:
```json
"Effect": "Deny",
"Resource": [
  "arn:aws:*:*:*:*staging*",
  "arn:aws:*:*:*:*prod*"
]
```

### Regional Restrictions
Operations are restricted to appropriate regions:
```json
"Condition": {
  "StringEquals": {
    "aws:RequestedRegion": ["us-west-2"]
  }
}
```

## Usage Examples

### Create/Update Users and Policies
```bash
./setup/bootstrap.sh                # Create all users and policies
./setup/bootstrap.sh development    # Create user for specific environment
```

### Bootstrap CDK (Interactive Mode)
```bash
./setup/bootstrap-cdk.sh
# Shows menu to select environment for CDK bootstrapping
```

### Bootstrap CDK for Specific Environment
```bash
./setup/bootstrap-cdk.sh dev        # Development
./setup/bootstrap-cdk.sh staging    # Staging
./setup/bootstrap-cdk.sh prod       # Production
```

### Update Policies
Edit the policy files in `policies/` and run:
```bash
./setup/bootstrap.sh
```

## Migration from Old System

If you're migrating from the old manual setup:

1. **Backup existing access keys** from GitLab CI/CD variables
2. **Run the new bootstrap script** to create standardized users
3. **Update GitLab CI/CD variables** with new access keys
4. **Test deployments** to ensure everything works
5. **Clean up old IAM users** if desired

## Troubleshooting

### Common Issues

**Bootstrap fails with permission errors**
- Verify administrator credentials in `.env`
- Ensure the admin user has sufficient permissions

**CDK bootstrap fails**
- Check that Node.js and CDK CLI are installed
- Verify AWS CLI configuration

**Policy updates don't take effect**
- IAM policy changes can take a few minutes to propagate
- Try running the deployment again after a few minutes

### Getting Help

1. Check the error messages - they include specific guidance
2. Review the `SETUP.md` file for detailed instructions
3. Verify prerequisites are met (AWS CLI, Node.js, CDK CLI)
4. Ensure `.env` file is properly configured

## Contributing

When updating policies:

1. **Edit the JSON files** in `policies/`
2. **Test changes** in development first
3. **Run the user management script** to apply updates
4. **Verify permissions** work as expected
5. **Document any breaking changes**

The system automatically manages policy versions and cleans up old versions to stay within AWS limits.
