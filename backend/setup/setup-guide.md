# GameFlex Backend CI/CD Setup Guide

This guide will help you set up the complete CI/CD pipeline for the GameFlex backend using GitLab CI/CD with integrated security scanning.

## ⚠️ Important: Optimized IAM Policies

The IAM policies in this guide have been optimized for security and functionality:

### **Bootstrap Strategy**
- **CDK Bootstrap**: Uses temporary AdministratorAccess for the bootstrap process only
- **CI/CD Operations**: Uses minimal, scoped permissions for day-to-day operations
- **Environment Isolation**: Strict resource-level restrictions prevent cross-environment access

### **Policy Features**
- **Minimal Permissions**: Only includes permissions necessary for CI/CD operations
- **Resource Scoping**: All permissions are scoped to specific resources where possible
- **Regional Restrictions**: Prevents operations outside designated regions
- **Environment Isolation**: Deny statements prevent access to other environments

If you previously created IAM policies from an earlier version of this guide, you should update them with the current optimized versions.

## Overview

The pipeline includes:
- ✅ **SAST (Static Application Security Testing)** - Scans code for security vulnerabilities
- ✅ **Secrets Detection** - Prevents hardcoded secrets from being committed
- ✅ **Dependency Scanning** - Checks for vulnerable dependencies
- ✅ **License Scanning** - Ensures license compliance
- ✅ **Automated Testing** - Unit and integration tests
- ✅ **Multi-environment Deployment** - Development, Staging, and Production
- ✅ **Security Gates** - Blocks deployments if critical issues are found

## Prerequisites

### 1. AWS Account Strategy

**Recommended Approach**: Use separate AWS accounts for maximum isolation:
- **Development Account** - Completely isolated development environment
- **Staging Account** - Pre-production testing environment  
- **Production Account** - Live production environment

**Alternative Approach**: Single account with strict IAM isolation (less secure but more cost-effective for smaller teams)

### 2. Environment Isolation Strategy

#### Regional Separation
- **Development**: `us-west-2` (Oregon)
- **Staging**: `us-west-2` (Oregon) 
- **Production**: `us-east-1` (N. Virginia) - For better global performance and compliance

#### Resource Naming Convention
- **Development**: `gameflex-dev-*`
- **Staging**: `gameflex-staging-*`
- **Production**: `gameflex-prod-*`

### 3. IAM Users and Policies Setup

#### Step 3.1: Create Development IAM User

1. **Login to Development AWS Account** (or main account if using single account)
2. **Navigate to IAM** → Users → Create User
3. **User Details**:
   - Username: `gameflex-ci-development`
   - Access type: Programmatic access only
   - No console access needed

4. **Create Development Policy**:
   - Policy name: `GameFlexDevelopmentCIPolicy`
   - Copy the policy below:

```json
{
  "Version": "2012-10-17",
  "Statement": [
    {
      "Sid": "CloudFormationAccess",
      "Effect": "Allow",
      "Action": [
        "cloudformation:CreateStack",
        "cloudformation:UpdateStack",
        "cloudformation:DeleteStack",
        "cloudformation:DescribeStacks",
        "cloudformation:DescribeStackEvents",
        "cloudformation:DescribeStackResources",
        "cloudformation:DescribeStackResource",
        "cloudformation:GetTemplate",
        "cloudformation:ListStacks",
        "cloudformation:ListStackResources",
        "cloudformation:ValidateTemplate",
        "cloudformation:CreateChangeSet",
        "cloudformation:DescribeChangeSet",
        "cloudformation:ExecuteChangeSet",
        "cloudformation:DeleteChangeSet",
        "cloudformation:ListChangeSets",
        "cloudformation:GetTemplateSummary"
      ],
      "Resource": [
        "arn:aws:cloudformation:us-west-2:************:stack/gameflex-dev-*/*",
        "arn:aws:cloudformation:us-west-2:************:stack/CDKToolkit-GameFlex-Development/*"
      ]
    },
    {
      "Sid": "CloudFormationListGlobal",
      "Effect": "Allow",
      "Action": [
        "cloudformation:ListStacks",
        "cloudformation:DescribeStacks"
      ],
      "Resource": "*"
    },
    {
      "Sid": "S3BucketAccess",
      "Effect": "Allow",
      "Action": [
        "s3:CreateBucket",
        "s3:DeleteBucket",
        "s3:GetBucketLocation",
        "s3:GetBucketPolicy",
        "s3:GetBucketVersioning",
        "s3:PutBucketPolicy",
        "s3:PutBucketVersioning",
        "s3:ListBucket"
      ],
      "Resource": [
        "arn:aws:s3:::gameflex-dev-*",
        "arn:aws:s3:::cdk-hnb659fds-assets-************-us-west-2"
      ]
    },
    {
      "Sid": "S3ObjectAccess",
      "Effect": "Allow",
      "Action": [
        "s3:GetObject",
        "s3:PutObject",
        "s3:DeleteObject"
      ],
      "Resource": [
        "arn:aws:s3:::gameflex-dev-*/*",
        "arn:aws:s3:::cdk-hnb659fds-assets-************-us-west-2/*"
      ]
    },
    {
      "Sid": "S3ListGlobal",
      "Effect": "Allow",
      "Action": [
        "s3:ListAllMyBuckets",
        "s3:GetBucketLocation"
      ],
      "Resource": "*"
    },
    {
      "Sid": "LambdaAccess",
      "Effect": "Allow",
      "Action": [
        "lambda:CreateFunction",
        "lambda:DeleteFunction",
        "lambda:GetFunction",
        "lambda:GetFunctionConfiguration",
        "lambda:UpdateFunctionCode",
        "lambda:UpdateFunctionConfiguration",
        "lambda:ListFunctions",
        "lambda:TagResource",
        "lambda:UntagResource",
        "lambda:AddPermission",
        "lambda:RemovePermission"
      ],
      "Resource": "arn:aws:lambda:us-west-2:************:function:gameflex-dev-*"
    },
    {
      "Sid": "APIGatewayAccess",
      "Effect": "Allow",
      "Action": [
        "apigateway:GET",
        "apigateway:POST",
        "apigateway:PUT",
        "apigateway:DELETE",
        "apigateway:PATCH"
      ],
      "Resource": "arn:aws:apigateway:us-west-2::/restapis/*"
    },
    {
      "Sid": "DynamoDBAccess",
      "Effect": "Allow",
      "Action": [
        "dynamodb:CreateTable",
        "dynamodb:DeleteTable",
        "dynamodb:DescribeTable",
        "dynamodb:UpdateTable",
        "dynamodb:TagResource",
        "dynamodb:UntagResource",
        "dynamodb:ListTables"
      ],
      "Resource": "arn:aws:dynamodb:us-west-2:************:table/gameflex-dev-*"
    },
    {
      "Sid": "CognitoAccess",
      "Effect": "Allow",
      "Action": [
        "cognito-idp:CreateUserPool",
        "cognito-idp:DeleteUserPool",
        "cognito-idp:DescribeUserPool",
        "cognito-idp:UpdateUserPool",
        "cognito-idp:CreateUserPoolClient",
        "cognito-idp:DeleteUserPoolClient",
        "cognito-idp:DescribeUserPoolClient",
        "cognito-idp:UpdateUserPoolClient",
        "cognito-idp:TagResource",
        "cognito-idp:UntagResource"
      ],
      "Resource": "arn:aws:cognito-idp:us-west-2:************:userpool/*"
    },
    {
      "Sid": "SecretsManagerAccess",
      "Effect": "Allow",
      "Action": [
        "secretsmanager:CreateSecret",
        "secretsmanager:DeleteSecret",
        "secretsmanager:DescribeSecret",
        "secretsmanager:GetSecretValue",
        "secretsmanager:PutSecretValue",
        "secretsmanager:UpdateSecret",
        "secretsmanager:TagResource",
        "secretsmanager:UntagResource"
      ],
      "Resource": "arn:aws:secretsmanager:us-west-2:************:secret:gameflex-dev-*"
    },
    {
      "Sid": "IAMRoleAccess",
      "Effect": "Allow",
      "Action": [
        "iam:CreateRole",
        "iam:DeleteRole",
        "iam:GetRole",
        "iam:PassRole",
        "iam:AttachRolePolicy",
        "iam:DetachRolePolicy",
        "iam:PutRolePolicy",
        "iam:DeleteRolePolicy",
        "iam:GetRolePolicy",
        "iam:ListRolePolicies",
        "iam:ListAttachedRolePolicies",
        "iam:TagRole",
        "iam:UntagRole",
        "iam:CreateInstanceProfile",
        "iam:DeleteInstanceProfile",
        "iam:AddRoleToInstanceProfile",
        "iam:RemoveRoleFromInstanceProfile",
        "iam:GetInstanceProfile"
      ],
      "Resource": [
        "arn:aws:iam::************:role/gameflex-dev-*",
        "arn:aws:iam::************:role/cdk-*",
        "arn:aws:iam::************:instance-profile/cdk-*"
      ]
    },
    {
      "Sid": "SSMAccess",
      "Effect": "Allow",
      "Action": [
        "ssm:GetParameter",
        "ssm:GetParameters",
        "ssm:PutParameter",
        "ssm:DeleteParameter",
        "ssm:DescribeParameters",
        "ssm:GetParameterHistory",
        "ssm:AddTagsToResource",
        "ssm:RemoveTagsFromResource"
      ],
      "Resource": [
        "arn:aws:ssm:us-west-2:************:parameter/cdk-bootstrap/*",
        "arn:aws:ssm:us-west-2:************:parameter/gameflex-dev/*"
      ]
    },
    {
      "Sid": "ECRAccess",
      "Effect": "Allow",
      "Action": [
        "ecr:CreateRepository",
        "ecr:DeleteRepository",
        "ecr:DescribeRepositories",
        "ecr:ListImages",
        "ecr:DescribeImages",
        "ecr:BatchGetImage",
        "ecr:GetDownloadUrlForLayer",
        "ecr:BatchCheckLayerAvailability",
        "ecr:PutImage",
        "ecr:InitiateLayerUpload",
        "ecr:UploadLayerPart",
        "ecr:CompleteLayerUpload",
        "ecr:SetRepositoryPolicy",
        "ecr:GetRepositoryPolicy",
        "ecr:DeleteRepositoryPolicy"
      ],
      "Resource": [
        "arn:aws:ecr:us-west-2:************:repository/cdk-*",
        "arn:aws:ecr:us-west-2:************:repository/gameflex-dev-*"
      ]
    },
    {
      "Sid": "ECRAuthToken",
      "Effect": "Allow",
      "Action": [
        "ecr:GetAuthorizationToken"
      ],
      "Resource": "*"
    },
    {
      "Sid": "IAMListGlobal",
      "Effect": "Allow",
      "Action": [
        "iam:ListRoles",
        "iam:ListPolicies",
        "iam:ListInstanceProfiles"
      ],
      "Resource": "*"
    },
    {
      "Sid": "RegionRestriction",
      "Effect": "Deny",
      "Action": "*",
      "Resource": "*",
      "Condition": {
        "StringNotEquals": {
          "aws:RequestedRegion": "us-west-2"
        }
      }
    },
    {
      "Sid": "PreventOtherEnvironmentAccess",
      "Effect": "Deny",
      "NotAction": [
        "iam:ListRoles",
        "iam:ListPolicies",
        "iam:ListInstanceProfiles",
        "iam:ListGroupsForUser",
        "iam:ListAttachedGroupPolicies",
        "iam:ListAttachedUserPolicies",
        "s3:ListAllMyBuckets",
        "cloudformation:ListStacks",
        "cloudformation:DescribeStacks",
        "ecr:GetAuthorizationToken"
      ],
      "Resource": [
        "arn:aws:cloudformation:*:*:stack/gameflex-prod-*/*",
        "arn:aws:cloudformation:*:*:stack/gameflex-staging-*/*",
        "arn:aws:s3:::gameflex-prod-*",
        "arn:aws:s3:::gameflex-prod-*/*",
        "arn:aws:s3:::gameflex-staging-*",
        "arn:aws:s3:::gameflex-staging-*/*",
        "arn:aws:lambda:*:*:function:gameflex-prod-*",
        "arn:aws:lambda:*:*:function:gameflex-staging-*",
        "arn:aws:dynamodb:*:*:table/gameflex-prod-*",
        "arn:aws:dynamodb:*:*:table/gameflex-staging-*",
        "arn:aws:iam::*:role/gameflex-prod-*",
        "arn:aws:iam::*:role/gameflex-staging-*"
      ]
    }
  ]
}
```

#### Step 3.2: Create Staging IAM User

1. **Login to Staging AWS Account** (or main account)
2. **Navigate to IAM** → Users → Create User
3. **User Details**:
   - Username: `gameflex-ci-staging`
   - Access type: Programmatic access only

4. **Create Staging Policy**:
   - Policy name: `GameFlexStagingCIPolicy`

```json
{
  "Version": "2012-10-17",
  "Statement": [
    {
      "Sid": "CloudFormationAccess",
      "Effect": "Allow",
      "Action": [
        "cloudformation:CreateStack",
        "cloudformation:UpdateStack",
        "cloudformation:DeleteStack",
        "cloudformation:DescribeStacks",
        "cloudformation:DescribeStackEvents",
        "cloudformation:DescribeStackResources",
        "cloudformation:DescribeStackResource",
        "cloudformation:GetTemplate",
        "cloudformation:ListStacks",
        "cloudformation:ListStackResources",
        "cloudformation:ValidateTemplate",
        "cloudformation:CreateChangeSet",
        "cloudformation:DescribeChangeSet",
        "cloudformation:ExecuteChangeSet",
        "cloudformation:DeleteChangeSet",
        "cloudformation:ListChangeSets",
        "cloudformation:GetTemplateSummary"
      ],
      "Resource": [
        "arn:aws:cloudformation:us-west-2:************:stack/gameflex-staging-*/*",
        "arn:aws:cloudformation:us-west-2:************:stack/CDKToolkit-GameFlex-Staging/*"
      ]
    },
    {
      "Sid": "CloudFormationListGlobal",
      "Effect": "Allow",
      "Action": [
        "cloudformation:ListStacks",
        "cloudformation:DescribeStacks"
      ],
      "Resource": "*"
    },
    {
      "Sid": "S3BucketAccess",
      "Effect": "Allow",
      "Action": [
        "s3:GetBucketLocation",
        "s3:GetBucketPolicy",
        "s3:GetBucketVersioning",
        "s3:ListBucket"
      ],
      "Resource": [
        "arn:aws:s3:::gameflex-staging-*",
        "arn:aws:s3:::cdk-hnb659fds-assets-************-us-west-2"
      ]
    },
    {
      "Sid": "S3ObjectAccess",
      "Effect": "Allow",
      "Action": [
        "s3:GetObject",
        "s3:PutObject",
        "s3:DeleteObject"
      ],
      "Resource": [
        "arn:aws:s3:::gameflex-staging-*/*",
        "arn:aws:s3:::cdk-hnb659fds-assets-************-us-west-2/*"
      ]
    },
    {
      "Sid": "S3ListGlobal",
      "Effect": "Allow",
      "Action": [
        "s3:ListAllMyBuckets",
        "s3:GetBucketLocation"
      ],
      "Resource": "*"
    },
    {
      "Sid": "LambdaAccess",
      "Effect": "Allow",
      "Action": [
        "lambda:GetFunction",
        "lambda:GetFunctionConfiguration",
        "lambda:UpdateFunctionCode",
        "lambda:UpdateFunctionConfiguration",
        "lambda:ListFunctions"
      ],
      "Resource": "arn:aws:lambda:us-west-2:************:function:gameflex-staging-*"
    },
    {
      "Sid": "APIGatewayAccess",
      "Effect": "Allow",
      "Action": [
        "apigateway:GET",
        "apigateway:POST",
        "apigateway:PUT",
        "apigateway:PATCH"
      ],
      "Resource": "arn:aws:apigateway:us-west-2::/restapis/*"
    },
    {
      "Sid": "DynamoDBAccess",
      "Effect": "Allow",
      "Action": [
        "dynamodb:DescribeTable",
        "dynamodb:UpdateTable"
      ],
      "Resource": "arn:aws:dynamodb:us-west-2:************:table/gameflex-staging-*"
    },
    {
      "Sid": "CognitoAccess",
      "Effect": "Allow",
      "Action": [
        "cognito-idp:DescribeUserPool",
        "cognito-idp:UpdateUserPool",
        "cognito-idp:DescribeUserPoolClient",
        "cognito-idp:UpdateUserPoolClient"
      ],
      "Resource": "arn:aws:cognito-idp:us-west-2:************:userpool/*"
    },
    {
      "Sid": "SecretsManagerAccess",
      "Effect": "Allow",
      "Action": [
        "secretsmanager:DescribeSecret",
        "secretsmanager:GetSecretValue",
        "secretsmanager:PutSecretValue",
        "secretsmanager:UpdateSecret"
      ],
      "Resource": "arn:aws:secretsmanager:us-west-2:************:secret:gameflex-staging-*"
    },
    {
      "Sid": "IAMRoleAccess",
      "Effect": "Allow",
      "Action": [
        "iam:CreateRole",
        "iam:DeleteRole",
        "iam:GetRole",
        "iam:PassRole",
        "iam:AttachRolePolicy",
        "iam:DetachRolePolicy",
        "iam:PutRolePolicy",
        "iam:DeleteRolePolicy",
        "iam:GetRolePolicy",
        "iam:ListRolePolicies",
        "iam:ListAttachedRolePolicies",
        "iam:TagRole",
        "iam:UntagRole",
        "iam:CreateInstanceProfile",
        "iam:DeleteInstanceProfile",
        "iam:AddRoleToInstanceProfile",
        "iam:RemoveRoleFromInstanceProfile",
        "iam:GetInstanceProfile"
      ],
      "Resource": [
        "arn:aws:iam::************:role/gameflex-staging-*",
        "arn:aws:iam::************:role/cdk-*",
        "arn:aws:iam::************:instance-profile/cdk-*"
      ]
    },
    {
      "Sid": "SSMAccess",
      "Effect": "Allow",
      "Action": [
        "ssm:GetParameter",
        "ssm:GetParameters",
        "ssm:PutParameter",
        "ssm:DeleteParameter",
        "ssm:DescribeParameters",
        "ssm:GetParameterHistory",
        "ssm:AddTagsToResource",
        "ssm:RemoveTagsFromResource"
      ],
      "Resource": [
        "arn:aws:ssm:us-west-2:************:parameter/cdk-bootstrap/*",
        "arn:aws:ssm:us-west-2:************:parameter/gameflex-staging/*"
      ]
    },
    {
      "Sid": "ECRAccess",
      "Effect": "Allow",
      "Action": [
        "ecr:CreateRepository",
        "ecr:DeleteRepository",
        "ecr:DescribeRepositories",
        "ecr:ListImages",
        "ecr:DescribeImages",
        "ecr:BatchGetImage",
        "ecr:GetDownloadUrlForLayer",
        "ecr:BatchCheckLayerAvailability",
        "ecr:PutImage",
        "ecr:InitiateLayerUpload",
        "ecr:UploadLayerPart",
        "ecr:CompleteLayerUpload",
        "ecr:SetRepositoryPolicy",
        "ecr:GetRepositoryPolicy",
        "ecr:DeleteRepositoryPolicy"
      ],
      "Resource": [
        "arn:aws:ecr:us-west-2:************:repository/cdk-*",
        "arn:aws:ecr:us-west-2:************:repository/gameflex-staging-*"
      ]
    },
    {
      "Sid": "ECRAuthToken",
      "Effect": "Allow",
      "Action": [
        "ecr:GetAuthorizationToken"
      ],
      "Resource": "*"
    },
    {
      "Sid": "IAMListGlobal",
      "Effect": "Allow",
      "Action": [
        "iam:ListRoles",
        "iam:ListPolicies",
        "iam:ListInstanceProfiles"
      ],
      "Resource": "*"
    },
    {
      "Sid": "RegionRestriction",
      "Effect": "Deny",
      "Action": "*",
      "Resource": "*",
      "Condition": {
        "StringNotEquals": {
          "aws:RequestedRegion": "us-west-2"
        }
      }
    },
    {
      "Sid": "PreventOtherEnvironmentAccess",
      "Effect": "Deny",
      "NotAction": [
        "iam:ListRoles",
        "iam:ListPolicies",
        "iam:ListInstanceProfiles",
        "s3:ListAllMyBuckets",
        "cloudformation:ListStacks",
        "cloudformation:DescribeStacks",
        "ecr:GetAuthorizationToken"
      ],
      "Resource": [
        "arn:aws:cloudformation:*:*:stack/gameflex-dev-*/*",
        "arn:aws:cloudformation:*:*:stack/gameflex-prod-*/*",
        "arn:aws:s3:::gameflex-dev-*",
        "arn:aws:s3:::gameflex-dev-*/*",
        "arn:aws:s3:::gameflex-prod-*",
        "arn:aws:s3:::gameflex-prod-*/*",
        "arn:aws:lambda:*:*:function:gameflex-dev-*",
        "arn:aws:lambda:*:*:function:gameflex-prod-*",
        "arn:aws:dynamodb:*:*:table/gameflex-dev-*",
        "arn:aws:dynamodb:*:*:table/gameflex-prod-*",
        "arn:aws:iam::*:role/gameflex-dev-*",
        "arn:aws:iam::*:role/gameflex-prod-*"
      ]
    }
  ]
}
```

#### Step 3.3: Create Production IAM User

1. **Login to Production AWS Account** (or main account)
2. **Navigate to IAM** → Users → Create User
3. **User Details**:
   - Username: `gameflex-ci-production`
   - Access type: Programmatic access only

4. **Create Production Policy**:
   - Policy name: `GameFlexProductionCIPolicy`

```json
{
  "Version": "2012-10-17",
  "Statement": [
    {
      "Sid": "CloudFormationAccess",
      "Effect": "Allow",
      "Action": [
        "cloudformation:CreateStack",
        "cloudformation:UpdateStack",
        "cloudformation:DeleteStack",
        "cloudformation:DescribeStacks",
        "cloudformation:DescribeStackEvents",
        "cloudformation:DescribeStackResources",
        "cloudformation:DescribeStackResource",
        "cloudformation:GetTemplate",
        "cloudformation:ListStacks",
        "cloudformation:ListStackResources",
        "cloudformation:ValidateTemplate",
        "cloudformation:CreateChangeSet",
        "cloudformation:DescribeChangeSet",
        "cloudformation:ExecuteChangeSet",
        "cloudformation:DeleteChangeSet",
        "cloudformation:ListChangeSets",
        "cloudformation:GetTemplateSummary"
      ],
      "Resource": [
        "arn:aws:cloudformation:us-east-1:************:stack/gameflex-prod-*/*",
        "arn:aws:cloudformation:us-east-1:************:stack/CDKToolkit-GameFlex-Production/*"
      ]
    },
    {
      "Sid": "CloudFormationListGlobal",
      "Effect": "Allow",
      "Action": [
        "cloudformation:ListStacks",
        "cloudformation:DescribeStacks"
      ],
      "Resource": "*"
    },
    {
      "Sid": "S3BucketAccess",
      "Effect": "Allow",
      "Action": [
        "s3:GetBucketLocation",
        "s3:GetBucketVersioning",
        "s3:ListBucket"
      ],
      "Resource": [
        "arn:aws:s3:::gameflex-prod-*",
        "arn:aws:s3:::cdk-hnb659fds-assets-************-us-east-1"
      ]
    },
    {
      "Sid": "S3ObjectAccess",
      "Effect": "Allow",
      "Action": [
        "s3:GetObject",
        "s3:PutObject"
      ],
      "Resource": [
        "arn:aws:s3:::gameflex-prod-*/*",
        "arn:aws:s3:::cdk-hnb659fds-assets-************-us-east-1/*"
      ]
    },
    {
      "Sid": "S3ListGlobal",
      "Effect": "Allow",
      "Action": [
        "s3:ListAllMyBuckets",
        "s3:GetBucketLocation"
      ],
      "Resource": "*"
    },
    {
      "Sid": "LambdaAccess",
      "Effect": "Allow",
      "Action": [
        "lambda:GetFunction",
        "lambda:GetFunctionConfiguration",
        "lambda:UpdateFunctionCode",
        "lambda:UpdateFunctionConfiguration"
      ],
      "Resource": "arn:aws:lambda:us-east-1:************:function:gameflex-prod-*"
    },
    {
      "Sid": "APIGatewayAccess",
      "Effect": "Allow",
      "Action": [
        "apigateway:GET",
        "apigateway:PATCH"
      ],
      "Resource": "arn:aws:apigateway:us-east-1::/restapis/*"
    },
    {
      "Sid": "DynamoDBAccess",
      "Effect": "Allow",
      "Action": [
        "dynamodb:DescribeTable"
      ],
      "Resource": "arn:aws:dynamodb:us-east-1:************:table/gameflex-prod-*"
    },
    {
      "Sid": "SecretsManagerAccess",
      "Effect": "Allow",
      "Action": [
        "secretsmanager:DescribeSecret",
        "secretsmanager:GetSecretValue"
      ],
      "Resource": "arn:aws:secretsmanager:us-east-1:************:secret:gameflex-prod-*"
    },
    {
      "Sid": "IAMRoleAccess",
      "Effect": "Allow",
      "Action": [
        "iam:CreateRole",
        "iam:DeleteRole",
        "iam:GetRole",
        "iam:PassRole",
        "iam:AttachRolePolicy",
        "iam:DetachRolePolicy",
        "iam:PutRolePolicy",
        "iam:DeleteRolePolicy",
        "iam:GetRolePolicy",
        "iam:ListRolePolicies",
        "iam:ListAttachedRolePolicies",
        "iam:TagRole",
        "iam:UntagRole",
        "iam:CreateInstanceProfile",
        "iam:DeleteInstanceProfile",
        "iam:AddRoleToInstanceProfile",
        "iam:RemoveRoleFromInstanceProfile",
        "iam:GetInstanceProfile"
      ],
      "Resource": [
        "arn:aws:iam::************:role/gameflex-prod-*",
        "arn:aws:iam::************:role/cdk-*",
        "arn:aws:iam::************:instance-profile/cdk-*"
      ]
    },
    {
      "Sid": "SSMAccess",
      "Effect": "Allow",
      "Action": [
        "ssm:GetParameter",
        "ssm:GetParameters",
        "ssm:PutParameter",
        "ssm:DeleteParameter",
        "ssm:DescribeParameters",
        "ssm:GetParameterHistory",
        "ssm:AddTagsToResource",
        "ssm:RemoveTagsFromResource"
      ],
      "Resource": [
        "arn:aws:ssm:us-east-1:************:parameter/cdk-bootstrap/*",
        "arn:aws:ssm:us-east-1:************:parameter/gameflex-prod/*"
      ]
    },
    {
      "Sid": "ECRAccess",
      "Effect": "Allow",
      "Action": [
        "ecr:CreateRepository",
        "ecr:DeleteRepository",
        "ecr:DescribeRepositories",
        "ecr:ListImages",
        "ecr:DescribeImages",
        "ecr:BatchGetImage",
        "ecr:GetDownloadUrlForLayer",
        "ecr:BatchCheckLayerAvailability",
        "ecr:PutImage",
        "ecr:InitiateLayerUpload",
        "ecr:UploadLayerPart",
        "ecr:CompleteLayerUpload",
        "ecr:SetRepositoryPolicy",
        "ecr:GetRepositoryPolicy",
        "ecr:DeleteRepositoryPolicy"
      ],
      "Resource": [
        "arn:aws:ecr:us-east-1:************:repository/cdk-*",
        "arn:aws:ecr:us-east-1:************:repository/gameflex-prod-*"
      ]
    },
    {
      "Sid": "ECRAuthToken",
      "Effect": "Allow",
      "Action": [
        "ecr:GetAuthorizationToken"
      ],
      "Resource": "*"
    },
    {
      "Sid": "IAMListGlobal",
      "Effect": "Allow",
      "Action": [
        "iam:ListRoles",
        "iam:ListPolicies",
        "iam:ListInstanceProfiles"
      ],
      "Resource": "*"
    },
    {
      "Sid": "RegionRestriction",
      "Effect": "Deny",
      "Action": "*",
      "Resource": "*",
      "Condition": {
        "StringNotEquals": {
          "aws:RequestedRegion": "us-east-1"
        }
      }
    },
    {
      "Sid": "PreventOtherEnvironmentAccess",
      "Effect": "Deny",
      "NotAction": [
        "iam:ListRoles",
        "iam:ListPolicies",
        "iam:ListInstanceProfiles",
        "s3:ListAllMyBuckets",
        "cloudformation:ListStacks",
        "cloudformation:DescribeStacks",
        "ecr:GetAuthorizationToken"
      ],
      "Resource": [
        "arn:aws:cloudformation:*:*:stack/gameflex-dev-*/*",
        "arn:aws:cloudformation:*:*:stack/gameflex-staging-*/*",
        "arn:aws:s3:::gameflex-dev-*",
        "arn:aws:s3:::gameflex-dev-*/*",
        "arn:aws:s3:::gameflex-staging-*",
        "arn:aws:s3:::gameflex-staging-*/*",
        "arn:aws:lambda:*:*:function:gameflex-dev-*",
        "arn:aws:lambda:*:*:function:gameflex-staging-*",
        "arn:aws:dynamodb:*:*:table/gameflex-dev-*",
        "arn:aws:dynamodb:*:*:table/gameflex-staging-*",
        "arn:aws:iam::*:role/gameflex-dev-*",
        "arn:aws:iam::*:role/gameflex-staging-*"
      ]
    },
    {
      "Sid": "PreventDestructiveActions",
      "Effect": "Deny",
      "Action": [
        "cloudformation:DeleteStack",
        "s3:DeleteBucket",
        "s3:DeleteObject",
        "dynamodb:DeleteTable",
        "lambda:DeleteFunction",
        "cognito-idp:DeleteUserPool",
        "secretsmanager:DeleteSecret"
      ],
      "Resource": "*"
    }
  ]
}
```

#### Step 3.4: Finding Your Account Information

Before creating the policies, you need to find your AWS account information:

1. **Find Your AWS Account ID**:
   ```bash
   # Using AWS CLI
   aws sts get-caller-identity --query Account --output text

   # Or check in AWS Console: Top right corner → Account ID
   ```

2. **Find Your CDK Asset Bucket Name**:
   ```bash
   # After bootstrapping CDK, list S3 buckets
   aws s3 ls | grep cdk

   # Look for bucket like: cdk-hnb659fds-assets-************-REGION
   # The "hnb659fds" part is the CDK qualifier (may be different)
   ```

3. **Update Policy Placeholders**:
   - All account IDs have been set to `************`
   - Update CDK bucket names if the qualifier is different from `hnb659fds`

#### Step 3.5: Security Best Practices

1. **Enable MFA for Root Accounts**
   ```bash
   # For each AWS account, enable MFA on the root user
   # Use hardware MFA devices for production
   ```

2. **Create Access Keys Securely**
   ```bash
   # After creating each IAM user:
   # 1. Generate access keys
   # 2. Download credentials CSV
   # 3. Store securely (password manager)
   # 4. Delete from local downloads
   ```

3. **Set Up CloudTrail Logging**
   ```bash
   # Enable CloudTrail in each account for audit logging
   # Store logs in separate S3 bucket
   # Enable log file integrity validation
   ```

4. **Configure Account Alerts**
   ```bash
   # Set up billing alerts for unexpected costs
   # Configure CloudWatch alarms for unusual API activity
   # Enable AWS Config for compliance monitoring
   ```

## Step-by-Step Setup

### Step 1: Bootstrap CDK in Each Environment

Before configuring GitLab, you need to bootstrap AWS CDK in each environment.

#### **⚠️ Important: Temporary Administrator Access Required**

CDK bootstrap requires extensive permissions that are difficult to scope precisely. For the bootstrap process only, temporarily grant AdministratorAccess to each IAM user:

**Grant Temporary Administrator Access:**
```bash
# For Development User
aws iam attach-user-policy \
  --user-name gameflex-ci-development \
  --policy-arn arn:aws:iam::aws:policy/AdministratorAccess

# For Staging User
aws iam attach-user-policy \
  --user-name gameflex-ci-staging \
  --policy-arn arn:aws:iam::aws:policy/AdministratorAccess

# For Production User
aws iam attach-user-policy \
  --user-name gameflex-ci-production \
  --policy-arn arn:aws:iam::aws:policy/AdministratorAccess
```

#### **Option 1: Use the Interactive Setup Script (Recommended)**

We've created an interactive script that handles the entire bootstrap process with proper feedback:

```bash
# Navigate to the backend directory
cd backend

# Run the interactive bootstrap script
./setup/bootstrap-cdk.sh
```

The script will:
- ✅ Ask you to select the environment (dev/staging/production)
- ✅ Load credentials from the `.env` file automatically
- ✅ Validate credentials and basic permissions
- ✅ Run CDK bootstrap with verbose output
- ✅ Verify the stack was created successfully
- ✅ Provide clear success/error messages

#### **Option 2: Manual Bootstrap Commands**

If you prefer to run the commands manually:

#### Development Environment (us-west-2)
```bash
# Use development AWS credentials
export AWS_ACCESS_KEY_ID=<dev-access-key>
export AWS_SECRET_ACCESS_KEY=<dev-secret-key>
export AWS_DEFAULT_REGION=us-west-2

# Bootstrap CDK with verbose output
npx cdk bootstrap aws://************/us-west-2 \
  --toolkit-stack-name CDKToolkit-GameFlex-Development \
  --description "CDK Toolkit for GameFlex Development Environment" \
  --qualifier gfdev \
  --cloudformation-execution-policies arn:aws:iam::************:policy/GameFlexDevelopmentCIPolicy \
  --verbose

# Verify bootstrap was successful
echo "Verifying CDK bootstrap..."
aws cloudformation describe-stacks \
  --stack-name CDKToolkit-GameFlex-Development \
  --region us-west-2 \
  --query 'Stacks[0].StackStatus' \
  --output text

# Should output: CREATE_COMPLETE or UPDATE_COMPLETE
```

#### Staging Environment (us-west-2)
```bash
# Use staging AWS credentials
export AWS_ACCESS_KEY_ID=<staging-access-key>
export AWS_SECRET_ACCESS_KEY=<staging-secret-key>
export AWS_DEFAULT_REGION=us-west-2

# Bootstrap CDK with verbose output
npx cdk bootstrap aws://************/us-west-2 \
  --toolkit-stack-name CDKToolkit-GameFlex-Staging \
  --description "CDK Toolkit for GameFlex Staging Environment" \
  --qualifier gfstaging \
  --cloudformation-execution-policies arn:aws:iam::************:policy/GameFlexStagingCIPolicy \
  --verbose

# Verify bootstrap was successful
echo "Verifying CDK bootstrap..."
aws cloudformation describe-stacks \
  --stack-name CDKToolkit-GameFlex-Staging \
  --region us-west-2 \
  --query 'Stacks[0].StackStatus' \
  --output text

# Should output: CREATE_COMPLETE or UPDATE_COMPLETE
```

#### Production Environment (us-east-1)
```bash
# Use production AWS credentials
export AWS_ACCESS_KEY_ID=<prod-access-key>
export AWS_SECRET_ACCESS_KEY=<prod-secret-key>
export AWS_DEFAULT_REGION=us-east-1

# Bootstrap CDK with verbose output
npx cdk bootstrap aws://************/us-east-1 \
  --toolkit-stack-name CDKToolkit-GameFlex-Production \
  --description "CDK Toolkit for GameFlex Production Environment" \
  --qualifier gfprod \
  --cloudformation-execution-policies arn:aws:iam::************:policy/GameFlexProductionCIPolicy \
  --verbose

# Verify bootstrap was successful
echo "Verifying CDK bootstrap..."
aws cloudformation describe-stacks \
  --stack-name CDKToolkit-GameFlex-Production \
  --region us-east-1 \
  --query 'Stacks[0].StackStatus' \
  --output text

# Should output: CREATE_COMPLETE or UPDATE_COMPLETE
```

#### **🔒 Remove Temporary Administrator Access (CRITICAL)**

**After successfully bootstrapping all environments, immediately remove the temporary AdministratorAccess:**

```bash
# Remove from Development User
aws iam detach-user-policy \
  --user-name gameflex-ci-development \
  --policy-arn arn:aws:iam::aws:policy/AdministratorAccess

# Remove from Staging User
aws iam detach-user-policy \
  --user-name gameflex-ci-staging \
  --policy-arn arn:aws:iam::aws:policy/AdministratorAccess

# Remove from Production User
aws iam detach-user-policy \
  --user-name gameflex-ci-production \
  --policy-arn arn:aws:iam::aws:policy/AdministratorAccess
```

**Important Notes:**
- Account ID is set to `************` for all environments
- Each environment uses different qualifiers to avoid conflicts
- Production uses `us-east-1` for better global performance
- The `--verbose` flag provides detailed output for troubleshooting
- **The environment-specific policies (GameFlexDevelopmentCIPolicy, etc.) remain attached and provide the necessary permissions for CI/CD operations**

### **Troubleshooting CDK Bootstrap**

If the bootstrap command fails or provides no feedback:

1. **Check AWS Credentials**:
   ```bash
   aws sts get-caller-identity
   # Should show your account ID and user/role
   ```

2. **Check Region Configuration**:
   ```bash
   aws configure get region
   # Should match the region you're bootstrapping
   ```

3. **Manual Bootstrap Verification**:
   ```bash
   # List all CloudFormation stacks to see if bootstrap succeeded
   aws cloudformation list-stacks \
     --region us-west-2 \
     --query 'StackSummaries[?contains(StackName, `CDKToolkit`)].{Name:StackName,Status:StackStatus}' \
     --output table
   ```

4. **Common Error Solutions**:
   - **No output**: Add `--verbose` flag for detailed logging
   - **Permission denied**: Ensure your IAM user has CloudFormation permissions
   - **Stack already exists**: Use `--force` flag to update existing stack
   - **Region mismatch**: Verify `AWS_DEFAULT_REGION` matches bootstrap region

5. **Force Re-bootstrap** (if needed):
   ```bash
   npx cdk bootstrap aws://************/us-west-2 \
     --toolkit-stack-name CDKToolkit-GameFlex-Development \
     --description "CDK Toolkit for GameFlex Development Environment" \
     --qualifier gfdev \
     --cloudformation-execution-policies arn:aws:iam::************:policy/GameFlexDevelopmentCIPolicy \
     --verbose \
     --force
   ```

### **Bootstrap Script Features**

The `bootstrap-cdk.sh` script provides:

- **🎯 Interactive Environment Selection**: Choose dev, staging, or production
- **🔐 Secure Credential Input**: Hidden password input for secret keys
- **✅ Credential Validation**: Verifies AWS credentials before proceeding
- **📊 Detailed Progress**: Verbose output with colored status messages
- **🔍 Automatic Verification**: Confirms stack creation after bootstrap
- **❌ Error Handling**: Clear error messages and troubleshooting tips
- **⚠️ Safety Checks**: Confirms account ID and asks for confirmation

**Example Script Output:**
```
==============================================
  GameFlex CDK Bootstrap Setup
==============================================

Select environment to bootstrap:
1) Development (us-west-2)
2) Staging (us-west-2)
3) Production (us-east-1)

Enter choice (1-3): 1
ℹ️  Selected: development environment in us-west-2

Enter AWS credentials for development environment:
AWS Access Key ID: AKIA...
AWS Secret Access Key: [hidden]

ℹ️  AWS credentials set for region: us-west-2
ℹ️  Validating AWS credentials...
✅ AWS credentials validated
ℹ️  Account: ************
ℹ️  User/Role: arn:aws:iam::************:user/gameflex-dev

⚠️  About to bootstrap CDK with the following settings:
  Environment: development
  Region: us-west-2
  Stack Name: CDKToolkit-GameFlex-Development
  Account ID: ************

Continue? (y/N): y

ℹ️  Starting CDK bootstrap for development environment...
ℹ️  Running CDK bootstrap command...
✅ CDK bootstrap completed successfully!
✅ Stack verification successful: CREATE_COMPLETE
✅ CDK bootstrap setup completed for development environment!
```
- Bootstrap creates S3 buckets and IAM roles needed for CDK deployments

### Step 2: Configure GitLab Variables

1. Go to your GitLab project
2. Navigate to **Settings** > **CI/CD**
3. Expand the **Variables** section
4. Add the following variables:

#### Required Variables
```bash
# Development Environment
AWS_ACCESS_KEY_ID_DEV=AKIA...
AWS_SECRET_ACCESS_KEY_DEV=... (mark as Protected and Masked)

# Staging Environment
AWS_ACCESS_KEY_ID_STAGING=AKIA...
AWS_SECRET_ACCESS_KEY_STAGING=... (mark as Protected and Masked)

# Production Environment
AWS_ACCESS_KEY_ID_PROD=AKIA...
AWS_SECRET_ACCESS_KEY_PROD=... (mark as Protected and Masked)
```

### Step 3: Configure Branch Protection

1. Go to **Settings** > **Repository**
2. Expand **Protected branches**
3. Protect the following branches:
   - `main` (or `master`) - Allow merge, Require approval
   - `develop` - Allow push and merge

### Step 4: Set Up Merge Request Approvals

1. Go to **Settings** > **Merge requests**
2. Set **Merge request approvals**:
   - Require 1 approval for `main` branch
   - Require security team approval for production deployments

### Step 5: Configure Security Scanning

The pipeline automatically includes GitLab's security templates. To customize:

1. Review `setup/security-policy.md` for advanced configuration
2. Modify `.gitleaks.toml` for custom secrets detection rules
3. Add custom SAST rules in `.gitlab/security-config.yml`

### Step 6: Test the Pipeline

1. Create a feature branch:
   ```bash
   git checkout -b feature/test-pipeline
   ```

2. Make a small change to the backend:
   ```bash
   echo "// Test change" >> src/health/index.ts
   git add .
   git commit -m "test: trigger pipeline"
   git push origin feature/test-pipeline
   ```

3. Create a merge request and observe the pipeline execution

## 6. Deploy Your Application

After bootstrapping is complete, you can deploy your CDK stacks using the provided deployment scripts:

### Development Deployment
```bash
# Deploy to development
./deploy.sh development

# Show diff before deploying
./deploy.sh development --diff

# Deploy without confirmation
./deploy.sh development -y
```

### Staging Deployment
```bash
# Deploy to staging
./deploy-staging.sh

# Show diff before deploying
./deploy-staging.sh --diff

# Deploy without confirmation
./deploy-staging.sh -y
```

### Production Deployment
```bash
# Deploy to production (requires double confirmation)
./deploy-production.sh

# Show diff before deploying
./deploy-production.sh --diff
```

### Manual CDK Commands (if needed)
```bash
# Deploy to development
cdk deploy gameflex-development --context environment=development --context projectName=gameflex --qualifier gfdev

# Deploy to staging
cdk deploy gameflex-staging --context environment=staging --context projectName=gameflex --qualifier gfstaging

# Deploy to production
cdk deploy gameflex-production --context environment=production --context projectName=gameflex --qualifier gfprod
```

### Important Notes
- All deployment scripts now use the correct CDK toolkit stack names and qualifiers
- The scripts automatically check for the proper bootstrap configuration
- Production deployments require explicit confirmation for safety
- Use `--diff` to preview changes before deploying
