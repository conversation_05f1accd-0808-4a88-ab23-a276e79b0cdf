#!/bin/bash

# GameFlex Backend - Staging Data Seeding Script
# This script seeds the staging environment with test data

set -e

echo "🌱 GameFlex Backend - Staging Data Seeding"
echo "=========================================="

# Set environment to staging
export ENVIRONMENT=staging

# Check if .env.staging exists
if [ ! -f ".env.staging" ]; then
    echo "❌ .env.staging file not found"
    echo "💡 Create it with staging configuration including secrets"
    exit 1
fi

echo "📋 Using staging environment configuration"

# Test environment loading
echo "📋 Testing environment configuration loading..."
if ! ./test-env-loading.sh staging; then
    echo "❌ Environment loading test failed"
    echo "💡 Check your .env.staging file and samconfig.toml configuration"
    exit 1
fi

echo "✅ Environment configuration validated"

# Check AWS credentials
if ! aws sts get-caller-identity > /dev/null 2>&1; then
    echo "❌ AWS credentials not configured or invalid"
    echo "💡 Please run 'aws configure' or set AWS environment variables"
    exit 1
fi

echo "✅ AWS credentials validated"

# Run the seed script for staging
echo "🚀 Starting staging data seeding..."
./scripts/seed-data.sh -e staging "$@"

if [ $? -eq 0 ]; then
    echo ""
    echo "🎉 Staging data seeding completed successfully!"
    echo ""
    echo "📋 What was seeded:"
    echo "  • AWS Secrets Manager with R2 and app configuration"
    echo "  • DynamoDB tables with test data"
    echo "  • CloudFlare R2 with test images"
    echo "  • Test users and channels"
    echo ""
    echo "🔗 Staging endpoints:"
    echo "  • API: ${API_BASE_URL:-https://staging.api.gameflex.io}"
    echo "  • Media: ${R2_PUBLIC_URL:-https://staging.media.gameflex.io}"
    echo ""
    echo "🧪 Test the staging environment:"
    echo "  ./test-staging.sh"
else
    echo "❌ Staging data seeding failed"
    exit 1
fi
