# GitLab Security Configuration
# This file contains additional security scanning configuration

# SAST (Static Application Security Testing) Configuration
sast:
  # Analyzer configuration
  analyzers:
    # JavaScript/TypeScript analyzers for backend
    eslint:
      enabled: true
      image: registry.gitlab.com/gitlab-org/security-products/analyzers/eslint:latest
      variables:
        ESLINT_CONFIG_FILE: "backend/.eslintrc.js"
    
    nodejs-scan:
      enabled: true
      image: registry.gitlab.com/gitlab-org/security-products/analyzers/nodejs-scan:latest
    
    semgrep:
      enabled: true
      image: registry.gitlab.com/gitlab-org/security-products/analyzers/semgrep:latest
      variables:
        SEMGREP_CONFIG: "auto"
    
    # Dart/Flutter analyzer
    dart-analyzer:
      enabled: true
      image: registry.gitlab.com/gitlab-org/security-products/analyzers/semgrep:latest
      variables:
        SEMGREP_CONFIG: "p/dart"

  # Global SAST configuration
  variables:
    SAST_ANALYZER_IMAGE_TAG: "latest"
    SAST_EXCLUDED_ANALYZERS: ""
    SAST_EXCLUDED_PATHS: "node_modules,build,.pub-cache,.dart_tool,coverage,dist,cdk.out"
    SAST_BANDIT_EXCLUDED_PATHS: ""
    SAST_BRAKEMAN_LEVEL: "1"
    SAST_GOSEC_LEVEL: "0"
    SAST_FLAWFINDER_LEVEL: "1"
    SAST_GITLEAKS_ENTROPY_LEVEL: "6.0"
    SAST_DISABLE_DIND: "false"

# Secrets Detection Configuration
secrets-detection:
  variables:
    SECRET_DETECTION_EXCLUDED_PATHS: "node_modules,build,.pub-cache,.dart_tool,coverage,dist,cdk.out"
    SECRET_DETECTION_HISTORIC_SCAN: "false"
    SECRET_DETECTION_LOG_OPTIONS: ""
    SECRET_DETECTION_IMAGE_SUFFIX: ""

# Dependency Scanning Configuration
dependency-scanning:
  variables:
    DS_EXCLUDED_ANALYZERS: ""
    DS_EXCLUDED_PATHS: "node_modules,build,.pub-cache,.dart_tool,coverage,dist,cdk.out"
    DS_INCLUDE_DEV_DEPENDENCIES: "true"
    DS_DEFAULT_ANALYZERS: "gemnasium"
    DS_DISABLE_DIND: "false"

# License Scanning Configuration
license-scanning:
  variables:
    LIC_EXCLUDED_PATHS: "node_modules,build,.pub-cache,.dart_tool,coverage,dist,cdk.out"
    LICENSE_FINDER_CLI_OPTS: ""

# Container Scanning Configuration (if using Docker)
container-scanning:
  variables:
    CS_ANALYZER_IMAGE: "registry.gitlab.com/gitlab-org/security-products/analyzers/container-scanning:latest"
    CS_EXCLUDED_PATHS: ""
    CS_DISABLE_DIND: "false"

# Security Dashboard Configuration
security-dashboard:
  # Vulnerability thresholds
  thresholds:
    critical: 0  # Block deployment if any critical vulnerabilities
    high: 5      # Block deployment if more than 5 high vulnerabilities
    medium: 20   # Warn if more than 20 medium vulnerabilities
    low: 50      # Warn if more than 50 low vulnerabilities

  # Auto-remediation settings
  auto-remediation:
    enabled: true
    create-merge-requests: true
    auto-merge: false

# Custom Security Rules
custom-rules:
  # Backend-specific rules
  backend:
    # Node.js security patterns
    patterns:
      - pattern: "eval\\("
        message: "Use of eval() is dangerous and should be avoided"
        severity: "high"
      
      - pattern: "process\\.env\\.[A-Z_]+\\s*=\\s*['\"][^'\"]+['\"]"
        message: "Hardcoded environment variable detected"
        severity: "medium"
      
      - pattern: "password\\s*=\\s*['\"][^'\"]+['\"]"
        message: "Hardcoded password detected"
        severity: "critical"
      
      - pattern: "api[_-]?key\\s*=\\s*['\"][^'\"]+['\"]"
        message: "Hardcoded API key detected"
        severity: "critical"

  # Frontend-specific rules
  frontend:
    # Dart/Flutter security patterns
    patterns:
      - pattern: "http://"
        message: "HTTP URLs should be HTTPS in production"
        severity: "medium"
        exclude_paths: ["test/**", "**/*test.dart"]
      
      - pattern: "allowsArbitraryLoads.*true"
        message: "Arbitrary loads should be disabled in production"
        severity: "high"
      
      - pattern: "debugPrint\\("
        message: "Debug prints should be removed in production"
        severity: "low"
        exclude_paths: ["test/**", "**/*test.dart"]

# Compliance Configuration
compliance:
  # OWASP Top 10 checks
  owasp-top-10:
    enabled: true
    categories:
      - "A01:2021-Broken Access Control"
      - "A02:2021-Cryptographic Failures"
      - "A03:2021-Injection"
      - "A04:2021-Insecure Design"
      - "A05:2021-Security Misconfiguration"
      - "A06:2021-Vulnerable and Outdated Components"
      - "A07:2021-Identification and Authentication Failures"
      - "A08:2021-Software and Data Integrity Failures"
      - "A09:2021-Security Logging and Monitoring Failures"
      - "A10:2021-Server-Side Request Forgery"

  # CWE (Common Weakness Enumeration) checks
  cwe:
    enabled: true
    high-priority:
      - "CWE-79"   # Cross-site Scripting
      - "CWE-89"   # SQL Injection
      - "CWE-22"   # Path Traversal
      - "CWE-352"  # Cross-Site Request Forgery
      - "CWE-434"  # Unrestricted Upload of File
      - "CWE-502"  # Deserialization of Untrusted Data

# Notification Configuration
notifications:
  # Security findings notifications
  security-findings:
    enabled: true
    channels:
      - type: "email"
        recipients: ["<EMAIL>", "<EMAIL>"]
        severity-threshold: "high"
      
      - type: "slack"
        webhook: "$SLACK_SECURITY_WEBHOOK"
        severity-threshold: "critical"

  # Deployment notifications
  deployment:
    enabled: true
    channels:
      - type: "email"
        recipients: ["<EMAIL>"]
        events: ["deployment-success", "deployment-failure"]

# Reporting Configuration
reporting:
  # Security report formats
  formats:
    - "json"
    - "html"
    - "junit"
    - "sarif"

  # Report retention
  retention:
    days: 90
    
  # Report storage
  storage:
    type: "gitlab-artifacts"
    path: "security-reports/"

# Integration Configuration
integrations:
  # JIRA integration for vulnerability tracking
  jira:
    enabled: false
    url: "$JIRA_URL"
    project: "$JIRA_PROJECT"
    issue-type: "Security Vulnerability"
    
  # Defect Dojo integration
  defect-dojo:
    enabled: false
    url: "$DEFECT_DOJO_URL"
    api-key: "$DEFECT_DOJO_API_KEY"

# Performance Configuration
performance:
  # Scanner timeouts
  timeouts:
    sast: "30m"
    secrets-detection: "15m"
    dependency-scanning: "20m"
    license-scanning: "10m"
    
  # Resource limits
  resources:
    memory: "2Gi"
    cpu: "1000m"

# Advanced Configuration
advanced:
  # Custom analyzer images
  custom-analyzers:
    enabled: false
    registry: "$CUSTOM_ANALYZER_REGISTRY"
    
  # Offline scanning
  offline-mode:
    enabled: false
    
  # Debug mode
  debug:
    enabled: false
    log-level: "info"
