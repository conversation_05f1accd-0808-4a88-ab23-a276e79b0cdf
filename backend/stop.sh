#!/bin/bash

# GameFlex CDK Backend Stop Script
# This script stops the AWS CDK watch process

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Function to print colored output
print_status() {
    echo -e "${GREEN}[INFO]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARN]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

print_header() {
    echo -e "${BLUE}[GAMEFLEX]${NC} $1"
}

# Stop CDK watch process
stop_cdk_watch() {
    print_status "Stopping CDK watch..."

    if [ -f .cdk_watch_pid ]; then
        local pid=$(cat .cdk_watch_pid)
        if kill -0 $pid 2>/dev/null; then
            kill $pid
            print_status "CDK watch stopped"
        else
            print_warning "CDK watch process not found"
        fi
        rm .cdk_watch_pid
    else
        print_warning "CDK watch PID file not found"
    fi

    # Also try to kill any remaining cdk watch processes
    pkill -f "cdk watch" 2>/dev/null || true
}

# Stop any remaining CDK processes
stop_cdk_processes() {
    print_status "Stopping any remaining CDK processes..."

    # Kill any remaining cdk processes
    pkill -f "cdk watch" 2>/dev/null || true

    print_status "CDK processes stopped"
}

# Clean up temporary files
cleanup_files() {
    print_status "Cleaning up temporary files..."

    # Remove any temporary files
    rm -f .cdk_watch_pid

    print_status "Cleanup completed"
}

# Main execution
main() {
    print_header "Stopping GameFlex CDK Backend"
    echo

    stop_cdk_watch
    stop_cdk_processes
    cleanup_files

    echo
    print_status "GameFlex CDK Backend stopped successfully!"
}

# Run main function
main "$@"
