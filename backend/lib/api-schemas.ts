/**
 * OpenAPI 3.0 Schema Definitions for GameFlex API
 * Used for AWS API Gateway Request Validation and Documentation
 */

import * as apigateway from 'aws-cdk-lib/aws-apigateway';

// Common schema components
export const commonSchemas = {
  // Error response schema
  ErrorResponse: {
    type: 'object',
    required: ['error'],
    properties: {
      error: {
        type: 'string',
        description: 'Error message describing what went wrong'
      },
      details: {
        type: 'string',
        description: 'Additional error details for debugging'
      }
    },
    additionalProperties: true
  },

  // User object schema
  User: {
    type: 'object',
    required: ['id', 'email', 'firstName', 'lastName'],
    properties: {
      id: {
        type: 'string',
        description: 'Unique user identifier (UUID)',
        pattern: '^[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}$'
      },
      email: {
        type: 'string',
        format: 'email',
        description: 'User email address'
      },
      username: {
        type: 'string',
        minLength: 3,
        maxLength: 30,
        pattern: '^[a-zA-Z0-9_-]+$',
        description: 'Unique username'
      },
      firstName: {
        type: 'string',
        maxLength: 50,
        description: 'User first name'
      },
      lastName: {
        type: 'string',
        maxLength: 50,
        description: 'User last name'
      }
    },
    additionalProperties: true
  },

  // Authentication tokens schema
  AuthTokens: {
    type: 'object',
    required: ['accessToken', 'refreshToken', 'idToken'],
    properties: {
      accessToken: {
        type: 'string',
        description: 'JWT access token for API authentication'
      },
      refreshToken: {
        type: 'string',
        description: 'Refresh token for obtaining new access tokens'
      },
      idToken: {
        type: 'string',
        description: 'JWT ID token containing user claims'
      }
    },
    additionalProperties: true
  },

  // Partial auth tokens (for refresh response)
  RefreshTokens: {
    type: 'object',
    required: ['accessToken', 'idToken'],
    properties: {
      accessToken: {
        type: 'string',
        description: 'New JWT access token'
      },
      idToken: {
        type: 'string',
        description: 'New JWT ID token'
      }
    },
    additionalProperties: true
  }
};

// Auth endpoint schemas
export const authSchemas = {
  // POST /auth/signup
  signupRequest: {
    type: 'object',
    required: ['email', 'password'],
    properties: {
      email: {
        type: 'string',
        format: 'email',
        description: 'User email address',
        example: '<EMAIL>'
      },
      password: {
        type: 'string',
        minLength: 8,
        maxLength: 128,
        pattern: '^(?=.*[a-z])(?=.*[A-Z])(?=.*\\d)(?=.*[@$!%*?&])[A-Za-z\\d@$!%*?&]',
        description: 'Password must contain at least 8 characters with uppercase, lowercase, number, and special character',
        example: 'SecurePass123!'
      },
      firstName: {
        type: 'string',
        maxLength: 50,
        description: 'User first name',
        example: 'John'
      },
      lastName: {
        type: 'string',
        maxLength: 50,
        description: 'User last name',
        example: 'Doe'
      }
    },
    additionalProperties: true
  },

  signupResponse: {
    type: 'object',
    required: ['message', 'user'],
    properties: {
      message: {
        type: 'string',
        description: 'Success message',
        example: 'User created successfully'
      },
      user: {
        $ref: '#/components/schemas/User'
      }
    },
    additionalProperties: true
  },

  // POST /auth/signin
  signinRequest: {
    type: 'object',
    required: ['email', 'password'],
    properties: {
      email: {
        type: 'string',
        format: 'email',
        description: 'User email address',
        example: '<EMAIL>'
      },
      password: {
        type: 'string',
        minLength: 1,
        description: 'User password',
        example: 'SecurePass123!'
      }
    },
    additionalProperties: true
  },

  signinResponse: {
    type: 'object',
    required: ['message', 'tokens', 'user'],
    properties: {
      message: {
        type: 'string',
        description: 'Success message',
        example: 'Sign in successful'
      },
      tokens: {
        $ref: '#/components/schemas/AuthTokens'
      },
      user: {
        $ref: '#/components/schemas/User'
      }
    },
    additionalProperties: true
  },

  // POST /auth/set-username
  setUsernameRequest: {
    type: 'object',
    required: ['username'],
    properties: {
      username: {
        type: 'string',
        minLength: 4,
        maxLength: 32,
        pattern: '^[a-zA-Z0-9_-]+$',
        description: 'Unique username (4-32 characters, alphanumeric, underscore, and hyphen only, case-insensitive)',
        example: 'john_doe'
      }
    },
    additionalProperties: true
  },

  setUsernameResponse: {
    type: 'object',
    required: ['message', 'user'],
    properties: {
      message: {
        type: 'string',
        description: 'Success message',
        example: 'Username set successfully'
      },
      user: {
        $ref: '#/components/schemas/User'
      }
    },
    additionalProperties: true
  },

  // Check username availability response
  checkUsernameAvailabilityResponse: {
    type: 'object',
    required: ['available', 'valid'],
    properties: {
      available: {
        type: 'boolean',
        description: 'Whether the username is available for use',
        example: true
      },
      valid: {
        type: 'boolean',
        description: 'Whether the username passes all validation checks',
        example: true
      },
      username: {
        type: 'string',
        description: 'The normalized username (lowercase)',
        example: 'testuser123'
      },
      error: {
        type: 'string',
        description: 'Error message if validation fails',
        example: 'Username contains inappropriate content'
      },
      details: {
        type: 'string',
        description: 'Additional details about the error',
        example: 'Please choose a different username'
      }
    },
    additionalProperties: true
  },

  // POST /auth/refresh
  refreshRequest: {
    type: 'object',
    required: ['refreshToken'],
    properties: {
      refreshToken: {
        type: 'string',
        description: 'Valid refresh token',
        example: 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...'
      }
    },
    additionalProperties: true
  },

  refreshResponse: {
    type: 'object',
    required: ['message', 'tokens'],
    properties: {
      message: {
        type: 'string',
        description: 'Success message',
        example: 'Token refreshed successfully'
      },
      tokens: {
        $ref: '#/components/schemas/RefreshTokens'
      }
    },
    additionalProperties: true
  },

  // GET /auth/validate
  validateResponse: {
    type: 'object',
    required: ['message', 'valid'],
    properties: {
      message: {
        type: 'string',
        description: 'Validation result message',
        example: 'Token is valid'
      },
      valid: {
        type: 'boolean',
        description: 'Whether the token is valid',
        example: true
      },
      user: {
        $ref: '#/components/schemas/User',
        description: 'User information (only present when valid=true)'
      }
    },
    additionalProperties: true
  },

  // POST /auth/apple
  appleSigninRequest: {
    type: 'object',
    required: ['identityToken'],
    properties: {
      identityToken: {
        type: 'string',
        description: 'Apple identity token from Sign in with Apple'
      },
      authorizationCode: {
        type: 'string',
        description: 'Apple authorization code (optional)'
      },
      user: {
        type: 'object',
        description: 'User information from Apple (only provided on first sign in)',
        properties: {
          name: {
            type: 'object',
            properties: {
              firstName: {
                type: 'string',
                description: 'User first name'
              },
              lastName: {
                type: 'string',
                description: 'User last name'
              }
            }
          },
          email: {
            type: 'string',
            format: 'email',
            description: 'User email address'
          }
        }
      }
    },
    additionalProperties: true
  }
};

// Request parameter schemas
export const parameterSchemas = {
  authorizationHeader: {
    name: 'Authorization',
    in: 'header',
    required: true,
    schema: {
      type: 'string',
      pattern: '^Bearer [A-Za-z0-9-_=]+\\.[A-Za-z0-9-_=]+\\.[A-Za-z0-9-_.+/=]*$'
    },
    description: 'Bearer token for authentication',
    example: 'Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...'
  }
};

// AWS API Gateway Request Validator Models
export const requestValidatorModels = {
  // Signup request model
  SignupRequest: {
    contentType: 'application/json',
    modelName: 'SignupRequest',
    description: 'Request body for user signup',
    schema: {
      type: apigateway.JsonSchemaType.OBJECT,
      title: 'Signup Request',
      description: 'User registration request containing email and password',
      required: ['email', 'password'],
      properties: {
        email: {
          type: apigateway.JsonSchemaType.STRING,
          format: 'email',
          description: 'User email address (must be valid email format)'
        },
        password: {
          type: apigateway.JsonSchemaType.STRING,
          minLength: 8,
          maxLength: 128,
          description: 'Password must contain at least 8 characters with uppercase, lowercase, number and special character'
        },
        firstName: {
          type: apigateway.JsonSchemaType.STRING,
          maxLength: 50,
          description: 'User first name (optional)'
        },
        lastName: {
          type: apigateway.JsonSchemaType.STRING,
          maxLength: 50,
          description: 'User last name (optional)'
        }
      },
      additionalProperties: true
    }
  },

  // Signin request model
  SigninRequest: {
    contentType: 'application/json',
    modelName: 'SigninRequest',
    description: 'Request body for user signin',
    schema: {
      type: apigateway.JsonSchemaType.OBJECT,
      title: 'Signin Request',
      description: 'User authentication request containing email and password',
      required: ['email', 'password'],
      properties: {
        email: {
          type: apigateway.JsonSchemaType.STRING,
          format: 'email',
          description: 'User email address'
        },
        password: {
          type: apigateway.JsonSchemaType.STRING,
          minLength: 1,
          description: 'User password'
        }
      },
      additionalProperties: true
    }
  },

  // Apple Sign In request model
  AppleSigninRequest: {
    contentType: 'application/json',
    modelName: 'AppleSigninRequest',
    description: 'Request body for Apple Sign In',
    schema: {
      type: apigateway.JsonSchemaType.OBJECT,
      title: 'Apple Sign In Request',
      description: 'Apple Sign In request containing identity token and optional user data',
      required: ['identityToken'],
      properties: {
        identityToken: {
          type: apigateway.JsonSchemaType.STRING,
          description: 'Apple identity token from Sign in with Apple'
        },
        authorizationCode: {
          type: apigateway.JsonSchemaType.STRING,
          description: 'Apple authorization code (optional)'
        },
        user: {
          type: apigateway.JsonSchemaType.OBJECT,
          description: 'User information from Apple (only provided on first sign in)',
          properties: {
            name: {
              type: apigateway.JsonSchemaType.OBJECT,
              properties: {
                firstName: {
                  type: apigateway.JsonSchemaType.STRING,
                  description: 'User first name'
                },
                lastName: {
                  type: apigateway.JsonSchemaType.STRING,
                  description: 'User last name'
                }
              }
            },
            email: {
              type: apigateway.JsonSchemaType.STRING,
              format: 'email',
              description: 'User email address'
            }
          }
        }
      },
      additionalProperties: true
    }
  },

  // Set username request model
  SetUsernameRequest: {
    contentType: 'application/json',
    modelName: 'SetUsernameRequest',
    description: 'Request body for setting username',
    schema: {
      type: apigateway.JsonSchemaType.OBJECT,
      title: 'Set Username Request',
      description: 'Request to set username for authenticated user',
      required: ['username'],
      properties: {
        username: {
          type: apigateway.JsonSchemaType.STRING,
          minLength: 4,
          maxLength: 32,
          pattern: '^[a-zA-Z0-9_-]+$',
          description: 'Unique username (4-32 characters, alphanumeric, underscore, and hyphen only, case-insensitive)'
        }
      },
      additionalProperties: true
    }
  },

  // Refresh token request model
  RefreshRequest: {
    contentType: 'application/json',
    modelName: 'RefreshRequest',
    description: 'Request body for token refresh',
    schema: {
      type: apigateway.JsonSchemaType.OBJECT,
      title: 'Refresh Token Request',
      description: 'Request to refresh access token using refresh token',
      required: ['refreshToken'],
      properties: {
        refreshToken: {
          type: apigateway.JsonSchemaType.STRING,
          description: 'Valid refresh token obtained from signin or previous refresh'
        }
      },
      additionalProperties: true
    }
  },

  // Success response models
  SignupResponse: {
    contentType: 'application/json',
    modelName: 'SignupResponse',
    description: 'Successful signup response',
    schema: {
      type: apigateway.JsonSchemaType.OBJECT,
      title: 'Signup Success Response',
      description: 'Response returned when user signup is successful',
      required: ['message'],
      properties: {
        message: {
          type: apigateway.JsonSchemaType.STRING,
          description: 'Success message'
        },
        userId: {
          type: apigateway.JsonSchemaType.STRING,
          description: 'Unique user identifier'
        }
      },
      additionalProperties: true
    }
  },

  SigninResponse: {
    contentType: 'application/json',
    modelName: 'SigninResponse',
    description: 'Successful signin response',
    schema: {
      type: apigateway.JsonSchemaType.OBJECT,
      title: 'Signin Success Response',
      description: 'Response returned when user signin is successful',
      required: ['accessToken', 'refreshToken', 'idToken', 'user'],
      properties: {
        accessToken: {
          type: apigateway.JsonSchemaType.STRING,
          description: 'JWT access token for API authentication'
        },
        refreshToken: {
          type: apigateway.JsonSchemaType.STRING,
          description: 'JWT refresh token for obtaining new access tokens'
        },
        idToken: {
          type: apigateway.JsonSchemaType.STRING,
          description: 'JWT ID token containing user identity information'
        },
        user: {
          type: apigateway.JsonSchemaType.OBJECT,
          description: 'User profile information',
          properties: {
            id: {
              type: apigateway.JsonSchemaType.STRING,
              description: 'User ID'
            },
            email: {
              type: apigateway.JsonSchemaType.STRING,
              description: 'User email'
            },
            username: {
              type: apigateway.JsonSchemaType.STRING,
              description: 'Username'
            }
          }
        }
      },
      additionalProperties: true
    }
  },

  RefreshResponse: {
    contentType: 'application/json',
    modelName: 'RefreshResponse',
    description: 'Successful token refresh response',
    schema: {
      type: apigateway.JsonSchemaType.OBJECT,
      title: 'Token Refresh Success Response',
      description: 'Response returned when token refresh is successful',
      required: ['accessToken', 'idToken'],
      properties: {
        accessToken: {
          type: apigateway.JsonSchemaType.STRING,
          description: 'New JWT access token'
        },
        idToken: {
          type: apigateway.JsonSchemaType.STRING,
          description: 'New JWT ID token'
        }
      },
      additionalProperties: true
    }
  },

  ValidateResponse: {
    contentType: 'application/json',
    modelName: 'ValidateResponse',
    description: 'Successful token validation response',
    schema: {
      type: apigateway.JsonSchemaType.OBJECT,
      title: 'Token Validation Success Response',
      description: 'Response returned when token validation is successful',
      required: ['valid', 'user'],
      properties: {
        valid: {
          type: apigateway.JsonSchemaType.BOOLEAN,
          description: 'Whether the token is valid'
        },
        user: {
          type: apigateway.JsonSchemaType.OBJECT,
          description: 'User information from the token',
          properties: {
            id: {
              type: apigateway.JsonSchemaType.STRING,
              description: 'User ID'
            },
            email: {
              type: apigateway.JsonSchemaType.STRING,
              description: 'User email'
            },
            username: {
              type: apigateway.JsonSchemaType.STRING,
              description: 'Username'
            }
          }
        }
      },
      additionalProperties: true
    }
  },

  SetUsernameResponse: {
    contentType: 'application/json',
    modelName: 'SetUsernameResponse',
    description: 'Successful set username response',
    schema: {
      type: apigateway.JsonSchemaType.OBJECT,
      title: 'Set Username Success Response',
      description: 'Response returned when username is set successfully',
      required: ['message', 'user'],
      properties: {
        message: {
          type: apigateway.JsonSchemaType.STRING,
          description: 'Success message'
        },
        user: {
          type: apigateway.JsonSchemaType.OBJECT,
          description: 'Updated user information',
          properties: {
            id: {
              type: apigateway.JsonSchemaType.STRING,
              description: 'User ID'
            },
            email: {
              type: apigateway.JsonSchemaType.STRING,
              description: 'User email'
            },
            username: {
              type: apigateway.JsonSchemaType.STRING,
              description: 'User username'
            },
            firstName: {
              type: apigateway.JsonSchemaType.STRING,
              description: 'User first name'
            },
            lastName: {
              type: apigateway.JsonSchemaType.STRING,
              description: 'User last name'
            }
          },
          additionalProperties: true
        }
      },
      additionalProperties: true
    }
  },

  // Check username availability response model
  CheckUsernameAvailabilityResponse: {
    contentType: 'application/json',
    modelName: 'CheckUsernameAvailabilityResponse',
    description: 'Response for checking username availability',
    schema: {
      type: apigateway.JsonSchemaType.OBJECT,
      title: 'Check Username Availability Response',
      description: 'Response for username availability check',
      required: ['available', 'valid'],
      properties: {
        available: {
          type: apigateway.JsonSchemaType.BOOLEAN,
          description: 'Whether the username is available for use'
        },
        valid: {
          type: apigateway.JsonSchemaType.BOOLEAN,
          description: 'Whether the username passes all validation checks'
        },
        username: {
          type: apigateway.JsonSchemaType.STRING,
          description: 'The normalized username (lowercase)'
        },
        error: {
          type: apigateway.JsonSchemaType.STRING,
          description: 'Error message if validation fails'
        },
        details: {
          type: apigateway.JsonSchemaType.STRING,
          description: 'Additional details about the error'
        }
      },
      additionalProperties: true
    }
  },

  // Error response model
  ErrorResponse: {
    contentType: 'application/json',
    modelName: 'ErrorResponse',
    description: 'Error response',
    schema: {
      type: apigateway.JsonSchemaType.OBJECT,
      title: 'Error Response',
      description: 'Standard error response format',
      required: ['error'],
      properties: {
        error: {
          type: apigateway.JsonSchemaType.STRING,
          description: 'Error message describing what went wrong'
        },
        details: {
          type: apigateway.JsonSchemaType.STRING,
          description: 'Additional error details for debugging'
        }
      },
      additionalProperties: true
    }
  },

};

// Method request parameters for validation
export const methodRequestParameters = {
  authValidate: {
    'method.request.header.Authorization': true
  },
  // Common headers for all endpoints
  commonHeaders: {
    'method.request.header.Content-Type': false,
    'method.request.header.Accept': false,
    'method.request.header.User-Agent': false
  },
  // CORS headers
  corsHeaders: {
    'method.request.header.Origin': false,
    'method.request.header.Access-Control-Request-Method': false,
    'method.request.header.Access-Control-Request-Headers': false
  }
};

// Method documentation for API Gateway
export const methodDocumentation = {
  authSignup: {
    summary: 'Create new user account',
    description: 'Register a new user with email, password, username and optional profile information. Returns success message upon successful registration.',
    tags: ['Authentication'],
    operationId: 'signup'
  },
  authSignin: {
    summary: 'Authenticate user',
    description: 'Authenticate user with email and password. Returns JWT tokens (access, refresh, ID) and user profile information upon successful authentication.',
    tags: ['Authentication'],
    operationId: 'signin'
  },
  authRefresh: {
    summary: 'Refresh access token',
    description: 'Refresh expired access token using a valid refresh token. Returns new access and ID tokens.',
    tags: ['Authentication'],
    operationId: 'refreshToken'
  },
  authValidate: {
    summary: 'Validate access token',
    description: 'Validate the provided JWT access token and return user information if valid. Requires Authorization header with Bearer token.',
    tags: ['Authentication'],
    operationId: 'validateToken'
  },

  // Posts endpoints
  postsGet: {
    summary: 'Get all posts',
    description: 'Retrieve a list of all published posts with pagination support. Returns posts sorted by creation date.',
    tags: ['Posts'],
    operationId: 'getPosts'
  },
  postsPost: {
    summary: 'Create new post',
    description: 'Create a new social media post with content and optional media attachments.',
    tags: ['Posts'],
    operationId: 'createPost'
  },
  postsDraftPost: {
    summary: 'Create draft post',
    description: 'Create a new post in draft status for later editing and publishing.',
    tags: ['Posts'],
    operationId: 'createDraftPost'
  },
  postGet: {
    summary: 'Get post by ID',
    description: 'Retrieve a specific post by its unique identifier with full details.',
    tags: ['Posts'],
    operationId: 'getPost'
  },
  postPut: {
    summary: 'Update post',
    description: 'Update an existing post content and metadata. Only the post owner can update.',
    tags: ['Posts'],
    operationId: 'updatePost'
  },
  postDelete: {
    summary: 'Delete post',
    description: 'Delete a post permanently. Only the post owner can delete.',
    tags: ['Posts'],
    operationId: 'deletePost'
  },
  postMediaPut: {
    summary: 'Attach media to post',
    description: 'Attach media files to an existing post by providing media IDs.',
    tags: ['Posts'],
    operationId: 'attachMediaToPost'
  },
  postPublishPut: {
    summary: 'Publish post',
    description: 'Change post status from draft to published, making it visible to other users.',
    tags: ['Posts'],
    operationId: 'publishPost'
  },
  postLikePost: {
    summary: 'Like post',
    description: 'Add a like to a post. Users can only like each post once.',
    tags: ['Posts', 'Engagement'],
    operationId: 'likePost'
  },
  postLikeDelete: {
    summary: 'Unlike post',
    description: 'Remove a like from a post. Users can only unlike posts they have liked.',
    tags: ['Posts', 'Engagement'],
    operationId: 'unlikePost'
  },

  // Users endpoints
  usersProfileGet: {
    summary: 'Get user profile',
    description: 'Retrieve the current user profile information including personal details and statistics.',
    tags: ['Users'],
    operationId: 'getUserProfile'
  },
  usersProfilePut: {
    summary: 'Update user profile',
    description: 'Update user profile information such as name, bio, and avatar.',
    tags: ['Users'],
    operationId: 'updateUserProfile'
  },
  usersProfileLikedPostsGet: {
    summary: 'Get user liked posts',
    description: 'Retrieve all posts that the current user has liked.',
    tags: ['Users', 'Posts'],
    operationId: 'getUserLikedPosts'
  },
  userGet: {
    summary: 'Get user by ID',
    description: 'Retrieve public profile information for a specific user by their ID.',
    tags: ['Users'],
    operationId: 'getUserById'
  },
  userFollowPost: {
    summary: 'Follow user',
    description: 'Follow another user to see their posts in your feed.',
    tags: ['Users', 'Social'],
    operationId: 'followUser'
  },
  userFollowDelete: {
    summary: 'Unfollow user',
    description: 'Stop following a user and remove their posts from your feed.',
    tags: ['Users', 'Social'],
    operationId: 'unfollowUser'
  },

  // Media endpoints
  mediaUploadPost: {
    summary: 'Upload media',
    description: 'Initiate media upload and get a presigned URL for file upload to cloud storage.',
    tags: ['Media'],
    operationId: 'uploadMedia'
  },
  mediaGet: {
    summary: 'Get media by ID',
    description: 'Retrieve media information and metadata by its unique identifier.',
    tags: ['Media'],
    operationId: 'getMedia'
  },
  mediaPut: {
    summary: 'Update media status',
    description: 'Update media processing status and metadata after upload completion.',
    tags: ['Media'],
    operationId: 'updateMediaStatus'
  },
  mediaDelete: {
    summary: 'Delete media',
    description: 'Delete media file and remove it from cloud storage. Only the owner can delete.',
    tags: ['Media'],
    operationId: 'deleteMedia'
  }
};

// Integration request templates
export const integrationRequestTemplates = {
  // Standard passthrough template
  passthroughTemplate: {
    'application/json': `{
      "body": $input.json('$'),
      "headers": {
        #foreach($header in $input.params().header.keySet())
        "$header": "$util.escapeJavaScript($input.params().header.get($header))"#if($foreach.hasNext),#end
        #end
      },
      "queryStringParameters": {
        #foreach($param in $input.params().querystring.keySet())
        "$param": "$util.escapeJavaScript($input.params().querystring.get($param))"#if($foreach.hasNext),#end
        #end
      },
      "pathParameters": {
        #foreach($param in $input.params().path.keySet())
        "$param": "$util.escapeJavaScript($input.params().path.get($param))"#if($foreach.hasNext),#end
        #end
      }
    }`
  }
};

// Posts API Schemas
export const postsSchemas = {
  // Post object schema
  Post: {
    contentType: 'application/json',
    modelName: 'Post',
    description: 'Post object with content and metadata',
    schema: {
      type: apigateway.JsonSchemaType.OBJECT,
      title: 'Post',
      description: 'Social media post with content, media, and engagement data',
      required: ['id', 'userId', 'content', 'status', 'createdAt'],
      properties: {
        id: {
          type: apigateway.JsonSchemaType.STRING,
          description: 'Unique post identifier'
        },
        userId: {
          type: apigateway.JsonSchemaType.STRING,
          description: 'ID of the user who created the post'
        },
        content: {
          type: apigateway.JsonSchemaType.STRING,
          description: 'Post content text'
        },
        mediaIds: {
          type: apigateway.JsonSchemaType.ARRAY,
          description: 'Array of media IDs attached to the post',
          items: {
            type: apigateway.JsonSchemaType.STRING
          }
        },
        status: {
          type: apigateway.JsonSchemaType.STRING,
          description: 'Post status',
          enum: ['draft', 'published', 'archived']
        },
        active: {
          type: apigateway.JsonSchemaType.BOOLEAN,
          description: 'Whether the post is active'
        },
        likesCount: {
          type: apigateway.JsonSchemaType.INTEGER,
          description: 'Number of likes on the post'
        },
        commentsCount: {
          type: apigateway.JsonSchemaType.INTEGER,
          description: 'Number of comments on the post'
        },
        createdAt: {
          type: apigateway.JsonSchemaType.STRING,
          description: 'Post creation timestamp'
        },
        updatedAt: {
          type: apigateway.JsonSchemaType.STRING,
          description: 'Post last update timestamp'
        }
      },
      additionalProperties: true
    }
  },

  // Create post request
  CreatePostRequest: {
    contentType: 'application/json',
    modelName: 'CreatePostRequest',
    description: 'Request body for creating a new post',
    schema: {
      type: apigateway.JsonSchemaType.OBJECT,
      title: 'Create Post Request',
      description: 'Request to create a new social media post',
      required: ['content'],
      properties: {
        content: {
          type: apigateway.JsonSchemaType.STRING,
          description: 'Post content text',
          minLength: 1,
          maxLength: 2000
        },
        title: {
          type: apigateway.JsonSchemaType.STRING,
          description: 'Optional post title',
          maxLength: 200
        },
        channelId: {
          type: apigateway.JsonSchemaType.STRING,
          description: 'Optional channel ID to post to'
        },
        mediaIds: {
          type: apigateway.JsonSchemaType.ARRAY,
          description: 'Array of media IDs to attach to the post',
          items: {
            type: apigateway.JsonSchemaType.STRING
          },
          maxItems: 10
        }
      },
      additionalProperties: true
    }
  },

  // Update post request
  UpdatePostRequest: {
    contentType: 'application/json',
    modelName: 'UpdatePostRequest',
    description: 'Request body for updating a post',
    schema: {
      type: apigateway.JsonSchemaType.OBJECT,
      title: 'Update Post Request',
      description: 'Request to update an existing post',
      properties: {
        content: {
          type: apigateway.JsonSchemaType.STRING,
          description: 'Updated post content text',
          minLength: 1,
          maxLength: 2000
        },
        title: {
          type: apigateway.JsonSchemaType.STRING,
          description: 'Updated post title',
          maxLength: 200
        },
        channelId: {
          type: apigateway.JsonSchemaType.STRING,
          description: 'Updated channel ID to post to'
        },
        mediaIds: {
          type: apigateway.JsonSchemaType.ARRAY,
          description: 'Updated array of media IDs',
          items: {
            type: apigateway.JsonSchemaType.STRING
          },
          maxItems: 10
        }
      },
      additionalProperties: true
    }
  },

  // Posts list response
  PostsListResponse: {
    contentType: 'application/json',
    modelName: 'PostsListResponse',
    description: 'Response containing list of posts',
    schema: {
      type: apigateway.JsonSchemaType.OBJECT,
      title: 'Posts List Response',
      description: 'List of posts with pagination information',
      required: ['posts'],
      properties: {
        posts: {
          type: apigateway.JsonSchemaType.ARRAY,
          description: 'Array of posts',
          items: {
            type: apigateway.JsonSchemaType.OBJECT,
            description: 'Post object'
          }
        },
        pagination: {
          type: apigateway.JsonSchemaType.OBJECT,
          description: 'Pagination information',
          properties: {
            total: {
              type: apigateway.JsonSchemaType.INTEGER,
              description: 'Total number of posts'
            },
            page: {
              type: apigateway.JsonSchemaType.INTEGER,
              description: 'Current page number'
            },
            limit: {
              type: apigateway.JsonSchemaType.INTEGER,
              description: 'Number of posts per page'
            }
          }
        }
      },
      additionalProperties: true
    }
  },

  // Post response
  PostResponse: {
    contentType: 'application/json',
    modelName: 'PostResponse',
    description: 'Response containing a single post',
    schema: {
      type: apigateway.JsonSchemaType.OBJECT,
      title: 'Post Response',
      description: 'Single post with full details',
      required: ['post'],
      properties: {
        post: {
          type: apigateway.JsonSchemaType.OBJECT,
          description: 'Post object with content and metadata'
        }
      },
      additionalProperties: true
    }
  },

  // Attach media request
  AttachMediaRequest: {
    contentType: 'application/json',
    modelName: 'AttachMediaRequest',
    description: 'Request to attach media to a post',
    schema: {
      type: apigateway.JsonSchemaType.OBJECT,
      title: 'Attach Media Request',
      description: 'Request to attach media files to an existing post',
      required: ['media_id'],
      properties: {
        media_id: {
          type: apigateway.JsonSchemaType.STRING,
          description: 'Media ID to attach to the post'
        }
      },
      additionalProperties: true
    }
  }
};

// Users API Schemas
export const usersSchemas = {
  // User profile schema
  UserProfile: {
    contentType: 'application/json',
    modelName: 'UserProfile',
    description: 'User profile information',
    schema: {
      type: apigateway.JsonSchemaType.OBJECT,
      title: 'User Profile',
      description: 'Complete user profile with personal information and settings',
      required: ['userId', 'username'],
      properties: {
        userId: {
          type: apigateway.JsonSchemaType.STRING,
          description: 'Unique user identifier'
        },
        username: {
          type: apigateway.JsonSchemaType.STRING,
          description: 'User username'
        },
        email: {
          type: apigateway.JsonSchemaType.STRING,
          description: 'User email address'
        },
        firstName: {
          type: apigateway.JsonSchemaType.STRING,
          description: 'User first name'
        },
        lastName: {
          type: apigateway.JsonSchemaType.STRING,
          description: 'User last name'
        },
        bio: {
          type: apigateway.JsonSchemaType.STRING,
          description: 'User biography'
        },
        avatarUrl: {
          type: apigateway.JsonSchemaType.STRING,
          description: 'URL to user avatar image'
        },
        followersCount: {
          type: apigateway.JsonSchemaType.INTEGER,
          description: 'Number of followers'
        },
        followingCount: {
          type: apigateway.JsonSchemaType.INTEGER,
          description: 'Number of users being followed'
        },
        postsCount: {
          type: apigateway.JsonSchemaType.INTEGER,
          description: 'Number of posts created'
        },
        createdAt: {
          type: apigateway.JsonSchemaType.STRING,
          description: 'Account creation timestamp'
        },
        updatedAt: {
          type: apigateway.JsonSchemaType.STRING,
          description: 'Profile last update timestamp'
        }
      },
      additionalProperties: true
    }
  },

  // Update profile request
  UpdateProfileRequest: {
    contentType: 'application/json',
    modelName: 'UpdateProfileRequest',
    description: 'Request to update user profile',
    schema: {
      type: apigateway.JsonSchemaType.OBJECT,
      title: 'Update Profile Request',
      description: 'Request to update user profile information',
      properties: {
        firstName: {
          type: apigateway.JsonSchemaType.STRING,
          description: 'Updated first name',
          maxLength: 50
        },
        lastName: {
          type: apigateway.JsonSchemaType.STRING,
          description: 'Updated last name',
          maxLength: 50
        },
        bio: {
          type: apigateway.JsonSchemaType.STRING,
          description: 'Updated biography',
          maxLength: 500
        },
        avatarUrl: {
          type: apigateway.JsonSchemaType.STRING,
          description: 'Updated avatar URL'
        }
      },
      additionalProperties: true
    }
  },

  // User profile response
  UserProfileResponse: {
    contentType: 'application/json',
    modelName: 'UserProfileResponse',
    description: 'Response containing user profile',
    schema: {
      type: apigateway.JsonSchemaType.OBJECT,
      title: 'User Profile Response',
      description: 'User profile information response',
      required: ['profile'],
      properties: {
        profile: {
          type: apigateway.JsonSchemaType.OBJECT,
          description: 'User profile information'
        }
      },
      additionalProperties: true
    }
  },

  // Follow user request
  FollowUserRequest: {
    contentType: 'application/json',
    modelName: 'FollowUserRequest',
    description: 'Request to follow a user',
    schema: {
      type: apigateway.JsonSchemaType.OBJECT,
      title: 'Follow User Request',
      description: 'Request to follow another user',
      required: ['userId'],
      properties: {
        userId: {
          type: apigateway.JsonSchemaType.STRING,
          description: 'ID of the user to follow'
        }
      },
      additionalProperties: true
    }
  },

  // Follow response
  FollowResponse: {
    contentType: 'application/json',
    modelName: 'FollowResponse',
    description: 'Response for follow/unfollow actions',
    schema: {
      type: apigateway.JsonSchemaType.OBJECT,
      title: 'Follow Response',
      description: 'Response indicating follow action result',
      required: ['success', 'message'],
      properties: {
        success: {
          type: apigateway.JsonSchemaType.BOOLEAN,
          description: 'Whether the action was successful'
        },
        message: {
          type: apigateway.JsonSchemaType.STRING,
          description: 'Success or error message'
        },
        following: {
          type: apigateway.JsonSchemaType.BOOLEAN,
          description: 'Current follow status'
        }
      },
      additionalProperties: true
    }
  }
};

// Media API Schemas
export const mediaSchemas = {
  // Media object schema
  Media: {
    contentType: 'application/json',
    modelName: 'Media',
    description: 'Media file information',
    schema: {
      type: apigateway.JsonSchemaType.OBJECT,
      title: 'Media',
      description: 'Media file with metadata and upload information',
      required: ['id', 'userId', 'fileName', 'fileType', 'status'],
      properties: {
        id: {
          type: apigateway.JsonSchemaType.STRING,
          description: 'Unique media identifier'
        },
        userId: {
          type: apigateway.JsonSchemaType.STRING,
          description: 'ID of the user who uploaded the media'
        },
        fileName: {
          type: apigateway.JsonSchemaType.STRING,
          description: 'Original file name'
        },
        fileType: {
          type: apigateway.JsonSchemaType.STRING,
          description: 'MIME type of the file'
        },
        fileSize: {
          type: apigateway.JsonSchemaType.INTEGER,
          description: 'File size in bytes'
        },
        url: {
          type: apigateway.JsonSchemaType.STRING,
          description: 'Public URL to access the media'
        },
        thumbnailUrl: {
          type: apigateway.JsonSchemaType.STRING,
          description: 'URL to thumbnail version'
        },
        status: {
          type: apigateway.JsonSchemaType.STRING,
          description: 'Media processing status',
          enum: ['uploading', 'processing', 'ready', 'failed']
        },
        metadata: {
          type: apigateway.JsonSchemaType.OBJECT,
          description: 'Additional metadata about the media',
          properties: {
            width: {
              type: apigateway.JsonSchemaType.INTEGER,
              description: 'Image/video width in pixels'
            },
            height: {
              type: apigateway.JsonSchemaType.INTEGER,
              description: 'Image/video height in pixels'
            },
            duration: {
              type: apigateway.JsonSchemaType.NUMBER,
              description: 'Video duration in seconds'
            }
          }
        },
        createdAt: {
          type: apigateway.JsonSchemaType.STRING,
          description: 'Upload timestamp'
        },
        updatedAt: {
          type: apigateway.JsonSchemaType.STRING,
          description: 'Last update timestamp'
        }
      },
      additionalProperties: true
    }
  },

  // Upload media request
  UploadMediaRequest: {
    contentType: 'application/json',
    modelName: 'UploadMediaRequest',
    description: 'Request to upload media',
    schema: {
      type: apigateway.JsonSchemaType.OBJECT,
      title: 'Upload Media Request',
      description: 'Request to initiate media upload',
      required: ['fileName', 'fileType', 'fileSize'],
      properties: {
        fileName: {
          type: apigateway.JsonSchemaType.STRING,
          description: 'Name of the file to upload',
          maxLength: 255
        },
        fileType: {
          type: apigateway.JsonSchemaType.STRING,
          description: 'MIME type of the file',
          pattern: '^(image|video|audio)\/[a-zA-Z0-9][a-zA-Z0-9!#$&\\-\\^_]*$'
        },
        fileSize: {
          type: apigateway.JsonSchemaType.INTEGER,
          description: 'File size in bytes',
          minimum: 1,
          maximum: 31457280
        },
        mediaType: {
          type: apigateway.JsonSchemaType.STRING,
          description: 'Type of media content',
          enum: ['image', 'video', 'audio', 'avatar', 'reflex']
        }
      },
      additionalProperties: true
    }
  },

  // Upload media response
  UploadMediaResponse: {
    contentType: 'application/json',
    modelName: 'UploadMediaResponse',
    description: 'Response for media upload initiation',
    schema: {
      type: apigateway.JsonSchemaType.OBJECT,
      title: 'Upload Media Response',
      description: 'Response containing upload URL and media information',
      required: ['mediaId', 'uploadUrl'],
      properties: {
        mediaId: {
          type: apigateway.JsonSchemaType.STRING,
          description: 'Unique identifier for the media'
        },
        uploadUrl: {
          type: apigateway.JsonSchemaType.STRING,
          description: 'Presigned URL for uploading the file'
        },
        expiresIn: {
          type: apigateway.JsonSchemaType.INTEGER,
          description: 'Upload URL expiration time in seconds'
        }
      },
      additionalProperties: true
    }
  },

  // Media response
  MediaResponse: {
    contentType: 'application/json',
    modelName: 'MediaResponse',
    description: 'Response containing media information',
    schema: {
      type: apigateway.JsonSchemaType.OBJECT,
      title: 'Media Response',
      description: 'Single media file information',
      required: ['media'],
      properties: {
        media: {
          type: apigateway.JsonSchemaType.OBJECT,
          description: 'Media file with metadata and upload information'
        }
      },
      additionalProperties: true
    }
  },

  // Update media status request
  UpdateMediaStatusRequest: {
    contentType: 'application/json',
    modelName: 'UpdateMediaStatusRequest',
    description: 'Request to update media status',
    schema: {
      type: apigateway.JsonSchemaType.OBJECT,
      title: 'Update Media Status Request',
      description: 'Request to update media processing status',
      required: ['status'],
      properties: {
        status: {
          type: apigateway.JsonSchemaType.STRING,
          description: 'New media status',
          enum: ['uploading', 'processing', 'ready', 'failed']
        },
        url: {
          type: apigateway.JsonSchemaType.STRING,
          description: 'Public URL for the media (when ready)'
        },
        thumbnailUrl: {
          type: apigateway.JsonSchemaType.STRING,
          description: 'Thumbnail URL (when ready)'
        },
        metadata: {
          type: apigateway.JsonSchemaType.OBJECT,
          description: 'Updated metadata',
          properties: {
            width: {
              type: apigateway.JsonSchemaType.INTEGER
            },
            height: {
              type: apigateway.JsonSchemaType.INTEGER
            },
            duration: {
              type: apigateway.JsonSchemaType.NUMBER
            }
          }
        }
      },
      additionalProperties: true
    }
  }
};

// All API Gateway Request Validator Models (combined)
export const allRequestValidatorModels = {
  ...requestValidatorModels,
  ...postsSchemas,
  ...usersSchemas,
  ...mediaSchemas
};

// Complete OpenAPI specification for auth endpoints
export const authOpenApiSpec = {
  openapi: '3.0.3',
  info: {
    title: 'GameFlex Auth API',
    description: 'Authentication endpoints for the GameFlex platform',
    version: '1.0.0'
  },
  components: {
    schemas: {
      ...commonSchemas,
      ...authSchemas
    },
    parameters: parameterSchemas
  }
};
