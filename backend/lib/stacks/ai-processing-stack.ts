import * as cdk from 'aws-cdk-lib';
import * as lambda from 'aws-cdk-lib/aws-lambda';
import * as s3 from 'aws-cdk-lib/aws-s3';
import * as s3n from 'aws-cdk-lib/aws-s3-notifications';
import * as dynamodb from 'aws-cdk-lib/aws-dynamodb';
import * as iam from 'aws-cdk-lib/aws-iam';
import { Construct } from 'constructs';

export interface AiProcessingStackProps extends cdk.NestedStackProps {
  projectName: string;
  environment: string;
  mediaBucket: s3.Bucket;
  mediaTable: dynamodb.Table;
}

export interface AiProcessingStackOutputs {
  aiProcessingFunction: lambda.Function;
}

export class AiProcessingStack extends cdk.NestedStack implements AiProcessingStackOutputs {
  public readonly aiProcessingFunction: lambda.Function;

  constructor(scope: Construct, id: string, props: AiProcessingStackProps) {
    super(scope, id, props);

    const { projectName, environment, mediaBucket, mediaTable } = props;

    // Create AI processing Lambda function
    this.aiProcessingFunction = this.createAiProcessingFunction(
      projectName,
      environment,
      mediaBucket,
      mediaTable
    );

    // Note: S3 event trigger will be set up in the storage stack to avoid circular dependencies

    // Create outputs
    this.createOutputs();
  }

  private createAiProcessingFunction(
    projectName: string,
    environment: string,
    mediaBucket: s3.Bucket,
    mediaTable: dynamodb.Table
  ): lambda.Function {
    const functionName = `${projectName}-ai-processing-${environment}`;

    const aiFunction = new lambda.Function(this, 'AiProcessingFunction', {
      functionName,
      runtime: lambda.Runtime.NODEJS_20_X,
      handler: 'index.handler',
      code: lambda.Code.fromAsset('src/ai-processing'),
      timeout: cdk.Duration.minutes(5), // AI processing can take time
      memorySize: 1024, // More memory for image processing
      environment: {
        ENVIRONMENT: environment,
        MEDIA_TABLE: mediaTable.tableName,
        BUCKET_NAME: mediaBucket.bucketName,
      },
      reservedConcurrentExecutions: environment === 'production' ? 10 : 5, // Limit concurrent executions
    });

    // Grant permissions to the AI processing function
    mediaTable.grantReadWriteData(aiFunction);
    mediaBucket.grantReadWrite(aiFunction);

    // Grant comprehensive Rekognition permissions
    aiFunction.addToRolePolicy(
      new iam.PolicyStatement({
        effect: iam.Effect.ALLOW,
        actions: [
          'rekognition:DetectModerationLabels',
          'rekognition:DetectLabels',
          'rekognition:DetectText',
          'rekognition:DetectFaces',
          'rekognition:RecognizeCelebrities',
          'rekognition:DetectProtectiveEquipment',
          'rekognition:GetLabelDetection',
          'rekognition:GetContentModeration',
        ],
        resources: ['*'],
      })
    );

    // Grant CloudWatch Logs permissions
    aiFunction.addToRolePolicy(
      new iam.PolicyStatement({
        effect: iam.Effect.ALLOW,
        actions: [
          'logs:CreateLogGroup',
          'logs:CreateLogStream',
          'logs:PutLogEvents',
        ],
        resources: [
          `arn:aws:logs:${cdk.Stack.of(this).region}:${cdk.Stack.of(this).account}:log-group:/aws/lambda/${functionName}*`,
        ],
      })
    );

    return aiFunction;
  }



  private createOutputs(): void {
    new cdk.CfnOutput(this, 'AiProcessingFunctionArn', {
      value: this.aiProcessingFunction.functionArn,
      description: 'ARN of the AI processing Lambda function',
      exportName: `${this.stackName}-AiProcessingFunctionArn`,
    });

    new cdk.CfnOutput(this, 'AiProcessingFunctionName', {
      value: this.aiProcessingFunction.functionName,
      description: 'Name of the AI processing Lambda function',
      exportName: `${this.stackName}-AiProcessingFunctionName`,
    });
  }
}
