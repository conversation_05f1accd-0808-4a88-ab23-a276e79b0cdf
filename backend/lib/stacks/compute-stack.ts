import * as cdk from 'aws-cdk-lib';
import * as lambda from 'aws-cdk-lib/aws-lambda';
import * as cognito from 'aws-cdk-lib/aws-cognito';
import * as dynamodb from 'aws-cdk-lib/aws-dynamodb';
import * as secretsmanager from 'aws-cdk-lib/aws-secretsmanager';
import { Construct } from 'constructs';

export interface ComputeStackProps extends cdk.NestedStackProps {
  projectName: string;
  environment: string;
  userPool: cognito.UserPool;
  userPoolClient: cognito.UserPoolClient;
  tables: { [key: string]: dynamodb.Table };
  appConfigSecret: secretsmanager.ISecret;
  appleConfigSecret: secretsmanager.ISecret;
  xboxConfigSecret: secretsmanager.ISecret;
}

export interface ComputeStackOutputs {
  functions: { [key: string]: lambda.Function };
}

export class ComputeStack extends cdk.NestedStack implements ComputeStackOutputs {
  public readonly functions: { [key: string]: lambda.Function };

  constructor(scope: Construct, id: string, props: ComputeStackProps) {
    super(scope, id, props);

    const {
      projectName,
      environment,
      userPool,
      userPoolClient,
      tables,
      appConfigSecret,
      appleConfigSecret,
      xboxConfigSecret
    } = props;

    this.functions = this.createLambdaFunctions(
      projectName,
      environment,
      userPool,
      userPoolClient,
      tables,
      appConfigSecret,
      appleConfigSecret,
      xboxConfigSecret
    );

    // Create outputs for Lambda function ARNs
    this.createFunctionOutputs();
  }

  private createLambdaFunctions(
    projectName: string,
    environment: string,
    userPool: cognito.UserPool,
    userPoolClient: cognito.UserPoolClient,
    tables: { [key: string]: dynamodb.Table },
    appConfigSecret: secretsmanager.ISecret,
    appleConfigSecret: secretsmanager.ISecret,
    xboxConfigSecret: secretsmanager.ISecret
  ): { [key: string]: lambda.Function } {
    const functions: { [key: string]: lambda.Function } = {};

    // Common environment variables for all Lambda functions
    const commonEnvironment = {
      ENVIRONMENT: environment,
      PROJECT_NAME: projectName,
      USER_POOL_ID: userPool.userPoolId,
      USER_POOL_CLIENT_ID: userPoolClient.userPoolClientId,
      POSTS_TABLE: tables.posts.tableName,
      MEDIA_TABLE: tables.media.tableName,
      USER_PROFILES_TABLE: tables.userProfiles.tableName,
      COMMENTS_TABLE: tables.comments.tableName,
      LIKES_TABLE: tables.likes.tableName,
      FOLLOWS_TABLE: tables.follows.tableName,
      CHANNELS_TABLE: tables.channels.tableName,
      CHANNEL_MEMBERS_TABLE: tables.channelMembers.tableName,
      REFLEXES_TABLE: tables.reflexes.tableName,
      REFLEX_LIKES_TABLE: tables.reflexLikes.tableName,
      POST_REACTIONS_TABLE: tables.postReactions.tableName,
      REFLEX_REACTIONS_TABLE: tables.reflexReactions.tableName,
      USERS_TABLE: tables.users.tableName,
      APP_CONFIG_SECRET_NAME: appConfigSecret.secretName,
      APPLE_CONFIG_SECRET_NAME: appleConfigSecret.secretName,
      XBOX_CONFIG_SECRET_NAME: xboxConfigSecret.secretName,
    };

    // Authorizer Function
    functions.authorizer = new lambda.Function(this, 'AuthorizerFunction', {
      functionName: `${projectName}-authorizer-${environment}`,
      runtime: lambda.Runtime.NODEJS_20_X,
      handler: 'index.handler',
      code: lambda.Code.fromAsset('src/authorizer'),
      timeout: cdk.Duration.seconds(30),
      memorySize: 256,
      environment: {
        ...commonEnvironment,
        USER_POOL_ID: userPool.userPoolId,
        USERS_TABLE: tables.users.tableName,
      },
    });

    // Grant permissions to authorizer
    userPool.grant(functions.authorizer, 'cognito-idp:GetUser');
    tables.users.grantReadData(functions.authorizer);

    // Health Function
    functions.health = new lambda.Function(this, 'HealthFunction', {
      functionName: `${projectName}-health-${environment}`,
      runtime: lambda.Runtime.NODEJS_20_X,
      handler: 'index.handler',
      code: lambda.Code.fromAsset('src/health'),
      timeout: cdk.Duration.seconds(30),
      memorySize: 256,
      environment: commonEnvironment,
    });

    // Grant permissions to health function
    tables.users.grantReadData(functions.health);

    // Auth Functions - separate functions for each operation
    functions.authSignup = new lambda.Function(this, 'AuthSignupFunction', {
      functionName: `${projectName}-auth-signup-${environment}`,
      runtime: lambda.Runtime.NODEJS_20_X,
      handler: 'signup.handler',
      code: lambda.Code.fromAsset('src/auth'),
      timeout: cdk.Duration.seconds(30),
      memorySize: 256,
      environment: commonEnvironment,
    });

    functions.authSignin = new lambda.Function(this, 'AuthSigninFunction', {
      functionName: `${projectName}-auth-signin-${environment}`,
      runtime: lambda.Runtime.NODEJS_20_X,
      handler: 'signin.handler',
      code: lambda.Code.fromAsset('src/auth'),
      timeout: cdk.Duration.seconds(30),
      memorySize: 256,
      environment: commonEnvironment,
    });

    functions.authAppleSignin = new lambda.Function(this, 'AuthAppleSigninFunction', {
      functionName: `${projectName}-auth-apple-signin-${environment}`,
      runtime: lambda.Runtime.NODEJS_20_X,
      handler: 'apple-signin.handler',
      code: lambda.Code.fromAsset('src/auth'),
      timeout: cdk.Duration.seconds(30),
      memorySize: 256,
      environment: commonEnvironment,
    });

    functions.authRefresh = new lambda.Function(this, 'AuthRefreshFunction', {
      functionName: `${projectName}-auth-refresh-${environment}`,
      runtime: lambda.Runtime.NODEJS_20_X,
      handler: 'refresh.handler',
      code: lambda.Code.fromAsset('src/auth'),
      timeout: cdk.Duration.seconds(30),
      memorySize: 256,
      environment: commonEnvironment,
    });

    functions.authValidate = new lambda.Function(this, 'AuthValidateFunction', {
      functionName: `${projectName}-auth-validate-${environment}`,
      runtime: lambda.Runtime.NODEJS_20_X,
      handler: 'validate.handler',
      code: lambda.Code.fromAsset('src/auth'),
      timeout: cdk.Duration.seconds(30),
      memorySize: 256,
      environment: commonEnvironment,
    });

    functions.authSetUsername = new lambda.Function(this, 'AuthSetUsernameFunction', {
      functionName: `${projectName}-auth-set-username-${environment}`,
      runtime: lambda.Runtime.NODEJS_20_X,
      handler: 'set-username.handler',
      code: lambda.Code.fromAsset('src/auth'),
      timeout: cdk.Duration.seconds(30),
      memorySize: 256,
      environment: commonEnvironment,
    });

    // Grant permissions to auth functions
    tables.users.grantReadWriteData(functions.authSignup);
    userPool.grant(functions.authSignup, 'cognito-idp:*');

    tables.users.grantReadData(functions.authSignin);
    userPool.grant(functions.authSignin, 'cognito-idp:AdminInitiateAuth');

    tables.users.grantReadWriteData(functions.authAppleSignin);
    userPool.grant(functions.authAppleSignin, 'cognito-idp:*');
    appleConfigSecret.grantRead(functions.authAppleSignin);

    userPool.grant(functions.authRefresh, 'cognito-idp:AdminInitiateAuth');

    tables.users.grantReadData(functions.authValidate);
    userPool.grant(functions.authValidate, 'cognito-idp:GetUser');

    tables.users.grantReadWriteData(functions.authSetUsername);

    return functions;
  }

  private createFunctionOutputs(): void {
    // Export function ARNs for use by other stacks
    Object.entries(this.functions).forEach(([name, func]) => {
      new cdk.CfnOutput(this, `${name}FunctionArn`, {
        value: func.functionArn,
        description: `${name} Lambda Function ARN`,
        exportName: `${this.stackName}-${name}FunctionArn`,
      });

      new cdk.CfnOutput(this, `${name}FunctionName`, {
        value: func.functionName,
        description: `${name} Lambda Function Name`,
        exportName: `${this.stackName}-${name}FunctionName`,
      });
    });
  }
}
