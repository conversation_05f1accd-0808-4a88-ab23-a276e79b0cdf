import * as cdk from 'aws-cdk-lib';
import * as apigateway from 'aws-cdk-lib/aws-apigateway';
import * as iam from 'aws-cdk-lib/aws-iam';
import * as lambda from 'aws-cdk-lib/aws-lambda';
import { Construct } from 'constructs';
import { allRequestValidatorModels } from '../api-schemas';

export interface ApiGatewayStackProps extends cdk.NestedStackProps {
  projectName: string;
  environment: string;
  isProductionOrStaging: boolean;
  authorizerFunction?: lambda.Function;
  allFunctions?: { [key: string]: lambda.Function };
  domainName?: string;
}

export interface ApiGatewayStackOutputs {
  api: apigateway.RestApi;
  apiUrl: string;
}

export class ApiGatewayStack extends cdk.NestedStack implements ApiGatewayStackOutputs {
  public readonly api: apigateway.RestApi;
  public readonly apiUrl: string;

  constructor(scope: Construct, id: string, props: ApiGatewayStackProps) {
    super(scope, id, props);

    const {
      projectName,
      environment,
      isProductionOrStaging,
      authorizerFunction,
      allFunctions,
      domainName
    } = props;

    // Set up CloudWatch Logs role for API Gateway (required for logging)
    this.setupApiGatewayLogging();

    // Create API Gateway
    this.api = this.createApiGateway(projectName, environment);

    // Create simplified models to reduce resource count
    const models = this.createSimplifiedModels(environment);

    // Create authorizer only if function is provided
    let authorizer: apigateway.RequestAuthorizer | undefined;
    if (authorizerFunction) {
      authorizer = this.createAuthorizer(authorizerFunction);
    }

    // Create request validators
    const validators = this.createRequestValidators();

    // Create API resources and methods with proper Lambda integrations
    if (!allFunctions) {
      throw new Error('allFunctions is required for API Gateway stack');
    }

    this.createApiResources(
      this.api,
      authorizer,
      validators,
      models,
      environment,
      isProductionOrStaging,
      allFunctions
    );

    // Grant API Gateway permissions to invoke Lambda functions
    if (allFunctions) {
      Object.values(allFunctions).forEach((func, index) => {
        func.addPermission(`ApiGatewayInvoke${index}`, {
          principal: new iam.ServicePrincipal('apigateway.amazonaws.com'),
          sourceArn: this.api.arnForExecuteApi('*'),
        });
      });
    }

    // Set up custom domain if provided
    if (domainName) {
      this.setupCustomDomain(domainName, environment);
    }

    this.apiUrl = this.api.url;

    // Create outputs
    this.createOutputs();
  }

  private setupApiGatewayLogging(): void {
    // Create CloudWatch Logs role for API Gateway
    const apiGatewayLogsRole = new iam.Role(this, 'ApiGatewayLogsRole', {
      assumedBy: new iam.ServicePrincipal('apigateway.amazonaws.com'),
      managedPolicies: [
        iam.ManagedPolicy.fromAwsManagedPolicyName('service-role/AmazonAPIGatewayPushToCloudWatchLogs'),
      ],
    });

    // Set the CloudWatch Logs role for API Gateway account settings
    new apigateway.CfnAccount(this, 'ApiGatewayAccount', {
      cloudWatchRoleArn: apiGatewayLogsRole.roleArn,
    });
  }

  private createApiGateway(projectName: string, environment: string): apigateway.RestApi {
    return new apigateway.RestApi(this, 'Api', {
      restApiName: `${projectName}-api-${environment}`,
      description: `GameFlex API for ${environment} environment`,
      defaultCorsPreflightOptions: {
        allowOrigins: apigateway.Cors.ALL_ORIGINS,
        allowMethods: apigateway.Cors.ALL_METHODS,
        allowHeaders: [
          'Content-Type',
          'X-Amz-Date',
          'Authorization',
          'X-Api-Key',
          'X-Amz-Security-Token',
          'X-Amz-User-Agent',
        ],
      },
      deployOptions: {
        stageName: environment,
        throttlingRateLimit: 1000,
        throttlingBurstLimit: 2000,
        loggingLevel: apigateway.MethodLoggingLevel.INFO,
        dataTraceEnabled: true,
        metricsEnabled: true,
      },
      endpointConfiguration: {
        types: [apigateway.EndpointType.REGIONAL],
      },
    });
  }

  private createSimplifiedModels(environment: string): { [key: string]: apigateway.Model } {
    // Create only essential models to reduce resource count
    const models: { [key: string]: apigateway.Model } = {};

    // Generic request model for all endpoints
    models.genericRequest = new apigateway.Model(this, 'GenericRequestModel', {
      restApi: this.api,
      modelName: `GenericRequest${environment}`,
      contentType: 'application/json',
      schema: {
        type: apigateway.JsonSchemaType.OBJECT,
        additionalProperties: true,
        description: 'Generic request model for all endpoints'
      },
      description: 'Consolidated request model to reduce resource count',
    });

    // Generic response model for all endpoints
    models.genericResponse = new apigateway.Model(this, 'GenericResponseModel', {
      restApi: this.api,
      modelName: `GenericResponse${environment}`,
      contentType: 'application/json',
      schema: {
        type: apigateway.JsonSchemaType.OBJECT,
        additionalProperties: true,
        description: 'Generic response model for all endpoints'
      },
      description: 'Consolidated response model to reduce resource count',
    });

    // Error response model
    models.errorResponse = new apigateway.Model(this, 'ErrorResponseModel', {
      restApi: this.api,
      modelName: `${allRequestValidatorModels.ErrorResponse.modelName}${environment}`,
      contentType: allRequestValidatorModels.ErrorResponse.contentType,
      schema: allRequestValidatorModels.ErrorResponse.schema,
      description: allRequestValidatorModels.ErrorResponse.description,
    });

    return models;
  }

  private createAuthorizer(authorizerFunction: lambda.Function): apigateway.RequestAuthorizer {
    return new apigateway.RequestAuthorizer(this, 'Authorizer', {
      handler: authorizerFunction,
      identitySources: [apigateway.IdentitySource.header('Authorization')],
      authorizerName: 'GameFlexAuthorizer',
      resultsCacheTtl: cdk.Duration.minutes(5),
    });
  }

  private createRequestValidators(): { [key: string]: apigateway.RequestValidator } {
    return {
      bodyValidator: new apigateway.RequestValidator(this, 'BodyValidator', {
        restApi: this.api,
        requestValidatorName: 'BodyValidator',
        validateRequestBody: true,
        validateRequestParameters: false,
      }),
      paramsValidator: new apigateway.RequestValidator(this, 'ParamsValidator', {
        restApi: this.api,
        requestValidatorName: 'ParamsValidator',
        validateRequestBody: false,
        validateRequestParameters: true,
      }),
      fullValidator: new apigateway.RequestValidator(this, 'FullValidator', {
        restApi: this.api,
        requestValidatorName: 'FullValidator',
        validateRequestBody: true,
        validateRequestParameters: true,
      }),
    };
  }

  private createLambdaIntegration(functionName: string, allFunctions: { [key: string]: lambda.Function }): apigateway.LambdaIntegration {
    if (!allFunctions[functionName]) {
      throw new Error(`Lambda function '${functionName}' not found. Available functions: ${Object.keys(allFunctions).join(', ')}`);
    }

    return new apigateway.LambdaIntegration(allFunctions[functionName], {
      proxy: true,
    });
  }

  private createMethodOptions(
    authorizer: apigateway.RequestAuthorizer | undefined,
    validators: { [key: string]: apigateway.RequestValidator },
    models: { [key: string]: apigateway.Model },
    requireAuth: boolean = true,
    requestValidator?: string,
    requestModel?: string
  ): apigateway.MethodOptions {
    const options: apigateway.MethodOptions = {
      requestValidator: validators[requestValidator || 'paramsValidator'],
      methodResponses: [
        { statusCode: '200', responseModels: { 'application/json': models.genericResponse } },
        { statusCode: '400', responseModels: { 'application/json': models.errorResponse } },
        { statusCode: '401', responseModels: { 'application/json': models.errorResponse } },
        { statusCode: '403', responseModels: { 'application/json': models.errorResponse } },
        { statusCode: '500', responseModels: { 'application/json': models.errorResponse } },
      ],
    };

    // Add authorizer if required
    if (requireAuth && authorizer) {
      (options as any).authorizer = authorizer;
    }

    // Add request models if specified
    if (requestModel) {
      (options as any).requestModels = { 'application/json': models[requestModel] };
    }

    return options;
  }

  private createApiResources(
    api: apigateway.RestApi,
    authorizer: apigateway.RequestAuthorizer | undefined,
    validators: { [key: string]: apigateway.RequestValidator },
    models: { [key: string]: apigateway.Model },
    environment: string,
    isProductionOrStaging: boolean,
    allFunctions: { [key: string]: lambda.Function }
  ): void {
    // Create API structure with proper Lambda integrations and authorization

    // Health endpoint (public)
    const healthResource = api.root.addResource('health');
    healthResource.addMethod('GET',
      this.createLambdaIntegration('health', allFunctions),
      this.createMethodOptions(authorizer, validators, models, false)
    );

    // Auth endpoints
    const authResource = api.root.addResource('auth');

    // Public auth endpoints (no authorization required)
    authResource.addResource('signup').addMethod('POST',
      this.createLambdaIntegration('authSignup', allFunctions),
      this.createMethodOptions(authorizer, validators, models, false, 'bodyValidator', 'genericRequest')
    );

    authResource.addResource('signin').addMethod('POST',
      this.createLambdaIntegration('authSignin', allFunctions),
      this.createMethodOptions(authorizer, validators, models, false, 'bodyValidator', 'genericRequest')
    );

    authResource.addResource('apple-signin').addMethod('POST',
      this.createLambdaIntegration('authAppleSignin', allFunctions),
      this.createMethodOptions(authorizer, validators, models, false, 'bodyValidator', 'genericRequest')
    );

    authResource.addResource('refresh').addMethod('POST',
      this.createLambdaIntegration('authRefresh', allFunctions),
      this.createMethodOptions(authorizer, validators, models, false, 'bodyValidator', 'genericRequest')
    );

    // Protected auth endpoints (require authorization)
    authResource.addResource('validate').addMethod('GET',
      this.createLambdaIntegration('authValidate', allFunctions),
      this.createMethodOptions(authorizer, validators, models, true)
    );

    authResource.addResource('set-username').addMethod('POST',
      this.createLambdaIntegration('authSetUsername', allFunctions),
      this.createMethodOptions(authorizer, validators, models, true, 'bodyValidator', 'genericRequest')
    );

    // Posts endpoints (all protected)
    const postsResource = api.root.addResource('posts');
    postsResource.addMethod('GET',
      this.createLambdaIntegration('posts', allFunctions),
      this.createMethodOptions(authorizer, validators, models, true)
    );
    postsResource.addMethod('POST',
      this.createLambdaIntegration('posts', allFunctions),
      this.createMethodOptions(authorizer, validators, models, true, 'bodyValidator', 'genericRequest')
    );

    // Posts draft endpoint
    const postsDraftResource = postsResource.addResource('draft');
    postsDraftResource.addMethod('POST',
      this.createLambdaIntegration('posts', allFunctions),
      this.createMethodOptions(authorizer, validators, models, true, 'bodyValidator', 'genericRequest')
    );

    // Posts followed endpoint
    const postsFollowedResource = postsResource.addResource('followed');
    postsFollowedResource.addMethod('GET',
      this.createLambdaIntegration('posts', allFunctions),
      this.createMethodOptions(authorizer, validators, models, true)
    );

    const postResource = postsResource.addResource('{id}');
    postResource.addMethod('GET',
      this.createLambdaIntegration('posts', allFunctions),
      this.createMethodOptions(authorizer, validators, models, true)
    );

    // Post media attachment endpoint - THIS IS THE KEY ONE WE'RE FIXING
    const postMediaResource = postResource.addResource('media');
    postMediaResource.addMethod('PUT',
      this.createLambdaIntegration('posts', allFunctions),
      this.createMethodOptions(authorizer, validators, models, true, 'bodyValidator', 'genericRequest')
    );

    // Post publish endpoint
    const postPublishResource = postResource.addResource('publish');
    postPublishResource.addMethod('PUT',
      this.createLambdaIntegration('posts', allFunctions),
      this.createMethodOptions(authorizer, validators, models, true, 'bodyValidator', 'genericRequest')
    );

    // Post like endpoints
    const postLikeResource = postResource.addResource('like');
    postLikeResource.addMethod('POST',
      this.createLambdaIntegration('posts', allFunctions),
      this.createMethodOptions(authorizer, validators, models, true, 'bodyValidator', 'genericRequest')
    );
    postLikeResource.addMethod('DELETE',
      this.createLambdaIntegration('posts', allFunctions),
      this.createMethodOptions(authorizer, validators, models, true)
    );

    // Post reactions endpoints
    const postReactionsResource = postResource.addResource('reactions');
    postReactionsResource.addMethod('POST',
      this.createLambdaIntegration('posts', allFunctions),
      this.createMethodOptions(authorizer, validators, models, true, 'bodyValidator', 'genericRequest')
    );
    postReactionsResource.addMethod('DELETE',
      this.createLambdaIntegration('posts', allFunctions),
      this.createMethodOptions(authorizer, validators, models, true)
    );

    // Post comments endpoints
    const postCommentsResource = postResource.addResource('comments');
    postCommentsResource.addMethod('GET',
      this.createLambdaIntegration('posts', allFunctions),
      this.createMethodOptions(authorizer, validators, models, true)
    );
    postCommentsResource.addMethod('POST',
      this.createLambdaIntegration('posts', allFunctions),
      this.createMethodOptions(authorizer, validators, models, true, 'bodyValidator', 'genericRequest')
    );

    // Media endpoints (all protected)
    const mediaResource = api.root.addResource('media');

    // Media upload endpoint
    const mediaUploadResource = mediaResource.addResource('upload');
    mediaUploadResource.addMethod('POST',
      this.createLambdaIntegration('media', allFunctions),
      this.createMethodOptions(authorizer, validators, models, true, 'bodyValidator', 'genericRequest')
    );

    // Media item endpoints
    const mediaItemResource = mediaResource.addResource('{id}');
    mediaItemResource.addMethod('GET',
      this.createLambdaIntegration('media', allFunctions),
      this.createMethodOptions(authorizer, validators, models, true)
    );

    mediaItemResource.addMethod('PUT',
      this.createLambdaIntegration('media', allFunctions),
      this.createMethodOptions(authorizer, validators, models, true, 'bodyValidator', 'genericRequest')
    );

    mediaItemResource.addMethod('DELETE',
      this.createLambdaIntegration('media', allFunctions),
      this.createMethodOptions(authorizer, validators, models, true)
    );

    // Media processing endpoints
    const mediaProcessResource = mediaItemResource.addResource('process');
    mediaProcessResource.addMethod('POST',
      this.createLambdaIntegration('media', allFunctions),
      this.createMethodOptions(authorizer, validators, models, true, 'bodyValidator', 'genericRequest')
    );

    // Media analysis endpoints
    const mediaAnalysisResource = mediaItemResource.addResource('analysis');
    mediaAnalysisResource.addMethod('GET',
      this.createLambdaIntegration('media', allFunctions),
      this.createMethodOptions(authorizer, validators, models, true)
    );

    // Media review endpoints
    const mediaReviewResource = mediaItemResource.addResource('review');
    mediaReviewResource.addMethod('GET',
      this.createLambdaIntegration('media', allFunctions),
      this.createMethodOptions(authorizer, validators, models, true)
    );

    // Media approval endpoints
    const mediaApproveResource = mediaItemResource.addResource('approve');
    mediaApproveResource.addMethod('POST',
      this.createLambdaIntegration('media', allFunctions),
      this.createMethodOptions(authorizer, validators, models, true, 'bodyValidator', 'genericRequest')
    );

    // Users endpoints (all protected)
    const usersResource = api.root.addResource('users');
    usersResource.addMethod('GET',
      this.createLambdaIntegration('users', allFunctions),
      this.createMethodOptions(authorizer, validators, models, true)
    );

    const userResource = usersResource.addResource('{id}');
    userResource.addMethod('GET',
      this.createLambdaIntegration('users', allFunctions),
      this.createMethodOptions(authorizer, validators, models, true)
    );

    // Channels endpoints (all protected)
    const channelsResource = api.root.addResource('channels');
    channelsResource.addMethod('GET',
      this.createLambdaIntegration('channels', allFunctions),
      this.createMethodOptions(authorizer, validators, models, true)
    );
    channelsResource.addMethod('POST',
      this.createLambdaIntegration('channels', allFunctions),
      this.createMethodOptions(authorizer, validators, models, true, 'bodyValidator', 'genericRequest')
    );

    // Reflexes endpoints (all protected)
    const reflexesResource = api.root.addResource('reflexes');
    reflexesResource.addMethod('GET',
      this.createLambdaIntegration('reflexes', allFunctions),
      this.createMethodOptions(authorizer, validators, models, true)
    );
    reflexesResource.addMethod('POST',
      this.createLambdaIntegration('reflexes', allFunctions),
      this.createMethodOptions(authorizer, validators, models, true, 'bodyValidator', 'genericRequest')
    );

    // Xbox endpoints
    const xboxResource = api.root.addResource('xbox');
    xboxResource.addMethod('GET', new apigateway.MockIntegration({
      integrationResponses: [{ statusCode: '200' }],
      passthroughBehavior: apigateway.PassthroughBehavior.NEVER,
      requestTemplates: { 'application/json': '{"statusCode": 200}' },
    }), {
      authorizer: authorizer,
    });
    xboxResource.addMethod('POST', new apigateway.MockIntegration({
      integrationResponses: [{ statusCode: '200' }],
      passthroughBehavior: apigateway.PassthroughBehavior.NEVER,
      requestTemplates: { 'application/json': '{"statusCode": 200}' },
    }), {
      authorizer: authorizer,
    });

    const xboxMediaResource = xboxResource.addResource('media');
    xboxMediaResource.addMethod('GET', new apigateway.MockIntegration({
      integrationResponses: [{ statusCode: '200' }],
      passthroughBehavior: apigateway.PassthroughBehavior.NEVER,
      requestTemplates: { 'application/json': '{"statusCode": 200}' },
    }), {
      authorizer: authorizer,
    });
  }

  private setupCustomDomain(
    domainName: string,
    environment: string
  ): void {
    // Import the existing custom domain instead of creating a new one
    const domain = apigateway.DomainName.fromDomainNameAttributes(this, 'CustomDomain', {
      domainName: domainName,
      domainNameAliasHostedZoneId: 'Z2FDTNDATAQYW2', // CloudFront hosted zone ID for edge-optimized domains
      domainNameAliasTarget: environment === 'development' ? 'd244o0xtb4tdqw.cloudfront.net' : 'd2q10y6jcz5xpl.cloudfront.net',
    });

    // Create base path mapping to link the domain to this API
    new apigateway.BasePathMapping(this, 'BasePathMapping', {
      domainName: domain,
      restApi: this.api,
      stage: this.api.deploymentStage,
    });
  }





  private createOutputs(): void {
    new cdk.CfnOutput(this, 'ApiUrl', {
      value: this.api.url,
      description: 'API Gateway URL',
      exportName: `${this.stackName}-ApiUrl`,
    });

    new cdk.CfnOutput(this, 'ApiId', {
      value: this.api.restApiId,
      description: 'API Gateway ID',
      exportName: `${this.stackName}-ApiId`,
    });
  }


}
