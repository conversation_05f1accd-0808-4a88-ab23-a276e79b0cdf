import * as cdk from 'aws-cdk-lib';
import * as lambda from 'aws-cdk-lib/aws-lambda';
import * as cognito from 'aws-cdk-lib/aws-cognito';
import * as dynamodb from 'aws-cdk-lib/aws-dynamodb';
import * as secretsmanager from 'aws-cdk-lib/aws-secretsmanager';
import * as s3 from 'aws-cdk-lib/aws-s3';
import * as cloudfront from 'aws-cdk-lib/aws-cloudfront';
import { Construct } from 'constructs';

export interface ApiFunctionsStackProps extends cdk.NestedStackProps {
  projectName: string;
  environment: string;
  userPool: cognito.UserPool;
  userPoolClient: cognito.UserPoolClient;
  tables: { [key: string]: dynamodb.Table };
  mediaBucket: s3.Bucket;
  distribution?: cloudfront.Distribution;
  appConfigSecret: secretsmanager.ISecret;
  xboxConfigSecret: secretsmanager.ISecret;
  mediaDomainName?: string;
}

export interface ApiFunctionsStackOutputs {
  functions: { [key: string]: lambda.Function };
}

export class ApiFunctionsStack extends cdk.NestedStack implements ApiFunctionsStackOutputs {
  public readonly functions: { [key: string]: lambda.Function };

  constructor(scope: Construct, id: string, props: ApiFunctionsStackProps) {
    super(scope, id, props);

    const {
      projectName,
      environment,
      userPool,
      userPoolClient,
      tables,
      mediaBucket,
      distribution,
      appConfigSecret,
      xboxConfigSecret,
      mediaDomainName
    } = props;

    this.functions = this.createLambdaFunctions(
      projectName,
      environment,
      userPool,
      userPoolClient,
      tables,
      mediaBucket,
      distribution,
      appConfigSecret,
      xboxConfigSecret,
      mediaDomainName
    );

    // Create outputs for Lambda function ARNs
    this.createFunctionOutputs();
  }

  private createLambdaFunctions(
    projectName: string,
    environment: string,
    userPool: cognito.UserPool,
    userPoolClient: cognito.UserPoolClient,
    tables: { [key: string]: dynamodb.Table },
    mediaBucket: s3.Bucket,
    distribution: cloudfront.Distribution | undefined,
    appConfigSecret: secretsmanager.ISecret,
    xboxConfigSecret: secretsmanager.ISecret,
    mediaDomainName?: string
  ): { [key: string]: lambda.Function } {
    const functions: { [key: string]: lambda.Function } = {};

    // Common environment variables for all Lambda functions
    const commonEnvironment = {
      ENVIRONMENT: environment,
      PROJECT_NAME: projectName,
      USER_POOL_ID: userPool.userPoolId,
      USER_POOL_CLIENT_ID: userPoolClient.userPoolClientId,
      POSTS_TABLE: tables.posts.tableName,
      MEDIA_TABLE: tables.media.tableName,
      USER_PROFILES_TABLE: tables.userProfiles.tableName,
      COMMENTS_TABLE: tables.comments.tableName,
      LIKES_TABLE: tables.likes.tableName,
      FOLLOWS_TABLE: tables.follows.tableName,
      CHANNELS_TABLE: tables.channels.tableName,
      CHANNEL_MEMBERS_TABLE: tables.channelMembers.tableName,
      REFLEXES_TABLE: tables.reflexes.tableName,
      REFLEX_LIKES_TABLE: tables.reflexLikes.tableName,
      POST_REACTIONS_TABLE: tables.postReactions.tableName,
      REFLEX_REACTIONS_TABLE: tables.reflexReactions.tableName,
      USERS_TABLE: tables.users.tableName,
      APP_CONFIG_SECRET_NAME: appConfigSecret.secretName,
      XBOX_CONFIG_SECRET_NAME: xboxConfigSecret.secretName,
    };

    // Posts Function
    functions.posts = new lambda.Function(this, 'PostsFunction', {
      functionName: `${projectName}-posts-${environment}`,
      runtime: lambda.Runtime.NODEJS_22_X,
      handler: 'index.handler',
      code: lambda.Code.fromAsset('src/posts'),
      timeout: cdk.Duration.seconds(30),
      memorySize: 512, // Increased memory for better performance
      environment: commonEnvironment,
    });

    // Grant permissions to posts function
    tables.posts.grantReadWriteData(functions.posts);
    tables.media.grantReadWriteData(functions.posts);
    tables.comments.grantReadWriteData(functions.posts);
    tables.likes.grantReadWriteData(functions.posts);
    tables.reflexes.grantReadWriteData(functions.posts);
    tables.users.grantReadWriteData(functions.posts);
    tables.postReactions.grantReadWriteData(functions.posts);
    tables.reflexReactions.grantReadWriteData(functions.posts);
    tables.channels.grantReadData(functions.posts);
    tables.follows.grantReadData(functions.posts);
    tables.channelMembers.grantReadData(functions.posts);
    tables.userProfiles.grantReadData(functions.posts);

    // Media Function
    functions.media = new lambda.Function(this, 'MediaFunction', {
      functionName: `${projectName}-media-${environment}`,
      runtime: lambda.Runtime.NODEJS_22_X,
      handler: 'index.handler',
      code: lambda.Code.fromAsset('src/media'),
      timeout: cdk.Duration.seconds(30),
      memorySize: 256,
      environment: {
        ...commonEnvironment,
        MEDIA_BUCKET: mediaBucket.bucketName,
        CLOUDFRONT_DOMAIN: mediaDomainName || distribution?.distributionDomainName || 'localhost',
        USER_POOL_ID: userPool.userPoolId,
        USER_POOL_CLIENT_ID: userPoolClient.userPoolClientId,
      },
    });

    // Grant permissions to media function
    tables.media.grantReadWriteData(functions.media);
    tables.users.grantReadWriteData(functions.media);
    tables.posts.grantReadWriteData(functions.media);
    mediaBucket.grantReadWrite(functions.media);
    appConfigSecret.grantRead(functions.media);

    // Users Function
    functions.users = new lambda.Function(this, 'UsersFunction', {
      functionName: `${projectName}-users-${environment}`,
      runtime: lambda.Runtime.NODEJS_22_X,
      handler: 'index.handler',
      code: lambda.Code.fromAsset('src/users'),
      timeout: cdk.Duration.seconds(30),
      memorySize: 256,
      environment: {
        ...commonEnvironment,
        XBOX_ACCOUNTS_TABLE: tables.xboxAccounts.tableName,
      },
    });

    // Grant permissions to users function
    tables.users.grantReadWriteData(functions.users);
    tables.userProfiles.grantReadWriteData(functions.users);
    tables.follows.grantReadWriteData(functions.users);
    tables.posts.grantReadWriteData(functions.users);
    tables.likes.grantReadWriteData(functions.users);
    tables.xboxAccounts.grantReadData(functions.users);

    return functions;
  }

  private createFunctionOutputs(): void {
    // Export function ARNs for use by other stacks
    Object.entries(this.functions).forEach(([name, func]) => {
      new cdk.CfnOutput(this, `${name}FunctionArn`, {
        value: func.functionArn,
        description: `${name} Lambda Function ARN`,
        exportName: `${this.stackName}-${name}FunctionArn`,
      });

      new cdk.CfnOutput(this, `${name}FunctionName`, {
        value: func.functionName,
        description: `${name} Lambda Function Name`,
        exportName: `${this.stackName}-${name}FunctionName`,
      });
    });
  }
}
