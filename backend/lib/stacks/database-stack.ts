import * as cdk from 'aws-cdk-lib';
import * as dynamodb from 'aws-cdk-lib/aws-dynamodb';
import { Construct } from 'constructs';

export interface DatabaseStackProps extends cdk.NestedStackProps {
  projectName: string;
  environment: string;
  isProductionOrStaging: boolean;
}

export interface DatabaseStackOutputs {
  tables: { [key: string]: dynamodb.Table };
}

export class DatabaseStack extends cdk.NestedStack implements DatabaseStackOutputs {
  public readonly tables: { [key: string]: dynamodb.Table };

  constructor(scope: Construct, id: string, props: DatabaseStackProps) {
    super(scope, id, props);

    const { projectName, environment, isProductionOrStaging } = props;

    this.tables = this.createDynamoDBTables(projectName, environment, isProductionOrStaging);

    // Create outputs for all table names and ARNs
    this.createTableOutputs();
  }

  private createDynamoDBTables(
    projectName: string,
    environment: string,
    isProductionOrStaging: boolean
  ): { [key: string]: dynamodb.Table } {
    const tableConfig = {
      billingMode: dynamodb.BillingMode.PAY_PER_REQUEST,
      deletionProtection: isProductionOrStaging,
      pointInTimeRecoverySpecification: {
        pointInTimeRecoveryEnabled: isProductionOrStaging,
      },
      removalPolicy: isProductionOrStaging ? cdk.RemovalPolicy.RETAIN : cdk.RemovalPolicy.DESTROY,
    };

    const tables: { [key: string]: dynamodb.Table } = {};

    // Posts Table
    tables.posts = new dynamodb.Table(this, 'PostsTable', {
      ...tableConfig,
      tableName: `${projectName}-${environment}-Posts`,
      partitionKey: { name: 'id', type: dynamodb.AttributeType.STRING },
    });

    // TODO: Add GSIs for better performance in future deployments
    // Note: DynamoDB only allows one GSI change per deployment

    // Media Table
    tables.media = new dynamodb.Table(this, 'MediaTable', {
      ...tableConfig,
      tableName: `${projectName}-${environment}-Media`,
      partitionKey: { name: 'id', type: dynamodb.AttributeType.STRING },
    });

    // User Profiles Table
    tables.userProfiles = new dynamodb.Table(this, 'UserProfilesTable', {
      ...tableConfig,
      tableName: `${projectName}-${environment}-UserProfiles`,
      partitionKey: { name: 'userId', type: dynamodb.AttributeType.STRING },
    });

    // Comments Table
    tables.comments = new dynamodb.Table(this, 'CommentsTable', {
      ...tableConfig,
      tableName: `${projectName}-${environment}-Comments`,
      partitionKey: { name: 'id', type: dynamodb.AttributeType.STRING },
    });
    tables.comments.addGlobalSecondaryIndex({
      indexName: 'postId-index',
      partitionKey: { name: 'postId', type: dynamodb.AttributeType.STRING },
    });

    // Likes Table
    tables.likes = new dynamodb.Table(this, 'LikesTable', {
      ...tableConfig,
      tableName: `${projectName}-${environment}-Likes`,
      partitionKey: { name: 'postId', type: dynamodb.AttributeType.STRING },
      sortKey: { name: 'userId', type: dynamodb.AttributeType.STRING },
    });
    tables.likes.addGlobalSecondaryIndex({
      indexName: 'userId-index',
      partitionKey: { name: 'userId', type: dynamodb.AttributeType.STRING },
    });

    // Follows Table
    tables.follows = new dynamodb.Table(this, 'FollowsTable', {
      ...tableConfig,
      tableName: `${projectName}-${environment}-Follows`,
      partitionKey: { name: 'followerId', type: dynamodb.AttributeType.STRING },
      sortKey: { name: 'followingId', type: dynamodb.AttributeType.STRING },
    });

    // Channels Table
    tables.channels = new dynamodb.Table(this, 'ChannelsTable', {
      ...tableConfig,
      tableName: `${projectName}-${environment}-Channels`,
      partitionKey: { name: 'id', type: dynamodb.AttributeType.STRING },
    });
    tables.channels.addGlobalSecondaryIndex({
      indexName: 'ownerId-index',
      partitionKey: { name: 'ownerId', type: dynamodb.AttributeType.STRING },
    });

    // Channel Members Table
    tables.channelMembers = new dynamodb.Table(this, 'ChannelMembersTable', {
      ...tableConfig,
      tableName: `${projectName}-${environment}-ChannelMembers`,
      partitionKey: { name: 'channelId', type: dynamodb.AttributeType.STRING },
      sortKey: { name: 'userId', type: dynamodb.AttributeType.STRING },
    });
    tables.channelMembers.addGlobalSecondaryIndex({
      indexName: 'userId-index',
      partitionKey: { name: 'userId', type: dynamodb.AttributeType.STRING },
    });

    // Reflexes Table
    tables.reflexes = new dynamodb.Table(this, 'ReflexesTable', {
      ...tableConfig,
      tableName: `${projectName}-${environment}-Reflexes`,
      partitionKey: { name: 'id', type: dynamodb.AttributeType.STRING },
    });
    tables.reflexes.addGlobalSecondaryIndex({
      indexName: 'postId-index',
      partitionKey: { name: 'postId', type: dynamodb.AttributeType.STRING },
    });
    tables.reflexes.addGlobalSecondaryIndex({
      indexName: 'userId-index',
      partitionKey: { name: 'userId', type: dynamodb.AttributeType.STRING },
    });

    // Reflex Likes Table
    tables.reflexLikes = new dynamodb.Table(this, 'ReflexLikesTable', {
      ...tableConfig,
      tableName: `${projectName}-${environment}-ReflexLikes`,
      partitionKey: { name: 'reflexId', type: dynamodb.AttributeType.STRING },
      sortKey: { name: 'userId', type: dynamodb.AttributeType.STRING },
    });
    tables.reflexLikes.addGlobalSecondaryIndex({
      indexName: 'userId-index',
      partitionKey: { name: 'userId', type: dynamodb.AttributeType.STRING },
    });

    // Post Reactions Table
    tables.postReactions = new dynamodb.Table(this, 'PostReactionsTable', {
      ...tableConfig,
      tableName: `${projectName}-${environment}-PostReactions`,
      partitionKey: { name: 'postId', type: dynamodb.AttributeType.STRING },
      sortKey: { name: 'reactionKey', type: dynamodb.AttributeType.STRING }, // format: "emoji#userId"
    });
    tables.postReactions.addGlobalSecondaryIndex({
      indexName: 'userId-index',
      partitionKey: { name: 'userId', type: dynamodb.AttributeType.STRING },
    });

    // Reflex Reactions Table
    tables.reflexReactions = new dynamodb.Table(this, 'ReflexReactionsTable', {
      ...tableConfig,
      tableName: `${projectName}-${environment}-ReflexReactions`,
      partitionKey: { name: 'reflexId', type: dynamodb.AttributeType.STRING },
      sortKey: { name: 'reactionKey', type: dynamodb.AttributeType.STRING }, // format: "emoji#userId"
    });
    tables.reflexReactions.addGlobalSecondaryIndex({
      indexName: 'userId-index',
      partitionKey: { name: 'userId', type: dynamodb.AttributeType.STRING },
    });

    // Users Table
    tables.users = new dynamodb.Table(this, 'UsersTable', {
      ...tableConfig,
      tableName: `${projectName}-${environment}-Users`,
      partitionKey: { name: 'id', type: dynamodb.AttributeType.STRING },
    });
    tables.users.addGlobalSecondaryIndex({
      indexName: 'EmailIndex',
      partitionKey: { name: 'email', type: dynamodb.AttributeType.STRING },
    });
    tables.users.addGlobalSecondaryIndex({
      indexName: 'UsernameIndex',
      partitionKey: { name: 'username', type: dynamodb.AttributeType.STRING },
    });
    tables.users.addGlobalSecondaryIndex({
      indexName: 'CognitoUserIdIndex',
      partitionKey: { name: 'cognitoUserId', type: dynamodb.AttributeType.STRING },
    });
    tables.users.addGlobalSecondaryIndex({
      indexName: 'XboxUserIdIndex',
      partitionKey: { name: 'xboxUserId', type: dynamodb.AttributeType.STRING },
    });

    // Xbox Accounts Table
    tables.xboxAccounts = new dynamodb.Table(this, 'XboxAccountsTable', {
      ...tableConfig,
      tableName: `${projectName}-${environment}-XboxAccounts`,
      partitionKey: { name: 'id', type: dynamodb.AttributeType.STRING },
    });
    tables.xboxAccounts.addGlobalSecondaryIndex({
      indexName: 'UserIdIndex',
      partitionKey: { name: 'userId', type: dynamodb.AttributeType.STRING },
    });
    tables.xboxAccounts.addGlobalSecondaryIndex({
      indexName: 'XboxUserIdIndex',
      partitionKey: { name: 'xboxUserId', type: dynamodb.AttributeType.STRING },
    });

    return tables;
  }

  private createTableOutputs(): void {
    // Export table names and ARNs for use by other stacks
    Object.entries(this.tables).forEach(([name, table]) => {
      new cdk.CfnOutput(this, `${name}TableName`, {
        value: table.tableName,
        description: `${name} DynamoDB Table Name`,
        exportName: `${this.stackName}-${name}TableName`,
      });

      new cdk.CfnOutput(this, `${name}TableArn`, {
        value: table.tableArn,
        description: `${name} DynamoDB Table ARN`,
        exportName: `${this.stackName}-${name}TableArn`,
      });
    });
  }
}
