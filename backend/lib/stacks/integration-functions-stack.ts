import * as cdk from 'aws-cdk-lib';
import * as lambda from 'aws-cdk-lib/aws-lambda';
import * as cognito from 'aws-cdk-lib/aws-cognito';
import * as dynamodb from 'aws-cdk-lib/aws-dynamodb';
import * as secretsmanager from 'aws-cdk-lib/aws-secretsmanager';
import { Construct } from 'constructs';

export interface IntegrationFunctionsStackProps extends cdk.NestedStackProps {
  projectName: string;
  environment: string;
  userPool: cognito.UserPool;
  userPoolClient: cognito.UserPoolClient;
  tables: { [key: string]: dynamodb.Table };
  appConfigSecret: secretsmanager.ISecret;
  xboxConfigSecret: secretsmanager.ISecret;
}

export interface IntegrationFunctionsStackOutputs {
  functions: { [key: string]: lambda.Function };
}

export class IntegrationFunctionsStack extends cdk.NestedStack implements IntegrationFunctionsStackOutputs {
  public readonly functions: { [key: string]: lambda.Function };

  constructor(scope: Construct, id: string, props: IntegrationFunctionsStackProps) {
    super(scope, id, props);

    const {
      projectName,
      environment,
      userPool,
      userPoolClient,
      tables,
      appConfigSecret,
      xboxConfigSecret
    } = props;

    this.functions = this.createIntegrationFunctions(
      projectName,
      environment,
      userPool,
      userPoolClient,
      tables,
      appConfigSecret,
      xboxConfigSecret
    );

    // Create outputs for Lambda function ARNs
    this.createFunctionOutputs();
  }

  private createIntegrationFunctions(
    projectName: string,
    environment: string,
    userPool: cognito.UserPool,
    userPoolClient: cognito.UserPoolClient,
    tables: { [key: string]: dynamodb.Table },
    appConfigSecret: secretsmanager.ISecret,
    xboxConfigSecret: secretsmanager.ISecret
  ): { [key: string]: lambda.Function } {
    const functions: { [key: string]: lambda.Function } = {};

    // Common environment variables for all Lambda functions
    const commonEnvironment = {
      ENVIRONMENT: environment,
      PROJECT_NAME: projectName,
      USER_POOL_ID: userPool.userPoolId,
      USER_POOL_CLIENT_ID: userPoolClient.userPoolClientId,
      POSTS_TABLE: tables.posts.tableName,
      MEDIA_TABLE: tables.media.tableName,
      USER_PROFILES_TABLE: tables.userProfiles.tableName,
      COMMENTS_TABLE: tables.comments.tableName,
      LIKES_TABLE: tables.likes.tableName,
      FOLLOWS_TABLE: tables.follows.tableName,
      CHANNELS_TABLE: tables.channels.tableName,
      CHANNEL_MEMBERS_TABLE: tables.channelMembers.tableName,
      REFLEXES_TABLE: tables.reflexes.tableName,
      REFLEX_LIKES_TABLE: tables.reflexLikes.tableName,
      POST_REACTIONS_TABLE: tables.postReactions.tableName,
      REFLEX_REACTIONS_TABLE: tables.reflexReactions.tableName,
      USERS_TABLE: tables.users.tableName,
      APP_CONFIG_SECRET_NAME: appConfigSecret.secretName,
      XBOX_CONFIG_SECRET_NAME: xboxConfigSecret.secretName,
    };

    // Reflexes Function
    functions.reflexes = new lambda.Function(this, 'ReflexesFunction', {
      functionName: `${projectName}-reflexes-${environment}`,
      runtime: lambda.Runtime.NODEJS_20_X,
      handler: 'index.handler',
      code: lambda.Code.fromAsset('src/reflexes'),
      timeout: cdk.Duration.seconds(30),
      memorySize: 256,
      environment: commonEnvironment,
    });

    // Grant permissions to reflexes function
    tables.reflexes.grantReadWriteData(functions.reflexes);
    tables.reflexLikes.grantReadWriteData(functions.reflexes);
    tables.posts.grantReadWriteData(functions.reflexes);
    tables.users.grantReadWriteData(functions.reflexes);
    tables.media.grantReadWriteData(functions.reflexes);
    tables.postReactions.grantReadWriteData(functions.reflexes);
    tables.reflexReactions.grantReadWriteData(functions.reflexes);

    // Channels Function
    functions.channels = new lambda.Function(this, 'ChannelsFunction', {
      functionName: `${projectName}-channels-${environment}`,
      runtime: lambda.Runtime.NODEJS_20_X,
      handler: 'index.handler',
      code: lambda.Code.fromAsset('src/channels'),
      timeout: cdk.Duration.seconds(30),
      memorySize: 256,
      environment: commonEnvironment,
    });

    // Grant permissions to channels function
    tables.channels.grantReadWriteData(functions.channels);
    tables.channelMembers.grantReadWriteData(functions.channels);
    tables.users.grantReadWriteData(functions.channels);
    tables.posts.grantReadWriteData(functions.channels);
    tables.media.grantReadWriteData(functions.channels);
    tables.likes.grantReadData(functions.channels);
    appConfigSecret.grantRead(functions.channels);

    // Xbox Function
    functions.xbox = new lambda.Function(this, 'XboxFunction', {
      functionName: `${projectName}-xbox-${environment}`,
      runtime: lambda.Runtime.NODEJS_20_X,
      handler: 'index.handler',
      code: lambda.Code.fromAsset('src/xbox'),
      timeout: cdk.Duration.seconds(30),
      memorySize: 256,
      environment: {
        ...commonEnvironment,
        XBOX_ACCOUNTS_TABLE: tables.xboxAccounts.tableName,
      },
    });

    // Grant permissions to xbox function
    tables.xboxAccounts.grantReadWriteData(functions.xbox);
    tables.users.grantReadWriteData(functions.xbox);
    appConfigSecret.grantRead(functions.xbox);
    xboxConfigSecret.grantRead(functions.xbox);
    userPool.grant(functions.xbox, 'cognito-idp:AdminCreateUser', 'cognito-idp:AdminSetUserPassword', 'cognito-idp:AdminInitiateAuth', 'cognito-idp:AdminGetUser');

    // Xbox Media Function
    functions.xboxMedia = new lambda.Function(this, 'XboxMediaFunction', {
      functionName: `${projectName}-xbox-media-${environment}`,
      runtime: lambda.Runtime.NODEJS_20_X,
      handler: 'media.handler',
      code: lambda.Code.fromAsset('src/xbox'),
      timeout: cdk.Duration.seconds(30),
      memorySize: 256,
      environment: {
        ...commonEnvironment,
        XBOX_ACCOUNTS_TABLE: tables.xboxAccounts.tableName,
      },
    });

    // Grant permissions to xbox media function
    tables.xboxAccounts.grantReadWriteData(functions.xboxMedia);
    tables.users.grantReadData(functions.xboxMedia);
    appConfigSecret.grantRead(functions.xboxMedia);
    xboxConfigSecret.grantRead(functions.xboxMedia);

    // Note: Cloudflare webhook function removed - AI processing now handled by S3 events

    return functions;
  }

  private createFunctionOutputs(): void {
    // Export function ARNs for use by other stacks
    Object.entries(this.functions).forEach(([name, func]) => {
      new cdk.CfnOutput(this, `${name}FunctionArn`, {
        value: func.functionArn,
        description: `${name} Lambda Function ARN`,
        exportName: `${this.stackName}-${name}FunctionArn`,
      });

      new cdk.CfnOutput(this, `${name}FunctionName`, {
        value: func.functionName,
        description: `${name} Lambda Function Name`,
        exportName: `${this.stackName}-${name}FunctionName`,
      });
    });
  }
}
