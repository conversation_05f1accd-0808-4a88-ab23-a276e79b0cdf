/**
 * Reusable validation utilities for AWS API Gateway
 * Provides consistent validation patterns and helper functions
 */

import * as apigateway from 'aws-cdk-lib/aws-apigateway';

// Standard HTTP status codes for API responses
export const HTTP_STATUS_CODES = {
  OK: '200',
  CREATED: '201',
  NO_CONTENT: '204',
  BAD_REQUEST: '400',
  UNAUTHORIZED: '401',
  FORBIDDEN: '403',
  NOT_FOUND: '404',
  CONFLICT: '409',
  INTERNAL_SERVER_ERROR: '500',
} as const;

// Standard CORS response parameters
export const CORS_RESPONSE_PARAMETERS = {
  'method.response.header.Access-Control-Allow-Origin': true,
  'method.response.header.Content-Type': true,
} as const;

// Extended CORS response parameters for preflight requests
export const CORS_PREFLIGHT_PARAMETERS = {
  ...CORS_RESPONSE_PARAMETERS,
  'method.response.header.Access-Control-Allow-Headers': true,
  'method.response.header.Access-Control-Allow-Methods': true,
} as const;

/**
 * Creates a standard method response configuration
 */
export function createMethodResponse(
  statusCode: string,
  responseModel?: apigateway.Model,
  additionalParameters?: Record<string, boolean>
): apigateway.MethodResponse {
  return {
    statusCode,
    responseModels: responseModel ? {
      'application/json': responseModel,
    } : {
      'application/json': apigateway.Model.EMPTY_MODEL,
    },
    responseParameters: {
      ...CORS_RESPONSE_PARAMETERS,
      ...additionalParameters,
    },
  };
}

/**
 * Creates standard method responses for auth endpoints
 */
export function createAuthMethodResponses(
  errorModel: apigateway.Model,
  successStatusCode: string = HTTP_STATUS_CODES.OK,
  successModel?: apigateway.Model
): apigateway.MethodResponse[] {
  return [
    createMethodResponse(successStatusCode, successModel),
    createMethodResponse(HTTP_STATUS_CODES.BAD_REQUEST, errorModel),
    createMethodResponse(HTTP_STATUS_CODES.UNAUTHORIZED, errorModel),
    createMethodResponse(HTTP_STATUS_CODES.INTERNAL_SERVER_ERROR, errorModel),
  ];
}

/**
 * Creates method responses for endpoints that can return 404
 */
export function createAuthMethodResponsesWithNotFound(
  errorModel: apigateway.Model,
  successStatusCode: string = HTTP_STATUS_CODES.OK,
  successModel?: apigateway.Model
): apigateway.MethodResponse[] {
  return [
    createMethodResponse(successStatusCode, successModel),
    createMethodResponse(HTTP_STATUS_CODES.BAD_REQUEST, errorModel),
    createMethodResponse(HTTP_STATUS_CODES.UNAUTHORIZED, errorModel),
    createMethodResponse(HTTP_STATUS_CODES.NOT_FOUND, errorModel),
    createMethodResponse(HTTP_STATUS_CODES.INTERNAL_SERVER_ERROR, errorModel),
  ];
}

/**
 * Standard request parameters for endpoints requiring authorization
 */
export const AUTHORIZATION_REQUEST_PARAMETERS = {
  'method.request.header.Authorization': true,
} as const;

/**
 * Standard request parameters for endpoints with optional query parameters
 */
export function createQueryParameters(paramNames: string[]): Record<string, boolean> {
  const params: Record<string, boolean> = {};
  paramNames.forEach(name => {
    params[`method.request.querystring.${name}`] = false; // false = optional
  });
  return params;
}

/**
 * Standard request parameters for endpoints with required query parameters
 */
export function createRequiredQueryParameters(paramNames: string[]): Record<string, boolean> {
  const params: Record<string, boolean> = {};
  paramNames.forEach(name => {
    params[`method.request.querystring.${name}`] = true; // true = required
  });
  return params;
}

/**
 * Standard request parameters for endpoints with path parameters
 */
export function createPathParameters(paramNames: string[]): Record<string, boolean> {
  const params: Record<string, boolean> = {};
  paramNames.forEach(name => {
    params[`method.request.path.${name}`] = true; // Path params are always required
  });
  return params;
}

/**
 * Creates a complete method options configuration for POST endpoints with body validation
 */
export function createPostMethodOptions(
  bodyValidator: apigateway.RequestValidator,
  requestModel: apigateway.Model,
  errorModel: apigateway.Model,
  successStatusCode: string = HTTP_STATUS_CODES.CREATED,
  successModel?: apigateway.Model,
  additionalParameters?: Record<string, boolean>
): apigateway.MethodOptions {
  return {
    requestValidator: bodyValidator,
    requestModels: {
      'application/json': requestModel,
    },
    requestParameters: additionalParameters,
    methodResponses: createAuthMethodResponses(errorModel, successStatusCode, successModel),
  };
}

/**
 * Creates a complete method options configuration for GET endpoints with parameter validation
 */
export function createGetMethodOptions(
  parameterValidator: apigateway.RequestValidator,
  errorModel: apigateway.Model,
  requestParameters?: Record<string, boolean>,
  includeNotFound: boolean = false,
  successModel?: apigateway.Model
): apigateway.MethodOptions {
  const responses = includeNotFound
    ? createAuthMethodResponsesWithNotFound(errorModel, HTTP_STATUS_CODES.OK, successModel)
    : createAuthMethodResponses(errorModel, HTTP_STATUS_CODES.OK, successModel);

  return {
    requestValidator: parameterValidator,
    requestParameters: requestParameters || {},
    methodResponses: responses,
  };
}

/**
 * Creates method options for endpoints requiring authorization
 */
export function createAuthorizedMethodOptions(
  authorizer: apigateway.IAuthorizer,
  validator?: apigateway.RequestValidator,
  requestModel?: apigateway.Model,
  errorModel?: apigateway.Model,
  additionalParameters?: Record<string, boolean>
): apigateway.MethodOptions {
  const options: apigateway.MethodOptions = {
    authorizer,
    requestParameters: {
      ...AUTHORIZATION_REQUEST_PARAMETERS,
      ...additionalParameters,
    },
  };

  if (validator) {
    return {
      ...options,
      requestValidator: validator,
      ...(requestModel && {
        requestModels: {
          'application/json': requestModel,
        },
      }),
      ...(errorModel && {
        methodResponses: createAuthMethodResponses(errorModel),
      }),
    };
  }

  return options;
}

/**
 * Validation patterns for common data types
 */
export const VALIDATION_PATTERNS = {
  EMAIL: '^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\\.[a-zA-Z]{2,}$',
  UUID: '^[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}$',
  USERNAME: '^[a-zA-Z0-9_-]+$',
  PASSWORD: '^(?=.*[a-z])(?=.*[A-Z])(?=.*\\d)(?=.*[@$!%*?&])[A-Za-z\\d@$!%*?&]',
  JWT_TOKEN: '^[A-Za-z0-9-_=]+\\.[A-Za-z0-9-_=]+\\.[A-Za-z0-9-_.+/=]*$',
  BEARER_TOKEN: '^Bearer [A-Za-z0-9-_=]+\\.[A-Za-z0-9-_=]+\\.[A-Za-z0-9-_.+/=]*$',
} as const;

/**
 * Common field length constraints
 */
export const FIELD_CONSTRAINTS = {
  PASSWORD_MIN_LENGTH: 8,
  PASSWORD_MAX_LENGTH: 128,
  USERNAME_MIN_LENGTH: 3,
  USERNAME_MAX_LENGTH: 30,
  NAME_MAX_LENGTH: 50,
  DESCRIPTION_MAX_LENGTH: 500,
  TITLE_MAX_LENGTH: 100,
} as const;

/**
 * Helper function to create a schema property with validation
 */
export function createSchemaProperty(
  type: string,
  description: string,
  options: {
    format?: string;
    pattern?: string;
    minLength?: number;
    maxLength?: number;
    minimum?: number;
    maximum?: number;
    example?: any;
    enum?: string[];
  } = {}
): any {
  return {
    type,
    description,
    ...options,
  };
}

/**
 * Creates a standard error response schema
 */
export function createErrorSchema(): any {
  return {
    type: 'object',
    required: ['error'],
    properties: {
      error: createSchemaProperty('string', 'Error message describing what went wrong'),
      details: createSchemaProperty('string', 'Additional error details for debugging'),
    },
    additionalProperties: false,
  };
}
