# GameFlex Backend Environment Variables
# This file contains secrets that will be automatically uploaded to AWS Secrets Manager
# Copy this file to .env and modify with your actual credentials

# AWS Administrator Credentials - FOR USER SETUP ONLY
# These credentials are used ONLY to create GitLab CI users and attach policies
# Individual users will handle their own CDK bootstrapping
AWS_ADMIN_ACCESS_KEY_ID=
AWS_ADMIN_SECRET_ACCESS_KEY=
AWS_ACCOUNT_ID=************
AWS_DEFAULT_REGION=us-west-2

# AWS Deployment Credentials by Environment
# Development Environment
AWS_ACCESS_KEY_ID_DEV=
AWS_SECRET_ACCESS_KEY_DEV=
AWS_REGION_DEV=us-west-2

# Staging Environment
AWS_ACCESS_KEY_ID_STAGING=
AWS_SECRET_ACCESS_KEY_STAGING=
AWS_REGION_STAGING=us-west-2

# Production Environment
AWS_ACCESS_KEY_ID_PROD=
AWS_SECRET_ACCESS_KEY_PROD=
AWS_REGION_PROD=us-east-1

# Application Configuration - DEVELOPMENT
# These will be stored in AWS Secrets Manager
TEST_USER_EMAIL=<EMAIL>
TEST_USER_PASSWORD=DevTest123!
DEBUG_MODE=development
ENVIRONMENT=development

# API Configuration - DEVELOPMENT (using custom domain without /v1)
API_URL_REMOTE=https://dev.api.gameflex.io
API_BASE_URL=https://dev.api.gameflex.io

# AWS Resources - STAGING (for tests)
USER_POOL_ID=us-west-2_Eoj9I6lEI
USER_POOL_CLIENT_ID=1muni71i0vo5opkfstefafo8vk
USERS_TABLE=gameflex-staging-Users
POSTS_TABLE=gameflex-staging-Posts
MEDIA_TABLE=gameflex-staging-Media
USER_PROFILES_TABLE=gameflex-staging-UserProfiles
COMMENTS_TABLE=gameflex-staging-Comments
LIKES_TABLE=gameflex-staging-Likes
FOLLOWS_TABLE=gameflex-staging-Follows
CHANNELS_TABLE=gameflex-staging-Channels
CHANNEL_MEMBERS_TABLE=gameflex-staging-ChannelMembers
REFLEXES_TABLE=gameflex-staging-Reflexes

# Sign in with Apple Configuration
# These are now stored in AWS Secrets Manager as 'gameflex-apple-config-{environment}'
# Use the ensure-secrets.sh script to create the secret, then populate with:
# aws secretsmanager put-secret-value --secret-id 'gameflex-apple-config-{environment}' --secret-string '{"teamId":"YOUR_APPLE_TEAM_ID","clientId":"YOUR_APPLE_CLIENT_ID","keyId":"YOUR_APPLE_KEY_ID","privateKey":"YOUR_APPLE_PRIVATE_KEY_CONTENT"}'
#
# Legacy environment variables (fallback only - not recommended for production):
# APPLE_TEAM_ID=YOUR_APPLE_TEAM_ID
# APPLE_CLIENT_ID=YOUR_APPLE_CLIENT_ID
# APPLE_KEY_ID=YOUR_APPLE_KEY_ID
# APPLE_PRIVATE_KEY=YOUR_APPLE_PRIVATE_KEY_CONTENT
