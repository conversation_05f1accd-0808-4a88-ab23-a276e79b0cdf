#!/bin/bash

# Script to clean up production DynamoDB tables
# This script disables deletion protection and deletes all production tables

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

print_status() {
    echo -e "${BLUE}ℹ️  $1${NC}"
}

print_success() {
    echo -e "${GREEN}✅ $1${NC}"
}

print_warning() {
    echo -e "${YELLOW}⚠️  $1${NC}"
}

print_error() {
    echo -e "${RED}❌ $1${NC}"
}

# Admin credentials (required for table deletion)
export AWS_ACCESS_KEY_ID=********************
export AWS_SECRET_ACCESS_KEY=An5pe0X0vdyPZXWIRbIYu/dcYdBd4o/BMGnp1yke
export AWS_DEFAULT_REGION=us-east-1

# List of production tables to delete
TABLES=(
    "gameflex-production-ChannelMembers"
    "gameflex-production-Channels"
    "gameflex-production-Comments"
    "gameflex-production-Follows"
    "gameflex-production-Likes"
    "gameflex-production-Media"
    "gameflex-production-Posts"
    "gameflex-production-Reflexes"
    "gameflex-production-UserProfiles"
    "gameflex-production-Users"
)

print_status "Starting cleanup of production DynamoDB tables..."

for table in "${TABLES[@]}"; do
    print_status "Processing table: $table"
    
    # Check if table exists
    if aws dynamodb describe-table --table-name "$table" --region us-east-1 &> /dev/null; then
        print_status "Table $table exists, disabling deletion protection..."
        
        # Disable deletion protection
        aws dynamodb update-table \
            --table-name "$table" \
            --no-deletion-protection-enabled \
            --region us-east-1
        
        print_status "Deletion protection disabled for $table"
        
        # Delete the table
        print_status "Deleting table $table..."
        aws dynamodb delete-table \
            --table-name "$table" \
            --region us-east-1
        
        print_success "Deletion initiated for $table"
    else
        print_warning "Table $table does not exist, skipping..."
    fi
done

print_success "All production table deletions have been initiated!"
print_status "Tables are being deleted in the background. This may take a few minutes."
print_status "You can check the status with: aws dynamodb list-tables --region us-east-1"
