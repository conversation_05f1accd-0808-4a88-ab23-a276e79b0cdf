import { S3Client, ListObjectsV2Command, GetObjectCommand, HeadObjectCommand } from '@aws-sdk/client-s3';
import { DynamoDBClient } from '@aws-sdk/client-dynamodb';
import { DynamoDBDocumentClient, GetCommand } from '@aws-sdk/lib-dynamodb';
import fetch from 'node-fetch';

// Configure AWS clients
const s3Client = new S3Client({ region: 'us-west-2' });
const dynamodbClient = new DynamoDBClient({ region: 'us-west-2' });
const dynamodb = DynamoDBDocumentClient.from(dynamodbClient);

// Configuration
const BUCKET_NAME = process.env.MEDIA_BUCKET || 'gameflex-media-development';
const MEDIA_TABLE = process.env.MEDIA_TABLE || 'gameflex-development-Media';
const API_BASE_URL = process.env.API_BASE_URL || 'https://dev.api.gameflex.io';
const MEDIA_DOMAIN = process.env.MEDIA_DOMAIN || 'd5e3lv3s19ias.cloudfront.net';

interface TestResult {
  test: string;
  status: 'PASS' | 'FAIL' | 'SKIP';
  message: string;
  details?: any;
}

// Test the staging workflow
async function testStagingWorkflow(): Promise<TestResult[]> {
  const results: TestResult[] = [];

  console.log('🧪 Testing Media Staging Workflow...\n');

  // Test 1: Verify staging area structure
  try {
    console.log('📋 Test 1: Verify S3 staging area structure');

    const stagingObjects = await s3Client.send(new ListObjectsV2Command({
      Bucket: BUCKET_NAME,
      Prefix: 'staging/',
      MaxKeys: 10,
    }));

    results.push({
      test: 'S3 Staging Area Structure',
      status: 'PASS',
      message: `Staging area accessible. Found ${stagingObjects.KeyCount || 0} objects.`,
      details: { keyCount: stagingObjects.KeyCount, keys: stagingObjects.Contents?.map(obj => obj.Key) }
    });
  } catch (error) {
    results.push({
      test: 'S3 Staging Area Structure',
      status: 'FAIL',
      message: `Failed to access staging area: ${(error as Error).message}`,
    });
  }

  // Test 2: Verify public area structure
  try {
    console.log('📋 Test 2: Verify S3 public area structure');

    const publicObjects = await s3Client.send(new ListObjectsV2Command({
      Bucket: BUCKET_NAME,
      Prefix: 'media/',
      MaxKeys: 10,
    }));

    results.push({
      test: 'S3 Public Area Structure',
      status: 'PASS',
      message: `Public area accessible. Found ${publicObjects.KeyCount || 0} approved objects.`,
      details: { keyCount: publicObjects.KeyCount, keys: publicObjects.Contents?.map(obj => obj.Key) }
    });
  } catch (error) {
    results.push({
      test: 'S3 Public Area Structure',
      status: 'FAIL',
      message: `Failed to access public area: ${(error as Error).message}`,
    });
  }

  // Test 3: Simulate upload workflow
  try {
    console.log('📋 Test 3: Simulate upload workflow');

    // This would normally require authentication
    // For testing, we'll just verify the API endpoint exists
    const uploadResponse = await fetch(`${API_BASE_URL}/media/upload`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        // 'Authorization': 'Bearer <token>' // Would need real auth token
      },
      body: JSON.stringify({
        fileName: 'test-image.jpg',
        fileType: 'image/jpeg',
        fileSize: 1024000,
        mediaType: 'media'
      })
    }).catch(() => null);

    if (uploadResponse && uploadResponse.status === 401) {
      results.push({
        test: 'Upload API Endpoint',
        status: 'PASS',
        message: 'Upload endpoint accessible (authentication required as expected)',
      });
    } else if (uploadResponse) {
      results.push({
        test: 'Upload API Endpoint',
        status: 'PASS',
        message: `Upload endpoint responded with status: ${uploadResponse.status}`,
      });
    } else {
      results.push({
        test: 'Upload API Endpoint',
        status: 'SKIP',
        message: 'Could not reach upload endpoint (network or configuration issue)',
      });
    }
  } catch (error) {
    results.push({
      test: 'Upload API Endpoint',
      status: 'FAIL',
      message: `Upload API test failed: ${(error as Error).message}`,
    });
  }

  // Test 4: Check for orphaned staging files
  try {
    console.log('📋 Test 4: Check for orphaned staging files');

    const stagingObjects = await s3Client.send(new ListObjectsV2Command({
      Bucket: BUCKET_NAME,
      Prefix: 'staging/',
    }));

    const now = new Date();
    const oneDayAgo = new Date(now.getTime() - 24 * 60 * 60 * 1000);

    const orphanedFiles = stagingObjects.Contents?.filter(obj =>
      obj.LastModified && obj.LastModified < oneDayAgo
    ) || [];

    if (orphanedFiles.length === 0) {
      results.push({
        test: 'Orphaned Staging Files',
        status: 'PASS',
        message: 'No orphaned files found in staging area (good cleanup)',
      });
    } else {
      results.push({
        test: 'Orphaned Staging Files',
        status: 'FAIL',
        message: `Found ${orphanedFiles.length} orphaned files older than 24 hours`,
        details: { orphanedFiles: orphanedFiles.map(f => ({ key: f.Key, lastModified: f.LastModified })) }
      });
    }
  } catch (error) {
    results.push({
      test: 'Orphaned Staging Files',
      status: 'FAIL',
      message: `Failed to check for orphaned files: ${(error as Error).message}`,
    });
  }

  // Test 5: Verify media records have correct staging fields
  try {
    console.log('📋 Test 5: Verify media record structure');

    // This is a sample test - in real scenario, you'd query for recent records
    const sampleMediaId = 'test-media-id'; // Replace with actual media ID

    const mediaRecord = await dynamodb.send(new GetCommand({
      TableName: MEDIA_TABLE,
      Key: { id: sampleMediaId }
    })).catch(() => null);

    if (!mediaRecord?.Item) {
      results.push({
        test: 'Media Record Structure',
        status: 'SKIP',
        message: 'No sample media record found to test structure',
      });
    } else {
      const record = mediaRecord.Item;
      const hasRequiredFields =
        'staging_s3_key' in record &&
        'final_s3_key' in record &&
        'final_url' in record;

      results.push({
        test: 'Media Record Structure',
        status: hasRequiredFields ? 'PASS' : 'FAIL',
        message: hasRequiredFields
          ? 'Media record has required staging workflow fields'
          : 'Media record missing staging workflow fields',
        details: {
          hasStaging: 'staging_s3_key' in record,
          hasFinal: 'final_s3_key' in record,
          hasFinalUrl: 'final_url' in record,
          status: record.status
        }
      });
    }
  } catch (error) {
    results.push({
      test: 'Media Record Structure',
      status: 'FAIL',
      message: `Failed to check media record structure: ${(error as Error).message}`,
    });
  }

  // Test 6: Verify CloudFront behavior (staging blocked)
  try {
    console.log('📋 Test 6: Verify CloudFront staging access is blocked');

    const cloudFrontDomain = process.env.CLOUDFRONT_DOMAIN;
    if (!cloudFrontDomain) {
      results.push({
        test: 'CloudFront Staging Block',
        status: 'SKIP',
        message: 'CloudFront domain not configured for testing',
      });
    } else {
      // Try to access a staging URL (should be blocked)
      const stagingUrl = `https://${cloudFrontDomain}/staging/test-file.jpg`;
      const response = await fetch(stagingUrl, { method: 'HEAD' }).catch(() => null);

      if (response && (response.status === 403 || response.status === 404)) {
        results.push({
          test: 'CloudFront Staging Block',
          status: 'PASS',
          message: `Staging access properly blocked (status: ${response.status})`,
        });
      } else {
        results.push({
          test: 'CloudFront Staging Block',
          status: 'FAIL',
          message: response
            ? `Staging access not blocked (status: ${response.status})`
            : 'Could not test staging access',
        });
      }
    }
  } catch (error) {
    results.push({
      test: 'CloudFront Staging Block',
      status: 'FAIL',
      message: `CloudFront test failed: ${(error as Error).message}`,
    });
  }

  return results;
}

// Display test results
function displayResults(results: TestResult[]): void {
  console.log('\n📊 Test Results Summary:\n');

  let passed = 0;
  let failed = 0;
  let skipped = 0;

  results.forEach(result => {
    const icon = result.status === 'PASS' ? '✅' : result.status === 'FAIL' ? '❌' : '⏭️';
    console.log(`${icon} ${result.test}: ${result.message}`);

    if (result.details) {
      console.log(`   Details: ${JSON.stringify(result.details, null, 2)}`);
    }

    if (result.status === 'PASS') passed++;
    else if (result.status === 'FAIL') failed++;
    else skipped++;
  });

  console.log(`\n📈 Summary: ${passed} passed, ${failed} failed, ${skipped} skipped`);

  if (failed === 0) {
    console.log('🎉 All tests passed! Staging workflow is properly configured.');
  } else {
    console.log('⚠️  Some tests failed. Please review the configuration.');
  }
}

// Run the tests
if (require.main === module) {
  testStagingWorkflow()
    .then(displayResults)
    .catch(error => {
      console.error('❌ Test execution failed:', error);
      process.exit(1);
    });
}

export { testStagingWorkflow };
