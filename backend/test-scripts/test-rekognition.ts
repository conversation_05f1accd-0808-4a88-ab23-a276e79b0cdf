import { RekognitionClient, DetectModerationLabelsCommand, DetectLabelsCommand } from '@aws-sdk/client-rekognition';
import { S3Client, GetObjectCommand } from '@aws-sdk/client-s3';
import * as fs from 'fs';
import * as path from 'path';

// Configure AWS clients
const rekognitionClient = new RekognitionClient({ region: 'us-west-2' });
const s3Client = new S3Client({ region: 'us-west-2' });

// Test image analysis with a sample image
async function testRekognitionAnalysis() {
  try {
    console.log('🔍 Testing AWS Rekognition Integration...\n');

    // You can test with a local image file or S3 object
    // For this example, we'll create a simple test with a sample image URL
    
    // Test 1: Content Moderation
    console.log('📋 Test 1: Content Moderation Analysis');
    console.log('Note: This test requires a sample image. You can:');
    console.log('1. Upload an image to your S3 bucket');
    console.log('2. Use a local image file');
    console.log('3. Use a sample image from AWS documentation\n');

    // Example with S3 object (uncomment and modify as needed)
    /*
    const moderationCommand = new DetectModerationLabelsCommand({
      Image: {
        S3Object: {
          Bucket: 'your-bucket-name',
          Name: 'path/to/your/image.jpg'
        }
      },
      MinConfidence: 50
    });

    const moderationResponse = await rekognitionClient.send(moderationCommand);
    console.log('Moderation Labels:', JSON.stringify(moderationResponse.ModerationLabels, null, 2));
    */

    // Test 2: Label Detection
    console.log('📋 Test 2: Label Detection Analysis');
    
    /*
    const labelsCommand = new DetectLabelsCommand({
      Image: {
        S3Object: {
          Bucket: 'your-bucket-name',
          Name: 'path/to/your/image.jpg'
        }
      },
      MaxLabels: 20,
      MinConfidence: 70,
      Features: ['GENERAL_LABELS', 'IMAGE_PROPERTIES'],
      Settings: {
        GeneralLabels: {
          LabelInclusionFilters: undefined,
          LabelExclusionFilters: undefined,
        },
        ImageProperties: {
          MaxDominantColors: 10,
        },
      },
    });

    const labelsResponse = await rekognitionClient.send(labelsCommand);
    console.log('Detected Labels:', JSON.stringify(labelsResponse.Labels, null, 2));
    console.log('Image Properties:', JSON.stringify(labelsResponse.ImageProperties, null, 2));
    */

    // Test 3: Simulate the processing pipeline
    console.log('📋 Test 3: Processing Pipeline Simulation');
    
    const sampleModerationResult = {
      isInappropriate: false,
      confidence: 95,
      moderationLabels: [
        {
          name: 'Safe Content',
          confidence: 95,
          parentName: 'General',
          categories: ['Safe']
        }
      ],
      summary: 'Content appears appropriate'
    };

    const sampleContentAnalysis = {
      labels: [
        {
          name: 'Gaming',
          confidence: 92,
          categories: ['Technology', 'Entertainment'],
          instances: [],
          parents: [{ name: 'Technology' }]
        },
        {
          name: 'Computer',
          confidence: 88,
          categories: ['Technology'],
          instances: [
            {
              boundingBox: { width: 0.5, height: 0.3, left: 0.2, top: 0.1 },
              confidence: 88
            }
          ],
          parents: [{ name: 'Technology' }]
        },
        {
          name: 'Screen',
          confidence: 85,
          categories: ['Technology'],
          instances: [],
          parents: [{ name: 'Electronics' }]
        }
      ],
      videoGameRelated: true,
      videoGameConfidence: 90,
      suggestedTags: ['gaming', 'computer', 'screen', 'technology', 'entertainment'],
      dominantColors: [
        {
          red: 45,
          green: 123,
          blue: 200,
          cssColor: '#2d7bc8',
          simplifiedColor: 'Blue',
          pixelPercent: 35.5
        },
        {
          red: 20,
          green: 20,
          blue: 25,
          cssColor: '#141419',
          simplifiedColor: 'Black',
          pixelPercent: 28.2
        }
      ],
      imageProperties: {
        quality: {
          brightness: 75.5,
          sharpness: 82.3,
          contrast: 68.7
        }
      }
    };

    console.log('✅ Sample Moderation Analysis:');
    console.log(JSON.stringify(sampleModerationResult, null, 2));
    
    console.log('\n✅ Sample Content Analysis:');
    console.log(JSON.stringify(sampleContentAnalysis, null, 2));

    // Test 4: Video Game Detection Logic
    console.log('\n📋 Test 4: Video Game Detection Logic');
    
    const videoGameKeywords = [
      'game', 'gaming', 'video game', 'console', 'controller', 'joystick', 'gamepad',
      'xbox', 'playstation', 'nintendo', 'pc gaming', 'steam', 'esports', 'gaming setup',
      'monitor', 'keyboard', 'mouse', 'headset', 'character', 'avatar', 'weapon',
      'armor', 'fantasy', 'sci-fi', 'medieval', 'futuristic', 'digital art',
      'screenshot', 'gameplay', 'streamer', 'twitch', 'youtube gaming'
    ];

    const testLabels = [
      { name: 'Gaming', confidence: 92, categories: ['Technology'] },
      { name: 'Computer', confidence: 88, categories: ['Technology'] },
      { name: 'Xbox Controller', confidence: 95, categories: ['Gaming', 'Technology'] },
      { name: 'Screen', confidence: 85, categories: ['Technology'] }
    ];

    const isGameRelated = testLabels.some(label => {
      const labelText = label.name.toLowerCase();
      const categoryText = (label.categories || []).join(' ').toLowerCase();
      
      return videoGameKeywords.some(keyword => 
        (labelText.includes(keyword.toLowerCase()) || categoryText.includes(keyword.toLowerCase()))
        && label.confidence > 75
      );
    });

    console.log('Test Labels:', testLabels);
    console.log('Is Video Game Related:', isGameRelated);

    console.log('\n🎉 Rekognition integration test completed successfully!');
    console.log('\n📝 Next Steps:');
    console.log('1. Deploy the updated AI processing Lambda');
    console.log('2. Upload a test image to trigger processing');
    console.log('3. Check DynamoDB for analysis results');
    console.log('4. Use GET /media/{id}/analysis to retrieve results');

  } catch (error) {
    console.error('❌ Error testing Rekognition:', error);
  }
}

// Run the test
if (require.main === module) {
  testRekognitionAnalysis();
}

export { testRekognitionAnalysis };
