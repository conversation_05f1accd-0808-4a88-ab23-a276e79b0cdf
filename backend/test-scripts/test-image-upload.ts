#!/usr/bin/env npx ts-node

import axios from 'axios';
import * as fs from 'fs';
import * as path from 'path';
import * as crypto from 'crypto';

const API_BASE_URL = process.env.API_BASE_URL || 'https://dev.api.gameflex.io';
const MEDIA_DOMAIN = process.env.MEDIA_DOMAIN || 'd5e3lv3s19ias.cloudfront.net';

interface AuthResponse {
  message: string;
  user: {
    id: string;
    email: string;
    username?: string;
  };
  tokens: {
    accessToken: string;
    refreshToken: string;
    idToken: string;
  };
}

interface UploadResponse {
  message: string;
  mediaId: string;
  uploadUrl: string;
  media: {
    id: string;
    fileName: string;
    fileType: string;
    fileSize: number;
    mediaType: string;
    userId: string;
    status: string;
    staging_s3_key: string;
    final_s3_key: string;
    final_url: string;
    url: string;
  };
  instructions: {
    step1: string;
    step2: string;
    step3: string;
  };
}

async function createTestImage(): Promise<Buffer> {
  // Load the real COD screenshot for testing
  const imagePath = path.join(__dirname, '..', 'assets', 'media', 'cod_screenshot.jpg');
  if (!fs.existsSync(imagePath)) {
    throw new Error(`Test image not found at: ${imagePath}`);
  }
  return fs.readFileSync(imagePath);
}

async function authenticateUser(): Promise<string> {
  console.log('🔐 Authenticating test user...');

  // Generate unique test user
  const timestamp = Date.now();
  const randomId = crypto.randomBytes(3).toString('hex');
  const testEmail = `test-upload-${timestamp}-${randomId}@example.com`;
  const testPassword = 'TestUpload123!';

  try {
    // Sign up
    const signupResponse = await axios.post(`${API_BASE_URL}/auth/signup`, {
      email: testEmail,
      password: testPassword,
    });

    console.log('✅ User signed up successfully');

    // Sign in to get tokens
    const signinResponse = await axios.post<AuthResponse>(`${API_BASE_URL}/auth/signin`, {
      email: testEmail,
      password: testPassword,
    });

    console.log('✅ User signed in successfully');
    return signinResponse.data.tokens.accessToken;

  } catch (error: any) {
    console.error('❌ Authentication failed:', error.response?.data || error.message);
    throw error;
  }
}

async function testImageUpload(): Promise<void> {
  console.log('🧪 Testing Image Upload Functionality\n');

  try {
    // Step 1: Authenticate
    const accessToken = await authenticateUser();

    // Step 2: Create test image
    console.log('🖼️ Creating test image...');
    const imageBuffer = await createTestImage();
    console.log(`✅ Created test image (${imageBuffer.length} bytes)`);

    // Step 3: Request upload URL
    console.log('\n📤 Requesting upload URL...');
    const uploadRequest = {
      fileName: 'cod_screenshot.jpg',
      fileType: 'image/jpeg',
      fileSize: imageBuffer.length,
      mediaType: 'image'
    };

    const uploadResponse = await axios.post<UploadResponse>(`${API_BASE_URL}/media/upload`, uploadRequest, {
      headers: {
        'Authorization': `Bearer ${accessToken}`,
        'Content-Type': 'application/json'
      }
    });

    console.log('✅ Upload URL received');
    console.log('📋 Media ID:', uploadResponse.data.mediaId);
    console.log('📋 Status:', uploadResponse.data.media.status);
    console.log('📋 Staging Key:', uploadResponse.data.media.staging_s3_key);
    console.log('📋 Final Key:', uploadResponse.data.media.final_s3_key);
    console.log('📋 Final URL:', uploadResponse.data.media.final_url);

    // Step 4: Upload image to S3
    console.log('\n⬆️ Uploading image to S3...');
    const s3UploadResponse = await axios.put(uploadResponse.data.uploadUrl, imageBuffer, {
      headers: {
        'Content-Type': 'image/jpeg'
      }
    });

    console.log('✅ Image uploaded to S3 successfully');
    console.log('📋 S3 Response Status:', s3UploadResponse.status);

    // Step 5: Verify media record status
    console.log('\n🔍 Checking media record status...');
    let attempts = 0;
    const maxAttempts = 10;

    while (attempts < maxAttempts) {
      try {
        const mediaResponse = await axios.get(`${API_BASE_URL}/media/${uploadResponse.data.mediaId}`, {
          headers: {
            'Authorization': `Bearer ${accessToken}`
          }
        });

        const media = mediaResponse.data.media;
        console.log(`📋 Attempt ${attempts + 1}: Status = ${media.status}`);

        if (media.status === 'approved') {
          console.log('✅ Media processing completed!');
          console.log('📋 Final URL:', media.url);
          console.log('📋 CloudFront URL:', media.final_url);

          // Step 6: Test CloudFront access
          console.log('\n🌐 Testing CloudFront access...');
          try {
            const cloudFrontResponse = await axios.head(media.final_url);
            console.log('✅ CloudFront access successful');
            console.log('📋 CloudFront Status:', cloudFrontResponse.status);
            console.log('📋 Content-Type:', cloudFrontResponse.headers['content-type']);
          } catch (cfError: any) {
            console.log('⚠️ CloudFront access failed:', cfError.response?.status || cfError.message);
          }

          break;
        } else if (media.status === 'failed' || media.status === 'rejected_inappropriate') {
          console.log('❌ Media processing failed with status:', media.status);
          break;
        }

        attempts++;
        if (attempts < maxAttempts) {
          console.log('⏳ Waiting 2 seconds before next check...');
          await new Promise(resolve => setTimeout(resolve, 2000));
        }
      } catch (error: any) {
        console.log('⚠️ Error checking media status:', error.response?.status || error.message);
        attempts++;
        if (attempts < maxAttempts) {
          await new Promise(resolve => setTimeout(resolve, 2000));
        }
      }
    }

    if (attempts >= maxAttempts) {
      console.log('⚠️ Media processing did not complete within expected time');
    }

    console.log('\n🎉 Image upload test completed!');

  } catch (error: any) {
    console.error('❌ Test failed:', error.response?.data || error.message);
    if (error.response?.status) {
      console.error('📋 Status Code:', error.response.status);
    }
    throw error;
  }
}

// Run the test
if (require.main === module) {
  testImageUpload()
    .then(() => {
      console.log('\n✅ All tests passed!');
      process.exit(0);
    })
    .catch((error) => {
      console.error('\n❌ Test failed:', error.message);
      process.exit(1);
    });
}
