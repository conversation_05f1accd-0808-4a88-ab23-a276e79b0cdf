{"swagger": "2.0", "info": {"version": "2025-07-26T20:48:33Z", "title": "gameflex-api-development"}, "host": "bllxxn79s2.execute-api.us-west-2.amazonaws.com", "basePath": "/v1", "schemes": ["https"], "paths": {"/": {"options": {"consumes": ["application/json"], "responses": {"204": {"description": "204 response", "headers": {"Access-Control-Allow-Origin": {"type": "string"}, "Access-Control-Allow-Methods": {"type": "string"}, "Access-Control-Allow-Headers": {"type": "string"}}}}}}, "/auth": {"options": {"consumes": ["application/json"], "responses": {"204": {"description": "204 response", "headers": {"Access-Control-Allow-Origin": {"type": "string"}, "Access-Control-Allow-Methods": {"type": "string"}, "Access-Control-Allow-Headers": {"type": "string"}}}}}}, "/auth/refresh": {"post": {"consumes": ["application/json"], "produces": ["application/json"], "parameters": [{"in": "body", "name": "RefreshRequest", "required": true, "schema": {"$ref": "#/definitions/RefreshRequest"}}], "responses": {"200": {"description": "200 response", "schema": {"$ref": "#/definitions/RefreshResponse"}, "headers": {"Access-Control-Allow-Origin": {"type": "string"}, "Content-Type": {"type": "string"}}}, "400": {"description": "400 response", "schema": {"$ref": "#/definitions/ErrorResponse"}, "headers": {"Access-Control-Allow-Origin": {"type": "string"}, "Content-Type": {"type": "string"}}}, "500": {"description": "500 response", "schema": {"$ref": "#/definitions/ErrorResponse"}, "headers": {"Access-Control-Allow-Origin": {"type": "string"}, "Content-Type": {"type": "string"}}}, "401": {"description": "401 response", "schema": {"$ref": "#/definitions/ErrorResponse"}, "headers": {"Access-Control-Allow-Origin": {"type": "string"}, "Content-Type": {"type": "string"}}}}}, "options": {"consumes": ["application/json"], "responses": {"204": {"description": "204 response", "headers": {"Access-Control-Allow-Origin": {"type": "string"}, "Access-Control-Allow-Methods": {"type": "string"}, "Access-Control-Allow-Headers": {"type": "string"}}}}}}, "/auth/signin": {"post": {"tags": ["Authentication"], "summary": "Authenticate user", "description": "Authenticate user with email and password. Returns JWT tokens (access, refresh, ID) and user profile information upon successful authentication.", "consumes": ["application/json"], "produces": ["application/json"], "parameters": [{"in": "body", "name": "SigninRequest", "required": true, "schema": {"$ref": "#/definitions/SigninRequest"}}], "responses": {"200": {"description": "200 response", "schema": {"$ref": "#/definitions/SigninResponse"}, "headers": {"Access-Control-Allow-Origin": {"type": "string"}, "Content-Type": {"type": "string"}}}, "400": {"description": "400 response", "schema": {"$ref": "#/definitions/ErrorResponse"}, "headers": {"Access-Control-Allow-Origin": {"type": "string"}, "Content-Type": {"type": "string"}}}, "500": {"description": "500 response", "schema": {"$ref": "#/definitions/ErrorResponse"}, "headers": {"Access-Control-Allow-Origin": {"type": "string"}, "Content-Type": {"type": "string"}}}, "401": {"description": "401 response", "schema": {"$ref": "#/definitions/ErrorResponse"}, "headers": {"Access-Control-Allow-Origin": {"type": "string"}, "Content-Type": {"type": "string"}}}}}, "options": {"consumes": ["application/json"], "responses": {"204": {"description": "204 response", "headers": {"Access-Control-Allow-Origin": {"type": "string"}, "Access-Control-Allow-Methods": {"type": "string"}, "Access-Control-Allow-Headers": {"type": "string"}}}}}}, "/auth/signup": {"post": {"consumes": ["application/json"], "produces": ["application/json"], "parameters": [{"in": "body", "name": "SignupRequest", "required": true, "schema": {"$ref": "#/definitions/SignupRequest"}}], "responses": {"201": {"description": "201 response", "schema": {"$ref": "#/definitions/SignupResponse"}, "headers": {"Access-Control-Allow-Origin": {"type": "string"}, "Content-Type": {"type": "string"}}}, "400": {"description": "400 response", "schema": {"$ref": "#/definitions/ErrorResponse"}, "headers": {"Access-Control-Allow-Origin": {"type": "string"}, "Content-Type": {"type": "string"}}}, "500": {"description": "500 response", "schema": {"$ref": "#/definitions/ErrorResponse"}, "headers": {"Access-Control-Allow-Origin": {"type": "string"}, "Content-Type": {"type": "string"}}}, "401": {"description": "401 response", "schema": {"$ref": "#/definitions/ErrorResponse"}, "headers": {"Access-Control-Allow-Origin": {"type": "string"}, "Content-Type": {"type": "string"}}}}}, "options": {"consumes": ["application/json"], "responses": {"204": {"description": "204 response", "headers": {"Access-Control-Allow-Origin": {"type": "string"}, "Access-Control-Allow-Methods": {"type": "string"}, "Access-Control-Allow-Headers": {"type": "string"}}}}}}, "/auth/validate": {"get": {"tags": ["Authentication"], "summary": "Validate access token", "description": "Validate the provided JWT access token and return user information if valid. Requires Authorization header with Bear<PERSON> token.", "produces": ["application/json"], "parameters": [{"name": "Authorization", "in": "header", "required": true, "type": "string"}], "responses": {"200": {"description": "200 response", "schema": {"$ref": "#/definitions/ValidateResponse"}, "headers": {"Access-Control-Allow-Origin": {"type": "string"}, "Content-Type": {"type": "string"}}}, "400": {"description": "400 response", "schema": {"$ref": "#/definitions/ErrorResponse"}, "headers": {"Access-Control-Allow-Origin": {"type": "string"}, "Content-Type": {"type": "string"}}}, "500": {"description": "500 response", "schema": {"$ref": "#/definitions/ErrorResponse"}, "headers": {"Access-Control-Allow-Origin": {"type": "string"}, "Content-Type": {"type": "string"}}}, "401": {"description": "401 response", "schema": {"$ref": "#/definitions/ErrorResponse"}, "headers": {"Access-Control-Allow-Origin": {"type": "string"}, "Content-Type": {"type": "string"}}}, "404": {"description": "404 response", "schema": {"$ref": "#/definitions/ErrorResponse"}, "headers": {"Access-Control-Allow-Origin": {"type": "string"}, "Content-Type": {"type": "string"}}}}}, "options": {"consumes": ["application/json"], "responses": {"204": {"description": "204 response", "headers": {"Access-Control-Allow-Origin": {"type": "string"}, "Access-Control-Allow-Methods": {"type": "string"}, "Access-Control-Allow-Headers": {"type": "string"}}}}}}, "/channels": {"get": {"responses": {}, "security": [{"DefaultAuthorizer": []}]}, "post": {"responses": {}, "security": [{"DefaultAuthorizer": []}]}, "options": {"consumes": ["application/json"], "responses": {"204": {"description": "204 response", "headers": {"Access-Control-Allow-Origin": {"type": "string"}, "Access-Control-Allow-Methods": {"type": "string"}, "Access-Control-Allow-Headers": {"type": "string"}}}}}}, "/channels/{id}": {"get": {"parameters": [{"name": "id", "in": "path", "required": true, "type": "string"}], "responses": {}, "security": [{"DefaultAuthorizer": []}]}, "put": {"parameters": [{"name": "id", "in": "path", "required": true, "type": "string"}], "responses": {}, "security": [{"DefaultAuthorizer": []}]}, "delete": {"parameters": [{"name": "id", "in": "path", "required": true, "type": "string"}], "responses": {}, "security": [{"DefaultAuthorizer": []}]}, "options": {"consumes": ["application/json"], "parameters": [{"name": "id", "in": "path", "required": true, "type": "string"}], "responses": {"204": {"description": "204 response", "headers": {"Access-Control-Allow-Origin": {"type": "string"}, "Access-Control-Allow-Methods": {"type": "string"}, "Access-Control-Allow-Headers": {"type": "string"}}}}}}, "/health": {"get": {"responses": {}}, "options": {"consumes": ["application/json"], "responses": {"204": {"description": "204 response", "headers": {"Access-Control-Allow-Origin": {"type": "string"}, "Access-Control-Allow-Methods": {"type": "string"}, "Access-Control-Allow-Headers": {"type": "string"}}}}}}, "/media": {"get": {"responses": {}, "security": [{"DefaultAuthorizer": []}]}, "post": {"responses": {}, "security": [{"DefaultAuthorizer": []}]}, "options": {"consumes": ["application/json"], "responses": {"204": {"description": "204 response", "headers": {"Access-Control-Allow-Origin": {"type": "string"}, "Access-Control-Allow-Methods": {"type": "string"}, "Access-Control-Allow-Headers": {"type": "string"}}}}}}, "/media/upload": {"post": {"responses": {}, "security": [{"DefaultAuthorizer": []}]}, "options": {"consumes": ["application/json"], "responses": {"204": {"description": "204 response", "headers": {"Access-Control-Allow-Origin": {"type": "string"}, "Access-Control-Allow-Methods": {"type": "string"}, "Access-Control-Allow-Headers": {"type": "string"}}}}}}, "/media/{id}": {"get": {"parameters": [{"name": "id", "in": "path", "required": true, "type": "string"}], "responses": {}, "security": [{"DefaultAuthorizer": []}]}, "put": {"parameters": [{"name": "id", "in": "path", "required": true, "type": "string"}], "responses": {}, "security": [{"DefaultAuthorizer": []}]}, "delete": {"parameters": [{"name": "id", "in": "path", "required": true, "type": "string"}], "responses": {}, "security": [{"DefaultAuthorizer": []}]}, "options": {"consumes": ["application/json"], "parameters": [{"name": "id", "in": "path", "required": true, "type": "string"}], "responses": {"204": {"description": "204 response", "headers": {"Access-Control-Allow-Origin": {"type": "string"}, "Access-Control-Allow-Methods": {"type": "string"}, "Access-Control-Allow-Headers": {"type": "string"}}}}}}, "/posts": {"get": {"responses": {}, "security": [{"DefaultAuthorizer": []}]}, "post": {"responses": {}, "security": [{"DefaultAuthorizer": []}]}, "options": {"consumes": ["application/json"], "responses": {"204": {"description": "204 response", "headers": {"Access-Control-Allow-Origin": {"type": "string"}, "Access-Control-Allow-Methods": {"type": "string"}, "Access-Control-Allow-Headers": {"type": "string"}}}}}}, "/posts/draft": {"post": {"responses": {}, "security": [{"DefaultAuthorizer": []}]}, "options": {"consumes": ["application/json"], "responses": {"204": {"description": "204 response", "headers": {"Access-Control-Allow-Origin": {"type": "string"}, "Access-Control-Allow-Methods": {"type": "string"}, "Access-Control-Allow-Headers": {"type": "string"}}}}}}, "/posts/{id}": {"get": {"parameters": [{"name": "id", "in": "path", "required": true, "type": "string"}], "responses": {}, "security": [{"DefaultAuthorizer": []}]}, "put": {"parameters": [{"name": "id", "in": "path", "required": true, "type": "string"}], "responses": {}, "security": [{"DefaultAuthorizer": []}]}, "delete": {"parameters": [{"name": "id", "in": "path", "required": true, "type": "string"}], "responses": {}, "security": [{"DefaultAuthorizer": []}]}, "options": {"consumes": ["application/json"], "parameters": [{"name": "id", "in": "path", "required": true, "type": "string"}], "responses": {"204": {"description": "204 response", "headers": {"Access-Control-Allow-Origin": {"type": "string"}, "Access-Control-Allow-Methods": {"type": "string"}, "Access-Control-Allow-Headers": {"type": "string"}}}}}}, "/posts/{id}/comments": {"get": {"parameters": [{"name": "id", "in": "path", "required": true, "type": "string"}], "responses": {}, "security": [{"DefaultAuthorizer": []}]}, "post": {"parameters": [{"name": "id", "in": "path", "required": true, "type": "string"}], "responses": {}, "security": [{"DefaultAuthorizer": []}]}, "options": {"consumes": ["application/json"], "parameters": [{"name": "id", "in": "path", "required": true, "type": "string"}], "responses": {"204": {"description": "204 response", "headers": {"Access-Control-Allow-Origin": {"type": "string"}, "Access-Control-Allow-Methods": {"type": "string"}, "Access-Control-Allow-Headers": {"type": "string"}}}}}}, "/posts/{id}/like": {"post": {"parameters": [{"name": "id", "in": "path", "required": true, "type": "string"}], "responses": {}, "security": [{"DefaultAuthorizer": []}]}, "delete": {"parameters": [{"name": "id", "in": "path", "required": true, "type": "string"}], "responses": {}, "security": [{"DefaultAuthorizer": []}]}, "options": {"consumes": ["application/json"], "parameters": [{"name": "id", "in": "path", "required": true, "type": "string"}], "responses": {"204": {"description": "204 response", "headers": {"Access-Control-Allow-Origin": {"type": "string"}, "Access-Control-Allow-Methods": {"type": "string"}, "Access-Control-Allow-Headers": {"type": "string"}}}}}}, "/posts/{id}/media": {"put": {"parameters": [{"name": "id", "in": "path", "required": true, "type": "string"}], "responses": {}, "security": [{"DefaultAuthorizer": []}]}, "options": {"consumes": ["application/json"], "parameters": [{"name": "id", "in": "path", "required": true, "type": "string"}], "responses": {"204": {"description": "204 response", "headers": {"Access-Control-Allow-Origin": {"type": "string"}, "Access-Control-Allow-Methods": {"type": "string"}, "Access-Control-Allow-Headers": {"type": "string"}}}}}}, "/posts/{id}/publish": {"put": {"parameters": [{"name": "id", "in": "path", "required": true, "type": "string"}], "responses": {}, "security": [{"DefaultAuthorizer": []}]}, "options": {"consumes": ["application/json"], "parameters": [{"name": "id", "in": "path", "required": true, "type": "string"}], "responses": {"204": {"description": "204 response", "headers": {"Access-Control-Allow-Origin": {"type": "string"}, "Access-Control-Allow-Methods": {"type": "string"}, "Access-Control-Allow-Headers": {"type": "string"}}}}}}, "/reflexes": {"get": {"responses": {}, "security": [{"DefaultAuthorizer": []}]}, "post": {"responses": {}, "security": [{"DefaultAuthorizer": []}]}, "options": {"consumes": ["application/json"], "responses": {"204": {"description": "204 response", "headers": {"Access-Control-Allow-Origin": {"type": "string"}, "Access-Control-Allow-Methods": {"type": "string"}, "Access-Control-Allow-Headers": {"type": "string"}}}}}}, "/reflexes/{id}": {"get": {"parameters": [{"name": "id", "in": "path", "required": true, "type": "string"}], "responses": {}, "security": [{"DefaultAuthorizer": []}]}, "put": {"parameters": [{"name": "id", "in": "path", "required": true, "type": "string"}], "responses": {}, "security": [{"DefaultAuthorizer": []}]}, "delete": {"parameters": [{"name": "id", "in": "path", "required": true, "type": "string"}], "responses": {}, "security": [{"DefaultAuthorizer": []}]}, "options": {"consumes": ["application/json"], "parameters": [{"name": "id", "in": "path", "required": true, "type": "string"}], "responses": {"204": {"description": "204 response", "headers": {"Access-Control-Allow-Origin": {"type": "string"}, "Access-Control-Allow-Methods": {"type": "string"}, "Access-Control-Allow-Headers": {"type": "string"}}}}}}, "/users": {"get": {"responses": {}, "security": [{"DefaultAuthorizer": []}]}, "post": {"responses": {}, "security": [{"DefaultAuthorizer": []}]}, "options": {"consumes": ["application/json"], "responses": {"204": {"description": "204 response", "headers": {"Access-Control-Allow-Origin": {"type": "string"}, "Access-Control-Allow-Methods": {"type": "string"}, "Access-Control-Allow-Headers": {"type": "string"}}}}}}, "/users/profile": {"get": {"responses": {}, "security": [{"DefaultAuthorizer": []}]}, "put": {"responses": {}, "security": [{"DefaultAuthorizer": []}]}, "options": {"consumes": ["application/json"], "responses": {"204": {"description": "204 response", "headers": {"Access-Control-Allow-Origin": {"type": "string"}, "Access-Control-Allow-Methods": {"type": "string"}, "Access-Control-Allow-Headers": {"type": "string"}}}}}}, "/users/profile/liked-posts": {"get": {"responses": {}, "security": [{"DefaultAuthorizer": []}]}, "options": {"consumes": ["application/json"], "responses": {"204": {"description": "204 response", "headers": {"Access-Control-Allow-Origin": {"type": "string"}, "Access-Control-Allow-Methods": {"type": "string"}, "Access-Control-Allow-Headers": {"type": "string"}}}}}}, "/users/{id}": {"get": {"parameters": [{"name": "id", "in": "path", "required": true, "type": "string"}], "responses": {}, "security": [{"DefaultAuthorizer": []}]}, "put": {"parameters": [{"name": "id", "in": "path", "required": true, "type": "string"}], "responses": {}, "security": [{"DefaultAuthorizer": []}]}, "options": {"consumes": ["application/json"], "parameters": [{"name": "id", "in": "path", "required": true, "type": "string"}], "responses": {"204": {"description": "204 response", "headers": {"Access-Control-Allow-Origin": {"type": "string"}, "Access-Control-Allow-Methods": {"type": "string"}, "Access-Control-Allow-Headers": {"type": "string"}}}}}}}, "securityDefinitions": {"DefaultAuthorizer": {"type": "<PERSON><PERSON><PERSON><PERSON>", "name": "Authorization", "in": "header", "x-amazon-apigateway-authtype": "custom"}}, "definitions": {"RefreshRequest": {"type": "object", "required": ["refreshToken"], "properties": {"refreshToken": {"type": "string", "description": "Valid refresh token obtained from signin or previous refresh"}}, "title": "Refresh Token Request", "description": "Request to refresh access token using refresh token"}, "ValidateResponse": {"type": "object", "required": ["user", "valid"], "properties": {"valid": {"type": "boolean", "description": "Whether the token is valid"}, "user": {"type": "object", "description": "User information from the token", "properties": {"id": {"type": "string", "description": "User ID"}, "email": {"type": "string", "description": "User email"}, "username": {"type": "string", "description": "Username"}}}}, "title": "Token Validation Success Response", "description": "Response returned when token validation is successful"}, "RefreshResponse": {"type": "object", "required": ["accessToken", "idToken"], "properties": {"idToken": {"type": "string", "description": "New JWT ID token"}, "accessToken": {"type": "string", "description": "New JWT access token"}}, "title": "Token Refresh Success Response", "description": "Response returned when token refresh is successful"}, "SigninRequest": {"type": "object", "required": ["email", "password"], "properties": {"password": {"type": "string", "description": "User password", "minLength": 1}, "email": {"type": "string", "format": "email", "description": "User email address"}}, "title": "Signin Request", "description": "User authentication request containing email and password"}, "SignupRequest": {"type": "object", "required": ["email", "password", "username"], "properties": {"firstName": {"type": "string", "description": "User first name (optional)", "maxLength": 50}, "lastName": {"type": "string", "description": "User last name (optional)", "maxLength": 50}, "password": {"type": "string", "description": "Password must contain at least 8 characters with uppercase, lowercase, number and special character", "minLength": 8, "maxLength": 128}, "email": {"type": "string", "format": "email", "description": "User email address (must be valid email format)"}, "username": {"type": "string", "description": "Unique username (alphanumeric, underscore, and hyphen only)", "minLength": 3, "maxLength": 30, "pattern": "^[a-zA-Z0-9_-]+$"}}, "title": "Signup Request", "description": "User registration request containing email, password, username and optional profile information"}, "SignupResponse": {"type": "object", "required": ["message"], "properties": {"message": {"type": "string", "description": "Success message"}, "userId": {"type": "string", "description": "Unique user identifier"}}, "title": "Signup Success Response", "description": "Response returned when user signup is successful"}, "ErrorResponse": {"type": "object", "required": ["error"], "properties": {"details": {"type": "string", "description": "Additional error details for debugging"}, "error": {"type": "string", "description": "Error message describing what went wrong"}}, "title": "Error Response", "description": "Standard error response format"}, "SigninResponse": {"type": "object", "required": ["accessToken", "idToken", "refreshToken", "user"], "properties": {"idToken": {"type": "string", "description": "JWT ID token containing user identity information"}, "accessToken": {"type": "string", "description": "JWT access token for API authentication"}, "user": {"type": "object", "description": "User profile information", "properties": {"id": {"type": "string", "description": "User ID"}, "email": {"type": "string", "description": "User email"}, "username": {"type": "string", "description": "Username"}}}, "refreshToken": {"type": "string", "description": "JWT refresh token for obtaining new access tokens"}}, "title": "Signin Success Response", "description": "Response returned when user signin is successful"}}}