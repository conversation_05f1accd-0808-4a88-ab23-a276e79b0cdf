#!/bin/bash

# GameFlex Mockup Server
# Serves the HTML/CSS mockup website locally for Figma import

echo "🎮 Starting GameFlex Mockup Server..."
echo "📁 Serving from: mockup/"
echo "🔗 URL: http://localhost:8081"
echo ""
echo "📱 This mockup includes all GameFlex app screens:"
echo "   • Splash Screen"
echo "   • Login/Signup Screen"
echo "   • Username Selection"
echo "   • Home Feed"
echo "   • Upload Screen"
echo "   • Profile Screen"
echo "   • Xbox Media Browser"
echo ""
echo "🎨 Perfect for importing into Figma!"
echo "Press Ctrl+C to stop the server"
echo ""

# Check if mockup directory exists
if [ ! -d "mockup" ]; then
    echo "❌ mockup directory not found!"
    echo "Please make sure you're running this from the project root"
    exit 1
fi

# Change to mockup directory
cd mockup

# Try different methods to start a web server
if command -v python3 &> /dev/null; then
    echo "Using Python 3 HTTP server..."
    python3 -m http.server 8081
elif command -v python &> /dev/null; then
    echo "Using Python 2 HTTP server..."
    python -m SimpleHTTPServer 8081
elif command -v php &> /dev/null; then
    echo "Using PHP built-in server..."
    php -S localhost:8081
elif command -v node &> /dev/null; then
    echo "Using Node.js http-server (if available)..."
    if command -v npx &> /dev/null; then
        npx http-server -p 8081
    else
        echo "❌ http-server not found. Please install with: npm install -g http-server"
        exit 1
    fi
else
    echo "❌ No suitable web server found!"
    echo "Please install one of the following:"
    echo "  • Python 3: python3 -m http.server"
    echo "  • Python 2: python -m SimpleHTTPServer"
    echo "  • PHP: php -S localhost:8081"
    echo "  • Node.js: npm install -g http-server"
    exit 1
fi
