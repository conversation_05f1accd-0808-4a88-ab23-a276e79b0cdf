import 'package:flutter_test/flutter_test.dart';
import 'package:gameflex_mobile/models/post_model.dart';
import 'package:gameflex_mobile/models/reflex_model.dart';

void main() {
  group('Emoji Reactions Tests', () {
    test('PostModel should handle single reaction correctly', () {
      final post = PostModel(
        id: 'test-post',
        userId: 'user1',
        content: 'Test post',
        likeCount: 0,
        commentCount: 0,
        reflexCount: 0,
        isActive: true,
        status: 'published',
        createdAt: DateTime.now(),
        updatedAt: DateTime.now(),
        reactions: {'🔥': 3, '👍': 1},
        currentUserReaction: '🔥',
      );

      expect(post.reactions['🔥'], equals(3));
      expect(post.reactions['👍'], equals(1));
      expect(post.currentUserReaction, equals('🔥'));
    });

    test('PostModel should handle no reactions', () {
      final post = PostModel(
        id: 'test-post',
        userId: 'user1',
        content: 'Test post',
        likeCount: 0,
        commentCount: 0,
        reflexCount: 0,
        isActive: true,
        status: 'published',
        createdAt: DateTime.now(),
        updatedAt: DateTime.now(),
      );

      expect(post.reactions, isEmpty);
      expect(post.currentUserReaction, isNull);
    });

    test('PostModel copyWith should update reactions correctly', () {
      final originalPost = PostModel(
        id: 'test-post',
        userId: 'user1',
        content: 'Test post',
        likeCount: 0,
        commentCount: 0,
        reflexCount: 0,
        isActive: true,
        status: 'published',
        createdAt: DateTime.now(),
        updatedAt: DateTime.now(),
        reactions: {'👍': 1},
        currentUserReaction: '👍',
      );

      final updatedPost = originalPost.copyWith(
        reactions: {'👍': 1, '🔥': 1},
        currentUserReaction: '🔥',
      );

      expect(updatedPost.reactions['👍'], equals(1));
      expect(updatedPost.reactions['🔥'], equals(1));
      expect(updatedPost.currentUserReaction, equals('🔥'));
    });

    test('ReflexModel should handle single reaction correctly', () {
      final reflex = ReflexModel(
        id: 'test-reflex',
        postId: 'test-post',
        userId: 'user1',
        reflexType: ReflexType.flare,
        likes: 0,
        isActive: true,
        createdAt: DateTime.now(),
        updatedAt: DateTime.now(),
        reactions: {'😂': 2},
        currentUserReaction: '😂',
      );

      expect(reflex.reactions['😂'], equals(2));
      expect(reflex.currentUserReaction, equals('😂'));
    });

    test('PostModel fromJson should parse reactions correctly', () {
      final json = {
        'id': 'test-post',
        'userId': 'user1',
        'content': 'Test post',
        'likeCount': 0,
        'commentCount': 0,
        'reflexCount': 0,
        'isActive': true,
        'status': 'published',
        'createdAt': DateTime.now().toIso8601String(),
        'updatedAt': DateTime.now().toIso8601String(),
        'reactions': {'🔥': 3, '👍': 1},
        'currentUserReactions': ['🔥'], // Backend sends as array
      };

      final post = PostModel.fromJson(json);

      expect(post.reactions['🔥'], equals(3));
      expect(post.reactions['👍'], equals(1));
      expect(post.currentUserReaction, equals('🔥')); // Should take first from array
    });

    test('PostModel fromJson should handle empty reactions', () {
      final json = {
        'id': 'test-post',
        'userId': 'user1',
        'content': 'Test post',
        'likeCount': 0,
        'commentCount': 0,
        'reflexCount': 0,
        'isActive': true,
        'status': 'published',
        'createdAt': DateTime.now().toIso8601String(),
        'updatedAt': DateTime.now().toIso8601String(),
        'reactions': <String, int>{},
        'currentUserReactions': <String>[],
      };

      final post = PostModel.fromJson(json);

      expect(post.reactions, isEmpty);
      expect(post.currentUserReaction, isNull);
    });
  });
}
