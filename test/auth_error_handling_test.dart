import 'package:flutter_test/flutter_test.dart';
import 'package:gameflex_mobile/services/api_service.dart';

void main() {
  group('Authentication Error Handling Tests', () {
    test('ApiException should include user-friendly message', () {
      const errorMessage = 'Invalid email or password';
      const userFriendlyMessage =
          'The email or password you entered is incorrect.';

      final exception = ApiException(
        message: errorMessage,
        statusCode: 401,
        userFriendlyMessage: userFriendlyMessage,
      );

      expect(exception.message, equals(errorMessage));
      expect(exception.userFriendlyMessage, equals(userFriendlyMessage));
      expect(exception.statusCode, equals(401));
    });

    test(
      'ApiException should use message as userFriendlyMessage when not provided',
      () {
        const errorMessage = 'Authentication failed';

        final exception = ApiException(message: errorMessage, statusCode: 401);

        expect(exception.userFriendlyMessage, equals(errorMessage));
      },
    );

    test('ApiException toString should include status code', () {
      const errorMessage = 'Invalid credentials';

      final exception = ApiException(message: errorMessage, statusCode: 401);

      expect(exception.toString(), contains('401'));
      expect(exception.toString(), contains(errorMessage));
    });
  });
}
