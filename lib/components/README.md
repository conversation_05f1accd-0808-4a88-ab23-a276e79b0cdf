# GameFlex Component Library

This document provides an overview of the GameFlex component library, designed to help the design team understand and modify the look and feel of the app.

## 📁 Component Structure

The components are organized into logical categories for easy navigation and maintenance:

```
lib/components/
├── core/           # Basic UI building blocks
├── layout/         # Layout and structural components  
├── media/          # Media-related components
├── feed/           # Feed-specific components
├── forms/          # Form and input components
├── auth/           # Authentication components
├── channels/       # Channel-related components
└── index.dart      # Main export file
```

## 🎨 Design System

All components follow the GameFlex design system with consistent:
- **Colors**: Defined in `lib/theme/app_theme.dart`
- **Typography**: Using Google Fonts (Roboto)
- **Spacing**: Consistent padding and margins
- **Border Radius**: Rounded corners for modern look
- **Shadows**: Subtle elevation effects

## 🧱 Core Components (`lib/components/core/`)

### GFButton
Reusable button component with multiple styles:
- **Primary**: Green background, black text
- **Secondary**: Dark background with border
- **Outline**: Transparent with green border
- **Text**: Text-only button
- **Danger**: Red background for destructive actions

**Props**: `text`, `onPressed`, `type`, `height`, `width`, `isEnabled`, `icon`, `isLoading`

### GFTextField
Consistent text input component:
- **Standard**: With label and validation
- **Search**: Specialized for search functionality

**Props**: `label`, `hint`, `controller`, `validator`, `onChanged`, `prefixIcon`, `suffixIcon`

### GFAvatar
User avatar component with fallbacks:
- **Standard**: Circular avatar with image
- **WithStatus**: Shows online/offline indicator
- **Group**: Multiple overlapping avatars

**Props**: `imageUrl`, `displayName`, `radius`, `onTap`, `showBorder`

### GFCard
Container component for content:
- **Standard**: Basic card with padding
- **List**: For list items with leading/trailing
- **Media**: For media content with header/footer
- **Empty**: For empty states

**Props**: `child`, `onTap`, `padding`, `margin`, `backgroundColor`

### GFLoading
Loading state components:
- **Indicator**: Circular progress indicator
- **Overlay**: Full-screen loading
- **Shimmer**: Skeleton loading effect
- **List**: Loading state for lists

## 🏗️ Layout Components (`lib/components/layout/`)

### GFBottomNavigation
Navigation bar components:
- **Standard**: Configurable tabs with icons/labels
- **Simple**: Current app style (3 tabs)
- **Floating**: Rounded floating navigation

### GFAppBar
App bar variations:
- **Standard**: Basic app bar with title
- **Transparent**: For overlay scenarios
- **Profile**: With user avatar
- **Search**: With search input

### GFOverlay
Overlay components for different scenarios:
- **Top/Bottom**: Feed overlays with gradients
- **Side**: Drawer-style overlays
- **Modal**: Dialog and bottom sheet containers
- **Floating**: Notification-style overlays

## 📱 Media Components (`lib/components/media/`)

### GFImage
Image display with loading states:
- **Standard**: Basic image with error handling
- **Lazy**: Lazy loading for performance
- **Circular**: For profile pictures

### GFVideoPlayer
Video playback component:
- **Standard**: Full video player with controls
- **Thumbnail**: Video preview with play button

## 📰 Feed Components (`lib/components/feed/`)

### GFPostCard
Post display components:
- **Standard**: Full post card with media
- **Compact**: Condensed list view

### GFReactionBar
Discord-style emoji reactions:
- **ReactionChip**: Individual reaction display
- **AddButton**: Add new reaction
- **Summary**: Total reaction count

### GFFeedOverlay
Feed-specific overlays:
- **Top**: User info and channel
- **Bottom**: Actions and description
- **Side**: Additional actions (reflexes)

## 📝 Form Components (`lib/components/forms/`)

### GFPicker
Selection components:
- **Single**: Select one option
- **Multi**: Select multiple options
- **Bottom Sheet**: Modal picker interface

### GFEmojiPicker
Emoji selection:
- **Standard**: Full emoji grid
- **Quick**: Quick reaction buttons
- **Dialog/BottomSheet**: Modal interfaces

## 🔐 Auth Components (`lib/components/auth/`)

### Sign-in Buttons
Platform-specific sign-in:
- **Apple**: Apple ID sign-in
- **Xbox**: Xbox Live sign-in
- **Google**: Google account sign-in
- **Email**: Email/password sign-in

### Account Linking
Third-party account management:
- **LinkedAccount**: Display connected accounts
- **LinkingOptions**: Available providers to link
- **AccountChoice**: Choose between new/existing

## 📺 Channel Components (`lib/components/channels/`)

### GFChannelCard
Channel display:
- **Standard**: Full channel info with stats
- **Compact**: List view
- **Header**: Channel detail page header

### GFChannelSelection
Channel selection for posts:
- **Widget**: Full selection interface
- **Quick**: Quick selection chips
- **BottomSheet**: Modal selection

## 🎯 Usage Examples

### Basic Button
```dart
GFButton(
  text: 'Sign In',
  type: GFButtonType.primary,
  onPressed: () => _handleSignIn(),
)
```

### Text Field with Validation
```dart
GFTextField(
  label: 'Email',
  hint: 'Enter your email',
  keyboardType: TextInputType.emailAddress,
  validator: (value) => value?.isEmpty == true ? 'Required' : null,
)
```

### Post Card
```dart
GFPostCard(
  post: post,
  onTap: () => _viewPost(post),
  onReaction: (emoji) => _addReaction(post, emoji),
  showFullContent: false,
)
```

## 🎨 Customization Guide

### Colors
Modify colors in `lib/theme/app_theme.dart`:
```dart
class AppColors {
  static const Color gfGreen = Color(0xFF00FF88);
  static const Color gfDarkBackground = Color(0xFF1A1A1A);
  // ... other colors
}
```

### Typography
Update fonts in the theme:
```dart
textTheme: GoogleFonts.robotoTextTheme(),
```

### Component Styling
Each component accepts styling props:
- `backgroundColor`: Override background color
- `borderRadius`: Customize corner radius
- `padding`/`margin`: Adjust spacing
- `height`/`width`: Control dimensions

## 🔧 Development Workflow

1. **Import Components**: Use the main index file
   ```dart
   import 'package:gameflex_mobile/components/index.dart';
   ```

2. **Customize Appearance**: Modify theme colors and typography

3. **Extend Components**: Create new variants by extending existing components

4. **Test Changes**: Use the component in different screens to ensure consistency

## 📋 Component Checklist

When creating new components:
- [ ] Follow naming convention (GF prefix)
- [ ] Include comprehensive documentation
- [ ] Support theme colors and typography
- [ ] Handle loading and error states
- [ ] Include accessibility features
- [ ] Add to appropriate index.dart file
- [ ] Test on different screen sizes

## 🚀 Next Steps

1. **Review Components**: Familiarize yourself with each component category
2. **Identify Customizations**: Determine which visual aspects need modification
3. **Update Theme**: Modify colors, fonts, and spacing in the theme file
4. **Test Changes**: Verify modifications across different screens
5. **Document Changes**: Keep track of customizations for future reference

This component library provides a solid foundation for maintaining design consistency while allowing for easy customization and extension.
