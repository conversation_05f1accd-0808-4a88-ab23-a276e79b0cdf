import 'package:flutter/material.dart';
import '../../theme/app_theme.dart';

import '../core/gf_button.dart';

/// GameFlex picker components
///
/// A collection of picker components for selecting from lists of options,
/// including dropdowns, bottom sheets, and modal pickers.

/// Base picker component
class GFPicker<T> extends StatelessWidget {
  final String label;
  final T? value;
  final List<GFPickerItem<T>> items;
  final void Function(T?) onChanged;
  final String? hint;
  final Widget? prefixIcon;
  final bool enabled;
  final String? errorText;

  const GFPicker({
    super.key,
    required this.label,
    this.value,
    required this.items,
    required this.onChanged,
    this.hint,
    this.prefixIcon,
    this.enabled = true,
    this.errorText,
  });

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          label,
          style: const TextStyle(
            color: AppColors.gfOffWhite,
            fontSize: 14,
            fontWeight: FontWeight.w500,
          ),
        ),
        const SizedBox(height: 8),
        GestureDetector(
          onTap: enabled ? () => _showPicker(context) : null,
          child: Container(
            padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 14),
            decoration: BoxDecoration(
              color:
                  enabled
                      ? AppColors.gfDarkBackground
                      : AppColors.gfDarkBackground.withValues(alpha: 128),
              borderRadius: BorderRadius.circular(8),
              border: Border.all(
                color: errorText != null ? Colors.red : AppColors.gfGrayBorder,
              ),
            ),
            child: Row(
              children: [
                if (prefixIcon != null) ...[
                  prefixIcon!,
                  const SizedBox(width: 12),
                ],
                Expanded(
                  child: Text(
                    _getDisplayText(),
                    style: TextStyle(
                      color:
                          value != null
                              ? AppColors.gfOffWhite
                              : AppColors.gfGrayText,
                      fontSize: 16,
                    ),
                  ),
                ),
                Icon(
                  Icons.keyboard_arrow_down,
                  color:
                      enabled
                          ? AppColors.gfGrayText
                          : AppColors.gfGrayText.withValues(alpha: 128),
                ),
              ],
            ),
          ),
        ),
        if (errorText != null) ...[
          const SizedBox(height: 4),
          Text(
            errorText!,
            style: const TextStyle(color: Colors.red, fontSize: 12),
          ),
        ],
      ],
    );
  }

  String _getDisplayText() {
    if (value != null) {
      final item = items.firstWhere((item) => item.value == value);
      return item.label;
    }
    return hint ?? 'Select an option';
  }

  void _showPicker(BuildContext context) {
    showModalBottomSheet(
      context: context,
      backgroundColor: Colors.transparent,
      builder:
          (context) => GFPickerBottomSheet(
            title: label,
            items: items,
            selectedValue: value,
            onSelected: (selectedValue) {
              onChanged(selectedValue);
              Navigator.of(context).pop();
            },
          ),
    );
  }
}

/// Picker item configuration
class GFPickerItem<T> {
  final T value;
  final String label;
  final Widget? icon;
  final String? subtitle;

  const GFPickerItem({
    required this.value,
    required this.label,
    this.icon,
    this.subtitle,
  });
}

/// Bottom sheet picker
class GFPickerBottomSheet<T> extends StatelessWidget {
  final String title;
  final List<GFPickerItem<T>> items;
  final T? selectedValue;
  final void Function(T) onSelected;

  const GFPickerBottomSheet({
    super.key,
    required this.title,
    required this.items,
    this.selectedValue,
    required this.onSelected,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      decoration: const BoxDecoration(
        color: AppColors.gfDarkBackground,
        borderRadius: BorderRadius.vertical(top: Radius.circular(20)),
      ),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          // Header
          Container(
            padding: const EdgeInsets.all(16),
            decoration: const BoxDecoration(
              border: Border(bottom: BorderSide(color: AppColors.gfGrayBorder)),
            ),
            child: Row(
              children: [
                Text(
                  title,
                  style: const TextStyle(
                    color: AppColors.gfOffWhite,
                    fontSize: 18,
                    fontWeight: FontWeight.w600,
                  ),
                ),
                const Spacer(),
                IconButton(
                  icon: const Icon(Icons.close, color: AppColors.gfGrayText),
                  onPressed: () => Navigator.of(context).pop(),
                ),
              ],
            ),
          ),

          // Items
          Flexible(
            child: ListView.builder(
              shrinkWrap: true,
              itemCount: items.length,
              itemBuilder: (context, index) {
                final item = items[index];
                final isSelected = item.value == selectedValue;

                return ListTile(
                  leading: item.icon,
                  title: Text(
                    item.label,
                    style: TextStyle(
                      color:
                          isSelected ? AppColors.gfGreen : AppColors.gfOffWhite,
                      fontWeight:
                          isSelected ? FontWeight.w600 : FontWeight.normal,
                    ),
                  ),
                  subtitle:
                      item.subtitle != null
                          ? Text(
                            item.subtitle!,
                            style: const TextStyle(color: AppColors.gfGrayText),
                          )
                          : null,
                  trailing:
                      isSelected
                          ? const Icon(Icons.check, color: AppColors.gfGreen)
                          : null,
                  onTap: () => onSelected(item.value),
                );
              },
            ),
          ),
        ],
      ),
    );
  }
}

/// Multi-select picker
class GFMultiPicker<T> extends StatelessWidget {
  final String label;
  final List<T> values;
  final List<GFPickerItem<T>> items;
  final void Function(List<T>) onChanged;
  final String? hint;
  final Widget? prefixIcon;
  final bool enabled;
  final String? errorText;
  final int? maxSelections;

  const GFMultiPicker({
    super.key,
    required this.label,
    required this.values,
    required this.items,
    required this.onChanged,
    this.hint,
    this.prefixIcon,
    this.enabled = true,
    this.errorText,
    this.maxSelections,
  });

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          label,
          style: const TextStyle(
            color: AppColors.gfOffWhite,
            fontSize: 14,
            fontWeight: FontWeight.w500,
          ),
        ),
        const SizedBox(height: 8),
        GestureDetector(
          onTap: enabled ? () => _showPicker(context) : null,
          child: Container(
            padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 14),
            decoration: BoxDecoration(
              color:
                  enabled
                      ? AppColors.gfDarkBackground
                      : AppColors.gfDarkBackground.withValues(alpha: 128),
              borderRadius: BorderRadius.circular(8),
              border: Border.all(
                color: errorText != null ? Colors.red : AppColors.gfGrayBorder,
              ),
            ),
            child: Row(
              children: [
                if (prefixIcon != null) ...[
                  prefixIcon!,
                  const SizedBox(width: 12),
                ],
                Expanded(
                  child: Text(
                    _getDisplayText(),
                    style: TextStyle(
                      color:
                          values.isNotEmpty
                              ? AppColors.gfOffWhite
                              : AppColors.gfGrayText,
                      fontSize: 16,
                    ),
                  ),
                ),
                Icon(
                  Icons.keyboard_arrow_down,
                  color:
                      enabled
                          ? AppColors.gfGrayText
                          : AppColors.gfGrayText.withValues(alpha: 128),
                ),
              ],
            ),
          ),
        ),
        if (errorText != null) ...[
          const SizedBox(height: 4),
          Text(
            errorText!,
            style: const TextStyle(color: Colors.red, fontSize: 12),
          ),
        ],
      ],
    );
  }

  String _getDisplayText() {
    if (values.isEmpty) {
      return hint ?? 'Select options';
    }

    if (values.length == 1) {
      final item = items.firstWhere((item) => item.value == values.first);
      return item.label;
    }

    return '${values.length} selected';
  }

  void _showPicker(BuildContext context) {
    showModalBottomSheet(
      context: context,
      backgroundColor: Colors.transparent,
      builder:
          (context) => GFMultiPickerBottomSheet(
            title: label,
            items: items,
            selectedValues: values,
            maxSelections: maxSelections,
            onChanged: onChanged,
          ),
    );
  }
}

/// Multi-select bottom sheet
class GFMultiPickerBottomSheet<T> extends StatefulWidget {
  final String title;
  final List<GFPickerItem<T>> items;
  final List<T> selectedValues;
  final int? maxSelections;
  final void Function(List<T>) onChanged;

  const GFMultiPickerBottomSheet({
    super.key,
    required this.title,
    required this.items,
    required this.selectedValues,
    this.maxSelections,
    required this.onChanged,
  });

  @override
  State<GFMultiPickerBottomSheet<T>> createState() =>
      _GFMultiPickerBottomSheetState<T>();
}

class _GFMultiPickerBottomSheetState<T>
    extends State<GFMultiPickerBottomSheet<T>> {
  late List<T> _selectedValues;

  @override
  void initState() {
    super.initState();
    _selectedValues = List.from(widget.selectedValues);
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      decoration: const BoxDecoration(
        color: AppColors.gfDarkBackground,
        borderRadius: BorderRadius.vertical(top: Radius.circular(20)),
      ),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          // Header
          Container(
            padding: const EdgeInsets.all(16),
            decoration: const BoxDecoration(
              border: Border(bottom: BorderSide(color: AppColors.gfGrayBorder)),
            ),
            child: Row(
              children: [
                Text(
                  widget.title,
                  style: const TextStyle(
                    color: AppColors.gfOffWhite,
                    fontSize: 18,
                    fontWeight: FontWeight.w600,
                  ),
                ),
                const Spacer(),
                GFButton(
                  text: 'Done',
                  type: GFButtonType.primary,
                  height: 32,
                  onPressed: () {
                    widget.onChanged(_selectedValues);
                    Navigator.of(context).pop();
                  },
                ),
              ],
            ),
          ),

          // Items
          Flexible(
            child: ListView.builder(
              shrinkWrap: true,
              itemCount: widget.items.length,
              itemBuilder: (context, index) {
                final item = widget.items[index];
                final isSelected = _selectedValues.contains(item.value);
                final canSelect =
                    widget.maxSelections == null ||
                    _selectedValues.length < widget.maxSelections! ||
                    isSelected;

                return ListTile(
                  leading: item.icon,
                  title: Text(
                    item.label,
                    style: TextStyle(
                      color:
                          canSelect
                              ? AppColors.gfOffWhite
                              : AppColors.gfGrayText,
                      fontWeight:
                          isSelected ? FontWeight.w600 : FontWeight.normal,
                    ),
                  ),
                  subtitle:
                      item.subtitle != null
                          ? Text(
                            item.subtitle!,
                            style: const TextStyle(color: AppColors.gfGrayText),
                          )
                          : null,
                  trailing: Checkbox(
                    value: isSelected,
                    onChanged:
                        canSelect
                            ? (value) => _toggleSelection(item.value)
                            : null,
                    activeColor: AppColors.gfGreen,
                  ),
                  onTap: canSelect ? () => _toggleSelection(item.value) : null,
                );
              },
            ),
          ),
        ],
      ),
    );
  }

  void _toggleSelection(T value) {
    setState(() {
      if (_selectedValues.contains(value)) {
        _selectedValues.remove(value);
      } else {
        _selectedValues.add(value);
      }
    });
  }
}
