import 'package:flutter/material.dart';
import '../../theme/app_theme.dart';

/// GameFlex emoji picker component
///
/// A reusable emoji picker that provides consistent emoji selection
/// functionality across the app with GameFlex styling.
///
/// Example usage:
/// ```dart
/// GFEmojiPicker(
///   onEmojiSelected: (emoji) => _handleEmojiSelection(emoji),
///   allowedEmojis: ['👍', '❤️', '😂', '😮', '😢', '😡'],
/// )
/// ```
class GFEmojiPicker extends StatelessWidget {
  final void Function(String) onEmojiSelected;
  final List<String>? allowedEmojis;
  final String? title;
  final bool showSearch;
  final int crossAxisCount;

  const GFEmojiPicker({
    super.key,
    required this.onEmojiSelected,
    this.allowedEmojis,
    this.title,
    this.showSearch = false,
    this.crossAxisCount = 8,
  });

  // Default emoji set for reactions
  static const List<String> defaultReactionEmojis = [
    '👍',
    '👎',
    '❤️',
    '😂',
    '😮',
    '😢',
    '😡',
    '🔥',
    '👏',
    '🎉',
    '💯',
    '⚡',
    '💪',
    '🙌',
    '👌',
    '✨',
    '🚀',
    '💎',
    '🏆',
    '⭐',
    '💝',
    '🎯',
    '🔮',
    '🌟',
  ];

  @override
  Widget build(BuildContext context) {
    final emojis = allowedEmojis ?? defaultReactionEmojis;

    return Container(
      decoration: const BoxDecoration(
        color: AppColors.gfDarkBackground,
        borderRadius: BorderRadius.vertical(top: Radius.circular(20)),
      ),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          // Header
          Container(
            padding: const EdgeInsets.all(16),
            decoration: const BoxDecoration(
              border: Border(bottom: BorderSide(color: AppColors.gfGrayBorder)),
            ),
            child: Row(
              children: [
                Text(
                  title ?? 'Choose Reaction',
                  style: const TextStyle(
                    color: AppColors.gfOffWhite,
                    fontSize: 18,
                    fontWeight: FontWeight.w600,
                  ),
                ),
                const Spacer(),
                IconButton(
                  icon: const Icon(Icons.close, color: AppColors.gfGrayText),
                  onPressed: () => Navigator.of(context).pop(),
                ),
              ],
            ),
          ),

          // Search bar (if enabled)
          if (showSearch) ...[
            Padding(
              padding: const EdgeInsets.all(16),
              child: TextField(
                style: const TextStyle(color: AppColors.gfOffWhite),
                decoration: InputDecoration(
                  hintText: 'Search emojis...',
                  hintStyle: const TextStyle(color: AppColors.gfGrayText),
                  prefixIcon: const Icon(
                    Icons.search,
                    color: AppColors.gfGrayText,
                  ),
                  filled: true,
                  fillColor: AppColors.gfDarkBackground100,
                  border: OutlineInputBorder(
                    borderRadius: BorderRadius.circular(8),
                    borderSide: BorderSide.none,
                  ),
                ),
                onChanged: (value) {
                  // TODO: Implement search functionality
                },
              ),
            ),
          ],

          // Emoji grid
          Flexible(
            child: Container(
              constraints: const BoxConstraints(maxHeight: 300),
              padding: const EdgeInsets.all(16),
              child: GridView.builder(
                shrinkWrap: true,
                gridDelegate: SliverGridDelegateWithFixedCrossAxisCount(
                  crossAxisCount: crossAxisCount,
                  crossAxisSpacing: 8,
                  mainAxisSpacing: 8,
                ),
                itemCount: emojis.length,
                itemBuilder: (context, index) {
                  final emoji = emojis[index];
                  return GestureDetector(
                    onTap: () {
                      onEmojiSelected(emoji);
                      Navigator.of(context).pop();
                    },
                    child: Container(
                      decoration: BoxDecoration(
                        color: AppColors.gfDarkBackground100,
                        borderRadius: BorderRadius.circular(8),
                        border: Border.all(
                          color: AppColors.gfGrayBorder,
                          width: 0.5,
                        ),
                      ),
                      child: Center(
                        child: Text(
                          emoji,
                          style: const TextStyle(fontSize: 24),
                        ),
                      ),
                    ),
                  );
                },
              ),
            ),
          ),
        ],
      ),
    );
  }
}

/// Compact emoji picker for quick reactions
class GFQuickEmojiPicker extends StatelessWidget {
  final void Function(String) onEmojiSelected;
  final List<String>? quickEmojis;

  const GFQuickEmojiPicker({
    super.key,
    required this.onEmojiSelected,
    this.quickEmojis,
  });

  static const List<String> defaultQuickEmojis = [
    '👍',
    '❤️',
    '😂',
    '😮',
    '😢',
    '😡',
  ];

  @override
  Widget build(BuildContext context) {
    final emojis = quickEmojis ?? defaultQuickEmojis;

    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
      decoration: BoxDecoration(
        color: AppColors.gfDarkBackground,
        borderRadius: BorderRadius.circular(25),
        border: Border.all(color: AppColors.gfGrayBorder),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 77), // 0.3 opacity
            blurRadius: 10,
            offset: const Offset(0, 4),
          ),
        ],
      ),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children:
            emojis.map((emoji) {
              return GestureDetector(
                onTap: () => onEmojiSelected(emoji),
                child: Container(
                  margin: const EdgeInsets.symmetric(horizontal: 4),
                  padding: const EdgeInsets.all(8),
                  decoration: BoxDecoration(
                    color: Colors.transparent,
                    borderRadius: BorderRadius.circular(20),
                  ),
                  child: Text(emoji, style: const TextStyle(fontSize: 20)),
                ),
              );
            }).toList(),
      ),
    );
  }
}

/// Emoji picker dialog
class GFEmojiPickerDialog extends StatelessWidget {
  final void Function(String) onEmojiSelected;
  final List<String>? allowedEmojis;
  final String? title;

  const GFEmojiPickerDialog({
    super.key,
    required this.onEmojiSelected,
    this.allowedEmojis,
    this.title,
  });

  @override
  Widget build(BuildContext context) {
    return Dialog(
      backgroundColor: Colors.transparent,
      child: Container(
        constraints: const BoxConstraints(maxWidth: 400, maxHeight: 500),
        decoration: BoxDecoration(
          color: AppColors.gfDarkBackground,
          borderRadius: BorderRadius.circular(12),
        ),
        child: GFEmojiPicker(
          onEmojiSelected: onEmojiSelected,
          allowedEmojis: allowedEmojis,
          title: title,
          showSearch: true,
        ),
      ),
    );
  }

  static Future<String?> show(
    BuildContext context, {
    List<String>? allowedEmojis,
    String? title,
  }) {
    return showDialog<String>(
      context: context,
      builder:
          (context) => GFEmojiPickerDialog(
            onEmojiSelected: (emoji) => Navigator.of(context).pop(emoji),
            allowedEmojis: allowedEmojis,
            title: title,
          ),
    );
  }
}

/// Emoji picker bottom sheet
class GFEmojiPickerBottomSheet extends StatelessWidget {
  final void Function(String) onEmojiSelected;
  final List<String>? allowedEmojis;
  final String? title;

  const GFEmojiPickerBottomSheet({
    super.key,
    required this.onEmojiSelected,
    this.allowedEmojis,
    this.title,
  });

  @override
  Widget build(BuildContext context) {
    return GFEmojiPicker(
      onEmojiSelected: onEmojiSelected,
      allowedEmojis: allowedEmojis,
      title: title,
      showSearch: true,
    );
  }

  static Future<String?> show(
    BuildContext context, {
    List<String>? allowedEmojis,
    String? title,
  }) {
    return showModalBottomSheet<String>(
      context: context,
      backgroundColor: Colors.transparent,
      isScrollControlled: true,
      builder:
          (context) => GFEmojiPickerBottomSheet(
            onEmojiSelected: (emoji) => Navigator.of(context).pop(emoji),
            allowedEmojis: allowedEmojis,
            title: title,
          ),
    );
  }
}

/// Utility functions for emoji handling
class GFEmojiUtils {
  /// Check if a string is a valid emoji
  static bool isEmoji(String text) {
    // Simple emoji validation - in a real app you might want more sophisticated validation
    final emojiRegex = RegExp(
      r'[\u{1F600}-\u{1F64F}]|[\u{1F300}-\u{1F5FF}]|[\u{1F680}-\u{1F6FF}]|[\u{1F1E0}-\u{1F1FF}]|[\u{2600}-\u{26FF}]|[\u{2700}-\u{27BF}]',
      unicode: true,
    );
    return emojiRegex.hasMatch(text);
  }

  /// Get emoji category
  static String getEmojiCategory(String emoji) {
    // Simple categorization - in a real app you might want more sophisticated categorization
    const faces = ['😀', '😃', '😄', '😁', '😆', '😅', '😂', '🤣', '😊', '😇'];
    const hearts = ['❤️', '🧡', '💛', '💚', '💙', '💜', '🖤', '🤍', '🤎', '💔'];
    const hands = ['👍', '👎', '👌', '✌️', '🤞', '🤟', '🤘', '🤙', '👈', '👉'];

    if (faces.contains(emoji)) return 'Faces';
    if (hearts.contains(emoji)) return 'Hearts';
    if (hands.contains(emoji)) return 'Hands';
    return 'Other';
  }

  /// Filter emojis by category
  static List<String> filterEmojisByCategory(
    List<String> emojis,
    String category,
  ) {
    return emojis
        .where((emoji) => getEmojiCategory(emoji) == category)
        .toList();
  }
}
