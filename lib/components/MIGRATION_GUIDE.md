# Component Migration Guide

This guide shows how to migrate from the old widget structure to the new component system.

## 📦 Import Changes

### Before (Old Structure)
```dart
// Multiple individual imports
import '../widgets/common/gf_button.dart';
import '../widgets/common/gf_text_field.dart';
import '../widgets/post_card.dart';
import '../widgets/reaction_bar.dart';
import '../widgets/custom_bottom_navigation.dart';
import '../widgets/feed_top_overlay.dart';
import '../widgets/feed_bottom_overlay.dart';
```

### After (New Structure)
```dart
// Single import for all components
import 'package:gameflex_mobile/components/index.dart';

// Or import specific categories
import 'package:gameflex_mobile/components/core/index.dart';
import 'package:gameflex_mobile/components/feed/index.dart';
import 'package:gameflex_mobile/components/layout/index.dart';
```

## 🔄 Component Mapping

### Core Components
| Old Path | New Path | New Component Name |
|----------|----------|-------------------|
| `widgets/common/gf_button.dart` | `components/core/gf_button.dart` | `GFButton` |
| `widgets/common/gf_text_field.dart` | `components/core/gf_text_field.dart` | `GFTextField` |
| N/A | `components/core/gf_avatar.dart` | `GFAvatar` |
| N/A | `components/core/gf_card.dart` | `GFCard` |
| N/A | `components/core/gf_loading.dart` | `GFLoadingIndicator` |

### Layout Components
| Old Path | New Path | New Component Name |
|----------|----------|-------------------|
| `widgets/custom_bottom_navigation.dart` | `components/layout/gf_bottom_navigation.dart` | `GFSimpleBottomNavigation` |
| N/A | `components/layout/gf_app_bar.dart` | `GFAppBar` |
| N/A | `components/layout/gf_overlay.dart` | `GFTopOverlay`, `GFBottomOverlay` |

### Feed Components
| Old Path | New Path | New Component Name |
|----------|----------|-------------------|
| `widgets/post_card.dart` | `components/feed/gf_post_card.dart` | `GFPostCard` |
| `widgets/reaction_bar.dart` | `components/feed/gf_reaction_bar.dart` | `GFReactionBar` |
| `widgets/feed_top_overlay.dart` | `components/feed/gf_feed_overlay.dart` | `GFFeedTopOverlay` |
| `widgets/feed_bottom_overlay.dart` | `components/feed/gf_feed_overlay.dart` | `GFFeedBottomOverlay` |

### Media Components
| Old Path | New Path | New Component Name |
|----------|----------|-------------------|
| `widgets/lazy_image.dart` | `components/media/gf_image.dart` | `GFLazyImage` |
| `widgets/video_player_widget.dart` | `components/media/gf_video_player.dart` | `GFVideoPlayer` |

### Form Components
| Old Path | New Path | New Component Name |
|----------|----------|-------------------|
| `widgets/emoji_picker_dialog.dart` | `components/forms/gf_emoji_picker.dart` | `GFEmojiPicker` |
| `widgets/channel_selection_widget.dart` | `components/channels/gf_channel_selection.dart` | `GFChannelSelectionWidget` |

### Auth Components
| Old Path | New Path | New Component Name |
|----------|----------|-------------------|
| `widgets/apple_signin_button.dart` | `components/auth/gf_signin_buttons.dart` | `GFAppleSignInButton` |
| `widgets/xbox_signin_button.dart` | `components/auth/gf_signin_buttons.dart` | `GFXboxSignInButton` |
| `widgets/linked_account_widget.dart` | `components/auth/gf_account_linking.dart` | `GFLinkedAccountWidget` |

### Channel Components
| Old Path | New Path | New Component Name |
|----------|----------|-------------------|
| `widgets/channel_card.dart` | `components/channels/gf_channel_card.dart` | `GFChannelCard` |

## 🛠️ Migration Steps

### Step 1: Update Imports
Replace old widget imports with the new component imports:

```dart
// Old
import '../widgets/common/gf_button.dart';

// New
import 'package:gameflex_mobile/components/index.dart';
```

### Step 2: Update Component Usage
Most components maintain the same API, but some have enhanced features:

```dart
// Old
ElevatedButton(
  onPressed: onPressed,
  style: ElevatedButton.styleFrom(
    backgroundColor: AppColors.gfGreen,
    foregroundColor: Colors.black,
  ),
  child: Text('Button'),
)

// New
GFButton(
  text: 'Button',
  onPressed: onPressed,
  type: GFButtonType.primary,
)
```

### Step 3: Leverage New Features
Take advantage of new component features:

```dart
// Enhanced button with loading state
GFButton(
  text: 'Sign In',
  onPressed: _signIn,
  isLoading: _isLoading,
  icon: Icons.login,
)

// Enhanced avatar with status
GFAvatarWithStatus(
  imageUrl: user.avatarUrl,
  displayName: user.name,
  isOnline: user.isOnline,
)
```

## 🎯 Example Migration

### Before: Login Screen
```dart
import 'package:flutter/material.dart';
import '../widgets/common/gf_button.dart';
import '../widgets/common/gf_text_field.dart';
import '../widgets/apple_signin_button.dart';
import '../theme/app_theme.dart';

class LoginScreen extends StatelessWidget {
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: Column(
        children: [
          GFTextField(
            label: 'Email',
            controller: _emailController,
          ),
          GFTextField(
            label: 'Password',
            obscureText: true,
            controller: _passwordController,
          ),
          GFButton(
            text: 'Sign In',
            onPressed: _signIn,
          ),
          AppleSignInButton(
            onPressed: _signInWithApple,
          ),
        ],
      ),
    );
  }
}
```

### After: Login Screen
```dart
import 'package:flutter/material.dart';
import 'package:gameflex_mobile/components/index.dart';

class LoginScreen extends StatelessWidget {
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: Column(
        children: [
          GFTextField(
            label: 'Email',
            controller: _emailController,
            keyboardType: TextInputType.emailAddress,
            validator: _validateEmail,
          ),
          GFTextField(
            label: 'Password',
            obscureText: true,
            controller: _passwordController,
            validator: _validatePassword,
          ),
          GFButton(
            text: 'Sign In',
            onPressed: _signIn,
            isLoading: _isLoading,
            type: GFButtonType.primary,
          ),
          GFAppleSignInButton(
            onPressed: _signInWithApple,
            isLoading: _isAppleLoading,
          ),
        ],
      ),
    );
  }
}
```

## ✅ Benefits of Migration

1. **Cleaner Imports**: Single import for all components
2. **Enhanced Features**: Loading states, validation, better styling
3. **Consistency**: All components follow the same design patterns
4. **Documentation**: Comprehensive docs for each component
5. **Maintainability**: Easier to update and extend components
6. **Type Safety**: Better TypeScript/Dart type definitions

## 🚨 Breaking Changes

### Component Renames
- `CustomBottomNavigation` → `GFSimpleBottomNavigation`
- `AppleSignInButton` → `GFAppleSignInButton`
- `XboxSignInButton` → `GFXboxSignInButton`

### API Changes
- Some props have been renamed for consistency
- New required props for enhanced functionality
- Deprecated props have been removed

### Styling Changes
- Components now use theme colors by default
- Custom styling props have been standardized
- Some hardcoded styles have been made configurable

## 🔍 Testing Migration

1. **Import Check**: Ensure all imports resolve correctly
2. **Visual Check**: Verify components render as expected
3. **Functionality Check**: Test all interactive features
4. **Theme Check**: Confirm theme colors are applied
5. **Responsive Check**: Test on different screen sizes

## 📞 Support

If you encounter issues during migration:
1. Check the component documentation in `README.md`
2. Look for similar usage patterns in other screens
3. Refer to the component source code for available props
4. Test changes incrementally to isolate issues
