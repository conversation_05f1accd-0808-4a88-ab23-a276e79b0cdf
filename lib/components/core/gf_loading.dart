import 'package:flutter/material.dart';
import '../../theme/app_theme.dart';

/// GameFlex loading components
/// 
/// A collection of loading indicators that follow the GameFlex design system.
/// Provides consistent loading states across the app.

/// Standard circular loading indicator
class GFLoadingIndicator extends StatelessWidget {
  final double size;
  final Color? color;
  final double strokeWidth;

  const GFLoadingIndicator({
    super.key,
    this.size = 24,
    this.color,
    this.strokeWidth = 2,
  });

  @override
  Widget build(BuildContext context) {
    return SizedBox(
      width: size,
      height: size,
      child: CircularProgressIndicator(
        strokeWidth: strokeWidth,
        valueColor: AlwaysStoppedAnimation<Color>(
          color ?? AppColors.gfGreen,
        ),
      ),
    );
  }
}

/// Full screen loading overlay
class GFLoadingOverlay extends StatelessWidget {
  final String? message;
  final bool isVisible;

  const GFLoadingOverlay({
    super.key,
    this.message,
    this.isVisible = true,
  });

  @override
  Widget build(BuildContext context) {
    if (!isVisible) return const SizedBox.shrink();

    return Container(
      color: Colors.black.withValues(alpha: 128), // 0.5 opacity
      child: Center(
        child: Container(
          padding: const EdgeInsets.all(24),
          decoration: BoxDecoration(
            color: AppColors.gfDarkBackground,
            borderRadius: BorderRadius.circular(12),
          ),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              const GFLoadingIndicator(size: 32),
              if (message != null) ...[
                const SizedBox(height: 16),
                Text(
                  message!,
                  style: const TextStyle(
                    color: AppColors.gfOffWhite,
                    fontSize: 16,
                  ),
                  textAlign: TextAlign.center,
                ),
              ],
            ],
          ),
        ),
      ),
    );
  }
}

/// Loading state for lists
class GFListLoading extends StatelessWidget {
  final int itemCount;
  final double itemHeight;

  const GFListLoading({
    super.key,
    this.itemCount = 3,
    this.itemHeight = 80,
  });

  @override
  Widget build(BuildContext context) {
    return Column(
      children: List.generate(
        itemCount,
        (index) => Container(
          height: itemHeight,
          margin: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
          child: const GFShimmerCard(),
        ),
      ),
    );
  }
}

/// Shimmer loading effect for cards
class GFShimmerCard extends StatefulWidget {
  final double? width;
  final double? height;
  final BorderRadius? borderRadius;

  const GFShimmerCard({
    super.key,
    this.width,
    this.height,
    this.borderRadius,
  });

  @override
  State<GFShimmerCard> createState() => _GFShimmerCardState();
}

class _GFShimmerCardState extends State<GFShimmerCard>
    with SingleTickerProviderStateMixin {
  late AnimationController _animationController;
  late Animation<double> _animation;

  @override
  void initState() {
    super.initState();
    _animationController = AnimationController(
      duration: const Duration(milliseconds: 1500),
      vsync: this,
    );
    _animation = Tween<double>(begin: -1.0, end: 2.0).animate(
      CurvedAnimation(parent: _animationController, curve: Curves.easeInOut),
    );
    _animationController.repeat();
  }

  @override
  void dispose() {
    _animationController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return AnimatedBuilder(
      animation: _animation,
      builder: (context, child) {
        return Container(
          width: widget.width,
          height: widget.height,
          decoration: BoxDecoration(
            borderRadius: widget.borderRadius ?? BorderRadius.circular(12),
            gradient: LinearGradient(
              begin: Alignment.centerLeft,
              end: Alignment.centerRight,
              stops: [
                (_animation.value - 1).clamp(0.0, 1.0),
                _animation.value.clamp(0.0, 1.0),
                (_animation.value + 1).clamp(0.0, 1.0),
              ],
              colors: [
                AppColors.gfDarkBackground,
                AppColors.gfDarkBackground.withValues(alpha: 179), // 0.7 opacity
                AppColors.gfDarkBackground,
              ],
            ),
          ),
        );
      },
    );
  }
}

/// Loading state for media content
class GFMediaLoading extends StatelessWidget {
  final double aspectRatio;
  final BorderRadius? borderRadius;

  const GFMediaLoading({
    super.key,
    this.aspectRatio = 16 / 9,
    this.borderRadius,
  });

  @override
  Widget build(BuildContext context) {
    return AspectRatio(
      aspectRatio: aspectRatio,
      child: Container(
        decoration: BoxDecoration(
          color: AppColors.gfDarkBackground,
          borderRadius: borderRadius ?? BorderRadius.circular(12),
        ),
        child: const Center(
          child: GFLoadingIndicator(size: 32),
        ),
      ),
    );
  }
}

/// Loading state for text content
class GFTextLoading extends StatelessWidget {
  final double width;
  final double height;
  final BorderRadius? borderRadius;

  const GFTextLoading({
    super.key,
    this.width = 100,
    this.height = 16,
    this.borderRadius,
  });

  @override
  Widget build(BuildContext context) {
    return GFShimmerCard(
      width: width,
      height: height,
      borderRadius: borderRadius ?? BorderRadius.circular(4),
    );
  }
}

/// Loading state for buttons
class GFButtonLoading extends StatelessWidget {
  final double width;
  final double height;

  const GFButtonLoading({
    super.key,
    this.width = 120,
    this.height = 48,
  });

  @override
  Widget build(BuildContext context) {
    return GFShimmerCard(
      width: width,
      height: height,
      borderRadius: BorderRadius.circular(height / 2),
    );
  }
}
