import 'package:flutter/material.dart';
import '../../theme/app_theme.dart';

enum GFButtonType { primary, secondary, outline, text, danger }

/// GameFlex custom button component
/// 
/// A reusable button component that follows the GameFlex design system.
/// Supports multiple button types and consistent styling across the app.
/// 
/// Example usage:
/// ```dart
/// GFButton(
///   text: 'Sign In',
///   type: GFButtonType.primary,
///   onPressed: () => _handleSignIn(),
/// )
/// ```
class GFButton extends StatelessWidget {
  final String text;
  final VoidCallback? onPressed;
  final GFButtonType type;
  final double height;
  final double? width;
  final bool isEnabled;
  final IconData? icon;
  final bool isLoading;

  const GFButton({
    super.key,
    required this.text,
    this.onPressed,
    this.type = GFButtonType.primary,
    this.height = 48.0,
    this.width,
    this.isEnabled = true,
    this.icon,
    this.isLoading = false,
  });

  @override
  Widget build(BuildContext context) {
    switch (type) {
      case GFButtonType.primary:
        return _buildPrimaryButton();
      case GFButtonType.secondary:
        return _buildSecondaryButton();
      case GFButtonType.outline:
        return _buildOutlineButton();
      case GFButtonType.text:
        return _buildTextButton();
      case GFButtonType.danger:
        return _buildDangerButton();
    }
  }

  Widget _buildPrimaryButton() {
    return SizedBox(
      height: height,
      width: width,
      child: ElevatedButton(
        onPressed: isEnabled && !isLoading ? onPressed : null,
        style: ElevatedButton.styleFrom(
          backgroundColor: AppColors.gfGreen,
          foregroundColor: Colors.black,
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(height / 2),
          ),
          elevation: 0,
          disabledBackgroundColor: AppColors.gfGreen.withValues(alpha: 153),
          disabledForegroundColor: Colors.black.withValues(alpha: 153),
        ),
        child: _buildButtonContent(),
      ),
    );
  }

  Widget _buildSecondaryButton() {
    return SizedBox(
      height: height,
      width: width,
      child: ElevatedButton(
        onPressed: isEnabled && !isLoading ? onPressed : null,
        style: ElevatedButton.styleFrom(
          backgroundColor: AppColors.gfDarkBackground,
          foregroundColor: AppColors.gfOffWhite,
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(height / 2),
            side: const BorderSide(color: AppColors.gfGrayBorder),
          ),
          elevation: 0,
          disabledBackgroundColor: AppColors.gfDarkBackground.withValues(alpha: 153),
          disabledForegroundColor: AppColors.gfOffWhite.withValues(alpha: 153),
        ),
        child: _buildButtonContent(),
      ),
    );
  }

  Widget _buildOutlineButton() {
    return SizedBox(
      height: height,
      width: width,
      child: OutlinedButton(
        onPressed: isEnabled && !isLoading ? onPressed : null,
        style: OutlinedButton.styleFrom(
          foregroundColor: AppColors.gfGreen,
          side: const BorderSide(color: AppColors.gfGreen),
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(height / 2),
          ),
          disabledForegroundColor: AppColors.gfGreen.withValues(alpha: 153),
        ),
        child: _buildButtonContent(),
      ),
    );
  }

  Widget _buildTextButton() {
    return SizedBox(
      height: height,
      width: width,
      child: TextButton(
        onPressed: isEnabled && !isLoading ? onPressed : null,
        style: TextButton.styleFrom(
          foregroundColor: AppColors.gfGreen,
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(height / 2),
          ),
          disabledForegroundColor: AppColors.gfGreen.withValues(alpha: 153),
        ),
        child: _buildButtonContent(),
      ),
    );
  }

  Widget _buildDangerButton() {
    return SizedBox(
      height: height,
      width: width,
      child: ElevatedButton(
        onPressed: isEnabled && !isLoading ? onPressed : null,
        style: ElevatedButton.styleFrom(
          backgroundColor: Colors.red,
          foregroundColor: Colors.white,
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(height / 2),
          ),
          elevation: 0,
          disabledBackgroundColor: Colors.red.withValues(alpha: 153),
          disabledForegroundColor: Colors.white.withValues(alpha: 153),
        ),
        child: _buildButtonContent(),
      ),
    );
  }

  Widget _buildButtonContent() {
    if (isLoading) {
      return SizedBox(
        height: 20,
        width: 20,
        child: CircularProgressIndicator(
          strokeWidth: 2,
          valueColor: AlwaysStoppedAnimation<Color>(
            type == GFButtonType.primary ? Colors.black : AppColors.gfGreen,
          ),
        ),
      );
    }

    if (icon != null) {
      return Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          Icon(icon, size: 18),
          const SizedBox(width: 8),
          Text(
            text,
            style: const TextStyle(fontSize: 16, fontWeight: FontWeight.bold),
          ),
        ],
      );
    }

    return Text(
      text,
      style: const TextStyle(fontSize: 16, fontWeight: FontWeight.bold),
    );
  }
}
