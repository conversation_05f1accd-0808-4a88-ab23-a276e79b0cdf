import 'package:flutter/material.dart';
import '../../theme/app_theme.dart';

/// GameFlex card component
/// 
/// A reusable card component that follows the GameFlex design system.
/// Provides consistent styling and interaction patterns across the app.
/// 
/// Example usage:
/// ```dart
/// GFCard(
///   child: Column(
///     children: [
///       Text('Card Title'),
///       Text('Card content'),
///     ],
///   ),
///   onTap: () => _handleTap(),
/// )
/// ```
class GFCard extends StatelessWidget {
  final Widget child;
  final VoidCallback? onTap;
  final VoidCallback? onLongPress;
  final EdgeInsetsGeometry? padding;
  final EdgeInsetsGeometry? margin;
  final Color? backgroundColor;
  final double? elevation;
  final BorderRadius? borderRadius;
  final Border? border;
  final bool showShadow;

  const GFCard({
    super.key,
    required this.child,
    this.onTap,
    this.onLongPress,
    this.padding,
    this.margin,
    this.backgroundColor,
    this.elevation,
    this.borderRadius,
    this.border,
    this.showShadow = false,
  });

  @override
  Widget build(BuildContext context) {
    final cardBorderRadius = borderRadius ?? BorderRadius.circular(12);
    
    return Container(
      margin: margin ?? const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
      decoration: BoxDecoration(
        color: backgroundColor ?? AppColors.gfDarkBackground,
        borderRadius: cardBorderRadius,
        border: border,
        boxShadow: showShadow
            ? [
                BoxShadow(
                  color: Colors.black.withValues(alpha: 51), // 0.2 opacity
                  blurRadius: 8,
                  offset: const Offset(0, 2),
                ),
              ]
            : null,
      ),
      child: Material(
        color: Colors.transparent,
        child: InkWell(
          onTap: onTap,
          onLongPress: onLongPress,
          borderRadius: cardBorderRadius,
          child: Padding(
            padding: padding ?? const EdgeInsets.all(16),
            child: child,
          ),
        ),
      ),
    );
  }
}

/// A specialized card for list items
class GFListCard extends StatelessWidget {
  final Widget leading;
  final Widget title;
  final Widget? subtitle;
  final Widget? trailing;
  final VoidCallback? onTap;
  final VoidCallback? onLongPress;
  final EdgeInsetsGeometry? margin;
  final bool showDivider;

  const GFListCard({
    super.key,
    required this.leading,
    required this.title,
    this.subtitle,
    this.trailing,
    this.onTap,
    this.onLongPress,
    this.margin,
    this.showDivider = false,
  });

  @override
  Widget build(BuildContext context) {
    return GFCard(
      margin: margin,
      onTap: onTap,
      onLongPress: onLongPress,
      padding: const EdgeInsets.all(12),
      child: Column(
        children: [
          Row(
            children: [
              leading,
              const SizedBox(width: 12),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    title,
                    if (subtitle != null) ...[
                      const SizedBox(height: 4),
                      subtitle!,
                    ],
                  ],
                ),
              ),
              if (trailing != null) ...[
                const SizedBox(width: 12),
                trailing!,
              ],
            ],
          ),
          if (showDivider) ...[
            const SizedBox(height: 12),
            Divider(
              color: AppColors.gfGrayBorder,
              height: 1,
            ),
          ],
        ],
      ),
    );
  }
}

/// A specialized card for media content
class GFMediaCard extends StatelessWidget {
  final Widget media;
  final Widget? header;
  final Widget? footer;
  final VoidCallback? onTap;
  final VoidCallback? onLongPress;
  final EdgeInsetsGeometry? margin;
  final double? aspectRatio;

  const GFMediaCard({
    super.key,
    required this.media,
    this.header,
    this.footer,
    this.onTap,
    this.onLongPress,
    this.margin,
    this.aspectRatio,
  });

  @override
  Widget build(BuildContext context) {
    return GFCard(
      margin: margin,
      onTap: onTap,
      onLongPress: onLongPress,
      padding: EdgeInsets.zero,
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          if (header != null) ...[
            Padding(
              padding: const EdgeInsets.all(12),
              child: header!,
            ),
          ],
          ClipRRect(
            borderRadius: BorderRadius.vertical(
              top: header != null ? Radius.zero : const Radius.circular(12),
              bottom: footer != null ? Radius.zero : const Radius.circular(12),
            ),
            child: aspectRatio != null
                ? AspectRatio(
                    aspectRatio: aspectRatio!,
                    child: media,
                  )
                : media,
          ),
          if (footer != null) ...[
            Padding(
              padding: const EdgeInsets.all(12),
              child: footer!,
            ),
          ],
        ],
      ),
    );
  }
}

/// A specialized card for empty states
class GFEmptyCard extends StatelessWidget {
  final IconData icon;
  final String title;
  final String? subtitle;
  final Widget? action;
  final EdgeInsetsGeometry? margin;

  const GFEmptyCard({
    super.key,
    required this.icon,
    required this.title,
    this.subtitle,
    this.action,
    this.margin,
  });

  @override
  Widget build(BuildContext context) {
    return GFCard(
      margin: margin,
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            icon,
            size: 64,
            color: AppColors.gfGrayText,
          ),
          const SizedBox(height: 16),
          Text(
            title,
            style: const TextStyle(
              color: AppColors.gfOffWhite,
              fontSize: 18,
              fontWeight: FontWeight.w600,
            ),
            textAlign: TextAlign.center,
          ),
          if (subtitle != null) ...[
            const SizedBox(height: 8),
            Text(
              subtitle!,
              style: const TextStyle(
                color: AppColors.gfGrayText,
                fontSize: 14,
              ),
              textAlign: TextAlign.center,
            ),
          ],
          if (action != null) ...[
            const SizedBox(height: 24),
            action!,
          ],
        ],
      ),
    );
  }
}
