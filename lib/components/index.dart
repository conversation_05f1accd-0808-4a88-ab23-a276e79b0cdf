// GameFlex Components
// 
// This is the main barrel export file for all GameFlex components.
// Import this file to access all components in the design system.
// 
// Example usage:
// ```dart
// import 'package:gameflex_mobile/components/index.dart';
// 
// // Now you can use any component:
// GFButton(text: 'Click me', onPressed: () {})
// GFTextField(label: 'Email')
// GFPostCard(post: myPost)
// ```

// Core UI Components
export 'core/index.dart';

// Layout Components  
export 'layout/index.dart';

// Media Components
export 'media/index.dart';

// Feed Components
export 'feed/index.dart';

// Form Components
export 'forms/index.dart';

// Authentication Components
export 'auth/index.dart';

// Channel Components
export 'channels/index.dart';
