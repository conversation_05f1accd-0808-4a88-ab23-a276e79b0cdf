import 'package:flutter/material.dart';
import '../../theme/app_theme.dart';

/// GameFlex bottom navigation component
/// 
/// A reusable bottom navigation bar that follows the GameFlex design system.
/// Supports customizable tabs and consistent styling.
/// 
/// Example usage:
/// ```dart
/// GFBottomNavigation(
///   currentIndex: _currentIndex,
///   onTap: (index) => setState(() => _currentIndex = index),
///   items: [
///     GFBottomNavigationItem(icon: Icons.home, label: 'Home'),
///     GFBottomNavigationItem(icon: Icons.add, label: 'Create'),
///     GFBottomNavigationItem(icon: Icons.person, label: 'Profile'),
///   ],
/// )
/// ```
class GFBottomNavigation extends StatelessWidget {
  final int currentIndex;
  final Function(int) onTap;
  final List<GFBottomNavigationItem> items;
  final Color? backgroundColor;
  final double height;

  const GFBottomNavigation({
    super.key,
    required this.currentIndex,
    required this.onTap,
    required this.items,
    this.backgroundColor,
    this.height = 50,
  });

  @override
  Widget build(BuildContext context) {
    return SafeArea(
      top: false,
      child: Container(
        height: height,
        decoration: BoxDecoration(
          color: backgroundColor ?? AppColors.darkBlue,
          border: const Border(
            top: BorderSide(color: AppColors.gfGrayBorder, width: 0.5),
          ),
        ),
        child: Padding(
          padding: const EdgeInsets.symmetric(vertical: 4.0),
          child: Row(
            mainAxisAlignment: MainAxisAlignment.spaceEvenly,
            children: items.asMap().entries.map((entry) {
              final index = entry.key;
              final item = entry.value;
              return _buildNavItem(
                item: item,
                index: index,
                isSelected: currentIndex == index,
              );
            }).toList(),
          ),
        ),
      ),
    );
  }

  Widget _buildNavItem({
    required GFBottomNavigationItem item,
    required int index,
    required bool isSelected,
  }) {
    return GestureDetector(
      onTap: () => onTap(index),
      behavior: HitTestBehavior.opaque,
      child: Container(
        padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            Icon(
              item.icon,
              size: item.size,
              color: isSelected ? AppColors.gfGreen : AppColors.gfGrayText,
            ),
            if (item.label != null) ...[
              const SizedBox(height: 2),
              Text(
                item.label!,
                style: TextStyle(
                  fontSize: 10,
                  color: isSelected ? AppColors.gfGreen : AppColors.gfGrayText,
                  fontWeight: isSelected ? FontWeight.w600 : FontWeight.normal,
                ),
              ),
            ],
          ],
        ),
      ),
    );
  }
}

/// Bottom navigation item configuration
class GFBottomNavigationItem {
  final IconData icon;
  final String? label;
  final double size;
  final Widget? badge;

  const GFBottomNavigationItem({
    required this.icon,
    this.label,
    this.size = 28,
    this.badge,
  });
}

/// Simplified bottom navigation (current implementation style)
class GFSimpleBottomNavigation extends StatelessWidget {
  final int currentIndex;
  final Function(int) onTap;

  const GFSimpleBottomNavigation({
    super.key,
    required this.currentIndex,
    required this.onTap,
  });

  @override
  Widget build(BuildContext context) {
    return SafeArea(
      top: false,
      child: Container(
        height: 50,
        decoration: const BoxDecoration(
          color: AppColors.darkBlue,
          border: Border(
            top: BorderSide(color: AppColors.gfGrayBorder, width: 0.5),
          ),
        ),
        child: Padding(
          padding: const EdgeInsets.symmetric(vertical: 4.0),
          child: Row(
            mainAxisAlignment: MainAxisAlignment.spaceEvenly,
            children: [
              _buildNavItem(icon: Icons.home, index: 0, size: 28),
              _buildNavItem(icon: Icons.add_circle_outline, index: 1, size: 32),
              _buildNavItem(icon: Icons.person, index: 2, size: 28),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildNavItem({
    required IconData icon,
    required int index,
    required double size,
  }) {
    final isSelected = currentIndex == index;

    return GestureDetector(
      onTap: () => onTap(index),
      behavior: HitTestBehavior.opaque,
      child: Container(
        padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
        child: Icon(
          icon,
          size: size,
          color: isSelected ? AppColors.gfGreen : AppColors.gfGrayText,
        ),
      ),
    );
  }
}

/// Floating action button style navigation
class GFFloatingBottomNavigation extends StatelessWidget {
  final int currentIndex;
  final Function(int) onTap;
  final List<GFBottomNavigationItem> items;

  const GFFloatingBottomNavigation({
    super.key,
    required this.currentIndex,
    required this.onTap,
    required this.items,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      margin: const EdgeInsets.all(16),
      padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
      decoration: BoxDecoration(
        color: AppColors.gfDarkBackground,
        borderRadius: BorderRadius.circular(25),
        border: Border.all(color: AppColors.gfGrayBorder),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 77), // 0.3 opacity
            blurRadius: 10,
            offset: const Offset(0, 4),
          ),
        ],
      ),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceEvenly,
        children: items.asMap().entries.map((entry) {
          final index = entry.key;
          final item = entry.value;
          final isSelected = currentIndex == index;
          
          return GestureDetector(
            onTap: () => onTap(index),
            child: Container(
              padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
              decoration: isSelected
                  ? BoxDecoration(
                      color: AppColors.gfGreen.withValues(alpha: 51), // 0.2 opacity
                      borderRadius: BorderRadius.circular(20),
                    )
                  : null,
              child: Icon(
                item.icon,
                size: item.size,
                color: isSelected ? AppColors.gfGreen : AppColors.gfGrayText,
              ),
            ),
          );
        }).toList(),
      ),
    );
  }
}
