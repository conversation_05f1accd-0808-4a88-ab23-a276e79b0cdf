import 'package:flutter/material.dart';
import '../../theme/app_theme.dart';
import '../core/gf_avatar.dart';

/// GameFlex app bar component
/// 
/// A reusable app bar component that follows the GameFlex design system.
/// Supports different styles and configurations for various screens.
/// 
/// Example usage:
/// ```dart
/// GFAppBar(
///   title: 'Home',
///   showBackButton: true,
///   actions: [
///     IconButton(icon: Icon(Icons.search), onPressed: () {}),
///   ],
/// )
/// ```
class GFAppBar extends StatelessWidget implements PreferredSizeWidget {
  final String? title;
  final Widget? titleWidget;
  final bool showBackButton;
  final VoidCallback? onBackPressed;
  final List<Widget>? actions;
  final Widget? leading;
  final Color? backgroundColor;
  final Color? foregroundColor;
  final double elevation;
  final bool centerTitle;
  final double? titleSpacing;
  final PreferredSizeWidget? bottom;

  const GFAppBar({
    super.key,
    this.title,
    this.titleWidget,
    this.showBackButton = false,
    this.onBackPressed,
    this.actions,
    this.leading,
    this.backgroundColor,
    this.foregroundColor,
    this.elevation = 0,
    this.centerTitle = true,
    this.titleSpacing,
    this.bottom,
  });

  @override
  Widget build(BuildContext context) {
    return AppBar(
      title: titleWidget ?? (title != null ? Text(title!) : null),
      leading: _buildLeading(context),
      actions: actions,
      backgroundColor: backgroundColor ?? AppColors.gfDarkBackground100,
      foregroundColor: foregroundColor ?? AppColors.gfOffWhite,
      elevation: elevation,
      centerTitle: centerTitle,
      titleSpacing: titleSpacing,
      bottom: bottom,
      titleTextStyle: const TextStyle(
        color: AppColors.gfOffWhite,
        fontSize: 18,
        fontWeight: FontWeight.w600,
      ),
    );
  }

  Widget? _buildLeading(BuildContext context) {
    if (leading != null) return leading;
    
    if (showBackButton) {
      return IconButton(
        icon: const Icon(Icons.arrow_back_ios),
        onPressed: onBackPressed ?? () => Navigator.of(context).pop(),
      );
    }
    
    return null;
  }

  @override
  Size get preferredSize => Size.fromHeight(
    kToolbarHeight + (bottom?.preferredSize.height ?? 0),
  );
}

/// Transparent app bar for overlay scenarios
class GFTransparentAppBar extends StatelessWidget implements PreferredSizeWidget {
  final String? title;
  final Widget? titleWidget;
  final bool showBackButton;
  final VoidCallback? onBackPressed;
  final List<Widget>? actions;
  final Widget? leading;
  final Color? foregroundColor;

  const GFTransparentAppBar({
    super.key,
    this.title,
    this.titleWidget,
    this.showBackButton = false,
    this.onBackPressed,
    this.actions,
    this.leading,
    this.foregroundColor,
  });

  @override
  Widget build(BuildContext context) {
    return AppBar(
      title: titleWidget ?? (title != null ? Text(title!) : null),
      leading: _buildLeading(context),
      actions: actions,
      backgroundColor: Colors.transparent,
      foregroundColor: foregroundColor ?? Colors.white,
      elevation: 0,
      titleTextStyle: TextStyle(
        color: foregroundColor ?? Colors.white,
        fontSize: 18,
        fontWeight: FontWeight.w600,
      ),
    );
  }

  Widget? _buildLeading(BuildContext context) {
    if (leading != null) return leading;
    
    if (showBackButton) {
      return IconButton(
        icon: const Icon(Icons.arrow_back_ios),
        onPressed: onBackPressed ?? () => Navigator.of(context).pop(),
      );
    }
    
    return null;
  }

  @override
  Size get preferredSize => const Size.fromHeight(kToolbarHeight);
}

/// App bar with user profile
class GFProfileAppBar extends StatelessWidget implements PreferredSizeWidget {
  final String? title;
  final String? userImageUrl;
  final String userName;
  final VoidCallback? onProfileTap;
  final List<Widget>? actions;

  const GFProfileAppBar({
    super.key,
    this.title,
    this.userImageUrl,
    required this.userName,
    this.onProfileTap,
    this.actions,
  });

  @override
  Widget build(BuildContext context) {
    return AppBar(
      title: title != null ? Text(title!) : null,
      leading: GFAvatar(
        imageUrl: userImageUrl,
        displayName: userName,
        radius: 18,
        onTap: onProfileTap,
      ),
      actions: actions,
      backgroundColor: AppColors.gfDarkBackground100,
      foregroundColor: AppColors.gfOffWhite,
      elevation: 0,
      titleTextStyle: const TextStyle(
        color: AppColors.gfOffWhite,
        fontSize: 18,
        fontWeight: FontWeight.w600,
      ),
    );
  }

  @override
  Size get preferredSize => const Size.fromHeight(kToolbarHeight);
}

/// Search app bar
class GFSearchAppBar extends StatelessWidget implements PreferredSizeWidget {
  final TextEditingController? controller;
  final String? hintText;
  final VoidCallback? onBackPressed;
  final void Function(String)? onChanged;
  final VoidCallback? onClear;
  final List<Widget>? actions;

  const GFSearchAppBar({
    super.key,
    this.controller,
    this.hintText,
    this.onBackPressed,
    this.onChanged,
    this.onClear,
    this.actions,
  });

  @override
  Widget build(BuildContext context) {
    return AppBar(
      leading: IconButton(
        icon: const Icon(Icons.arrow_back_ios),
        onPressed: onBackPressed ?? () => Navigator.of(context).pop(),
      ),
      title: TextField(
        controller: controller,
        onChanged: onChanged,
        autofocus: true,
        style: const TextStyle(color: AppColors.gfOffWhite),
        decoration: InputDecoration(
          hintText: hintText ?? 'Search...',
          hintStyle: const TextStyle(color: AppColors.gfGrayText),
          border: InputBorder.none,
          suffixIcon: controller?.text.isNotEmpty == true
              ? IconButton(
                  icon: const Icon(Icons.clear, color: AppColors.gfGrayText),
                  onPressed: onClear,
                )
              : null,
        ),
      ),
      actions: actions,
      backgroundColor: AppColors.gfDarkBackground100,
      foregroundColor: AppColors.gfOffWhite,
      elevation: 0,
    );
  }

  @override
  Size get preferredSize => const Size.fromHeight(kToolbarHeight);
}
