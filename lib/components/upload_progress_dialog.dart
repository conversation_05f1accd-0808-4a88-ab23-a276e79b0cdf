import 'dart:io';
import 'package:flutter/material.dart';
import '../theme/app_theme.dart';
import '../services/enhanced_media_service.dart';
import '../utils/app_logger.dart';

/// Dialog that shows upload progress and handles all upload states
class UploadProgressDialog extends StatefulWidget {
  final String title;
  final VoidCallback? onCancel;

  const UploadProgressDialog({
    super.key,
    this.title = 'Uploading Content',
    this.onCancel,
  });

  @override
  State<UploadProgressDialog> createState() => _UploadProgressDialogState();
}

class _UploadProgressDialogState extends State<UploadProgressDialog> {
  String _currentStatus = 'Preparing upload...';
  bool _isComplete = false;
  MediaUploadResult? _result;

  void updateStatus(String status) {
    if (mounted) {
      setState(() {
        _currentStatus = status;
      });
    }
  }

  void setResult(MediaUploadResult result) {
    if (mounted) {
      setState(() {
        _result = result;
        _isComplete = true;
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    return WillPopScope(
      onWillPop: () async {
        // Prevent dismissing during upload unless there's an error
        return _isComplete || _result != null;
      },
      child: AlertDialog(
        backgroundColor: AppColors.gfDarkBackground,
        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(16)),
        content: SizedBox(
          width: 300,
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              // Title
              Text(
                widget.title,
                style: const TextStyle(
                  color: AppColors.gfOffWhite,
                  fontSize: 18,
                  fontWeight: FontWeight.bold,
                ),
                textAlign: TextAlign.center,
              ),

              const SizedBox(height: 24),

              // Progress indicator or result icon
              if (!_isComplete) ...[
                const SizedBox(
                  width: 60,
                  height: 60,
                  child: CircularProgressIndicator(
                    color: AppColors.gfGreen,
                    strokeWidth: 4,
                  ),
                ),
              ] else if (_result != null) ...[
                Icon(_getResultIcon(), size: 60, color: _getResultColor()),
              ],

              const SizedBox(height: 24),

              // Status text
              Text(
                _isComplete && _result != null
                    ? _getResultTitle()
                    : _currentStatus,
                style: TextStyle(
                  color:
                      _isComplete && _result != null
                          ? _getResultColor()
                          : AppColors.gfOffWhite,
                  fontSize: 16,
                  fontWeight: FontWeight.w500,
                ),
                textAlign: TextAlign.center,
              ),

              // Detailed message for completed uploads
              if (_isComplete && _result != null) ...[
                const SizedBox(height: 16),
                Container(
                  padding: const EdgeInsets.all(16),
                  decoration: BoxDecoration(
                    color: AppColors.gfCardBackground,
                    borderRadius: BorderRadius.circular(12),
                    border: Border.all(
                      color: _getResultColor().withOpacity(0.3),
                    ),
                  ),
                  child: Text(
                    _result!.userFriendlyMessage,
                    style: const TextStyle(
                      color: AppColors.gfGrayText,
                      fontSize: 14,
                      height: 1.4,
                    ),
                    textAlign: TextAlign.center,
                  ),
                ),
              ],

              // Upload limit details
              if (_result?.isLimitExceeded == true) ...[
                const SizedBox(height: 16),
                _buildLimitDetails(),
              ],

              const SizedBox(height: 24),

              // Action buttons
              Row(
                mainAxisAlignment: MainAxisAlignment.end,
                children: [
                  if (!_isComplete && widget.onCancel != null)
                    TextButton(
                      onPressed: widget.onCancel,
                      child: const Text(
                        'Cancel',
                        style: TextStyle(color: AppColors.gfGrayText),
                      ),
                    ),

                  if (_isComplete)
                    TextButton(
                      onPressed: () => Navigator.of(context).pop(_result),
                      child: Text(
                        _result?.isSuccess == true ? 'Continue' : 'OK',
                        style: TextStyle(
                          color:
                              _result?.isSuccess == true
                                  ? AppColors.gfGreen
                                  : AppColors.gfTeal,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                    ),
                ],
              ),
            ],
          ),
        ),
      ),
    );
  }

  IconData _getResultIcon() {
    if (_result?.isSuccess == true) {
      return Icons.check_circle;
    } else if (_result?.isRejected == true) {
      return Icons.block;
    } else if (_result?.isLimitExceeded == true) {
      return Icons.warning;
    } else {
      return Icons.error;
    }
  }

  Color _getResultColor() {
    if (_result?.isSuccess == true) {
      return AppColors.gfGreen;
    } else if (_result?.isRejected == true) {
      return Colors.red;
    } else if (_result?.isLimitExceeded == true) {
      return Colors.orange;
    } else {
      return Colors.red;
    }
  }

  String _getResultTitle() {
    if (_result?.isSuccess == true) {
      return 'Upload Successful!';
    } else if (_result?.isRejected == true) {
      return 'Content Rejected';
    } else if (_result?.isLimitExceeded == true) {
      return 'Upload Limit Reached';
    } else {
      return 'Upload Failed';
    }
  }

  Widget _buildLimitDetails() {
    final result = _result!;

    return Container(
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: Colors.orange.withOpacity(0.1),
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: Colors.orange.withOpacity(0.3)),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          const Text(
            'Current Status:',
            style: TextStyle(
              color: AppColors.gfOffWhite,
              fontWeight: FontWeight.bold,
              fontSize: 12,
            ),
          ),
          const SizedBox(height: 8),
          if (result.activeDemerits != null)
            _buildStatusRow('Demerits', '${result.activeDemerits}/10'),
          if (result.imagesInReview != null)
            _buildStatusRow('Images in Review', '${result.imagesInReview}/1'),
          if (result.uploadsInLastHour != null)
            _buildStatusRow('Hourly Uploads', '${result.uploadsInLastHour}/20'),
        ],
      ),
    );
  }

  Widget _buildStatusRow(String label, String value) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 2),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Text(
            label,
            style: const TextStyle(color: AppColors.gfGrayText, fontSize: 12),
          ),
          Text(
            value,
            style: const TextStyle(
              color: AppColors.gfOffWhite,
              fontSize: 12,
              fontWeight: FontWeight.bold,
            ),
          ),
        ],
      ),
    );
  }
}

/// Helper function to show upload progress dialog with enhanced media service
Future<MediaUploadResult?> showUploadProgressDialog({
  required BuildContext context,
  required Future<MediaUploadResult> uploadFuture,
  String title = 'Uploading Content',
}) async {
  final dialogKey = GlobalKey<_UploadProgressDialogState>();

  // Start the upload and handle status updates
  uploadFuture
      .then((result) {
        dialogKey.currentState?.setResult(result);
      })
      .catchError((error) {
        dialogKey.currentState?.setResult(
          MediaUploadResult.error(
            error.toString(),
            'An unexpected error occurred. Please try again.',
          ),
        );
      });

  return await showDialog<MediaUploadResult>(
    context: context,
    barrierDismissible: false,
    builder: (context) => UploadProgressDialog(key: dialogKey, title: title),
  );
}

/// Enhanced helper function that works with status updates
Future<MediaUploadResult?> showEnhancedUploadDialog({
  required BuildContext context,
  required File file,
  required String fileName,
  required String fileType,
  String mediaType = 'image',
  String title = 'Uploading Content',
}) async {
  AppLogger.debug('UploadProgressDialog: Starting enhanced upload dialog');
  AppLogger.debug(
    'UploadProgressDialog: File: $fileName, Type: $fileType, Media: $mediaType',
  );

  final dialogKey = GlobalKey<_UploadProgressDialogState>();

  // Start the enhanced upload with status updates
  AppLogger.debug(
    'UploadProgressDialog: Creating upload future with enhanced media service',
  );
  final uploadFuture = EnhancedMediaService.instance.uploadMediaWithProcessing(
    file: file,
    fileName: fileName,
    fileType: fileType,
    mediaType: mediaType,
    onStatusUpdate: (status) {
      AppLogger.debug('UploadProgressDialog: Status update: $status');
      dialogKey.currentState?.updateStatus(status);
    },
  );

  uploadFuture
      .then((result) {
        AppLogger.debug('UploadProgressDialog: Upload completed with result');
        AppLogger.debug(
          'UploadProgressDialog: Result success: ${result.isSuccess}',
        );
        AppLogger.debug(
          'UploadProgressDialog: Result rejected: ${result.isRejected}',
        );
        AppLogger.debug(
          'UploadProgressDialog: Result media ID: ${result.mediaId}',
        );
        dialogKey.currentState?.setResult(result);
      })
      .catchError((error) {
        AppLogger.error(
          'UploadProgressDialog: Upload failed with error: $error',
        );
        dialogKey.currentState?.setResult(
          MediaUploadResult.error(
            error.toString(),
            'An unexpected error occurred. Please try again.',
          ),
        );
      });

  AppLogger.debug('UploadProgressDialog: Showing dialog');
  return await showDialog<MediaUploadResult>(
    context: context,
    barrierDismissible: false,
    builder: (context) => UploadProgressDialog(key: dialogKey, title: title),
  );
}
