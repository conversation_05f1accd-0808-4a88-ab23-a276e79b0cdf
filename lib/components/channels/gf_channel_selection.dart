import 'package:flutter/material.dart';
import '../../theme/app_theme.dart';
import '../../models/channel_model.dart';
import '../core/gf_card.dart';

import '../forms/gf_picker.dart';

/// GameFlex channel selection components
///
/// Components for selecting channels during post creation and other
/// channel-related selection scenarios.

/// Channel selection widget for post creation
class GFChannelSelectionWidget extends StatelessWidget {
  final List<ChannelModel> channels;
  final ChannelModel? selectedChannel;
  final Function(ChannelModel?) onChannelSelected;
  final bool allowNoChannel;
  final String? title;
  final String? subtitle;

  const GFChannelSelectionWidget({
    super.key,
    required this.channels,
    this.selectedChannel,
    required this.onChannelSelected,
    this.allowNoChannel = true,
    this.title,
    this.subtitle,
  });

  @override
  Widget build(BuildContext context) {
    return GFCard(
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Header
          Text(
            title ?? 'Select Channel',
            style: const TextStyle(
              color: AppColors.gfOffWhite,
              fontSize: 18,
              fontWeight: FontWeight.w600,
            ),
          ),
          if (subtitle != null) ...[
            const SizedBox(height: 4),
            Text(
              subtitle!,
              style: const TextStyle(color: AppColors.gfGrayText, fontSize: 14),
            ),
          ],
          const SizedBox(height: 16),

          // Channel picker
          GFPicker<ChannelModel?>(
            label: 'Channel',
            value: selectedChannel,
            items: [
              if (allowNoChannel)
                const GFPickerItem<ChannelModel?>(
                  value: null,
                  label: 'No Channel',
                  icon: Icon(Icons.public, color: AppColors.gfGrayText),
                  subtitle: 'Post to general feed',
                ),
              ...channels.map(
                (channel) => GFPickerItem<ChannelModel?>(
                  value: channel,
                  label: channel.name,
                  icon: Icon(Icons.tag, color: AppColors.gfGreen),
                  subtitle: '${channel.memberCount} members',
                ),
              ),
            ],
            onChanged: onChannelSelected,
            hint: 'Choose a channel',
            prefixIcon: const Icon(Icons.tag, color: AppColors.gfGrayText),
          ),

          // Selected channel info
          if (selectedChannel != null) ...[
            const SizedBox(height: 12),
            _buildSelectedChannelInfo(),
          ],
        ],
      ),
    );
  }

  Widget _buildSelectedChannelInfo() {
    if (selectedChannel == null) return const SizedBox.shrink();

    return Container(
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: AppColors.gfGreen.withValues(alpha: 26), // 0.1 opacity
        borderRadius: BorderRadius.circular(8),
        border: Border.all(
          color: AppColors.gfGreen.withValues(alpha: 77),
        ), // 0.3 opacity
      ),
      child: Row(
        children: [
          Container(
            width: 32,
            height: 32,
            decoration: BoxDecoration(
              color: AppColors.gfGreen.withValues(alpha: 51), // 0.2 opacity
              borderRadius: BorderRadius.circular(8),
            ),
            child: Center(
              child: Text(
                selectedChannel!.name.isNotEmpty
                    ? selectedChannel!.name[0].toUpperCase()
                    : '#',
                style: const TextStyle(
                  color: AppColors.gfGreen,
                  fontSize: 14,
                  fontWeight: FontWeight.bold,
                ),
              ),
            ),
          ),
          const SizedBox(width: 12),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  selectedChannel!.name,
                  style: const TextStyle(
                    color: AppColors.gfOffWhite,
                    fontSize: 14,
                    fontWeight: FontWeight.w600,
                  ),
                ),
                Text(
                  '${selectedChannel!.memberCount} members will see this post',
                  style: const TextStyle(
                    color: AppColors.gfGrayText,
                    fontSize: 12,
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }
}

/// Quick channel selection buttons
class GFQuickChannelSelection extends StatelessWidget {
  final List<ChannelModel> channels;
  final ChannelModel? selectedChannel;
  final Function(ChannelModel?) onChannelSelected;
  final bool allowNoChannel;
  final int maxVisible;

  const GFQuickChannelSelection({
    super.key,
    required this.channels,
    this.selectedChannel,
    required this.onChannelSelected,
    this.allowNoChannel = true,
    this.maxVisible = 4,
  });

  @override
  Widget build(BuildContext context) {
    final visibleChannels = channels.take(maxVisible).toList();
    final hasMore = channels.length > maxVisible;

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const Text(
          'Quick Select',
          style: TextStyle(
            color: AppColors.gfOffWhite,
            fontSize: 16,
            fontWeight: FontWeight.w600,
          ),
        ),
        const SizedBox(height: 12),

        Wrap(
          spacing: 8,
          runSpacing: 8,
          children: [
            // No channel option
            if (allowNoChannel)
              _buildChannelChip(
                label: 'No Channel',
                isSelected: selectedChannel == null,
                onTap: () => onChannelSelected(null),
              ),

            // Channel options
            ...visibleChannels.map(
              (channel) => _buildChannelChip(
                label: channel.name,
                isSelected: selectedChannel?.id == channel.id,
                onTap: () => onChannelSelected(channel),
              ),
            ),

            // More button
            if (hasMore) _buildMoreButton(context),
          ],
        ),
      ],
    );
  }

  Widget _buildChannelChip({
    required String label,
    required bool isSelected,
    required VoidCallback onTap,
  }) {
    return GestureDetector(
      onTap: onTap,
      child: Container(
        padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
        decoration: BoxDecoration(
          color: isSelected ? AppColors.gfGreen : AppColors.gfDarkBackground100,
          borderRadius: BorderRadius.circular(16),
          border: Border.all(
            color: isSelected ? AppColors.gfGreen : AppColors.gfGrayBorder,
          ),
        ),
        child: Text(
          label,
          style: TextStyle(
            color: isSelected ? Colors.black : AppColors.gfOffWhite,
            fontSize: 14,
            fontWeight: FontWeight.w500,
          ),
        ),
      ),
    );
  }

  Widget _buildMoreButton(BuildContext context) {
    return GestureDetector(
      onTap: () => _showAllChannels(context),
      child: Container(
        padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
        decoration: BoxDecoration(
          color: AppColors.gfDarkBackground100,
          borderRadius: BorderRadius.circular(16),
          border: Border.all(color: AppColors.gfGrayBorder),
        ),
        child: const Row(
          mainAxisSize: MainAxisSize.min,
          children: [
            Text(
              'More',
              style: TextStyle(
                color: AppColors.gfOffWhite,
                fontSize: 14,
                fontWeight: FontWeight.w500,
              ),
            ),
            SizedBox(width: 4),
            Icon(
              Icons.keyboard_arrow_down,
              color: AppColors.gfGrayText,
              size: 16,
            ),
          ],
        ),
      ),
    );
  }

  void _showAllChannels(BuildContext context) {
    showModalBottomSheet(
      context: context,
      backgroundColor: Colors.transparent,
      builder:
          (context) => GFChannelSelectionBottomSheet(
            channels: channels,
            selectedChannel: selectedChannel,
            onChannelSelected: (channel) {
              onChannelSelected(channel);
              Navigator.of(context).pop();
            },
            allowNoChannel: allowNoChannel,
          ),
    );
  }
}

/// Channel selection bottom sheet
class GFChannelSelectionBottomSheet extends StatelessWidget {
  final List<ChannelModel> channels;
  final ChannelModel? selectedChannel;
  final Function(ChannelModel?) onChannelSelected;
  final bool allowNoChannel;

  const GFChannelSelectionBottomSheet({
    super.key,
    required this.channels,
    this.selectedChannel,
    required this.onChannelSelected,
    this.allowNoChannel = true,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      decoration: const BoxDecoration(
        color: AppColors.gfDarkBackground,
        borderRadius: BorderRadius.vertical(top: Radius.circular(20)),
      ),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          // Header
          Container(
            padding: const EdgeInsets.all(16),
            decoration: const BoxDecoration(
              border: Border(bottom: BorderSide(color: AppColors.gfGrayBorder)),
            ),
            child: Row(
              children: [
                const Text(
                  'Select Channel',
                  style: TextStyle(
                    color: AppColors.gfOffWhite,
                    fontSize: 18,
                    fontWeight: FontWeight.w600,
                  ),
                ),
                const Spacer(),
                IconButton(
                  icon: const Icon(Icons.close, color: AppColors.gfGrayText),
                  onPressed: () => Navigator.of(context).pop(),
                ),
              ],
            ),
          ),

          // Channel list
          Flexible(
            child: ListView(
              shrinkWrap: true,
              children: [
                // No channel option
                if (allowNoChannel)
                  ListTile(
                    leading: const Icon(
                      Icons.public,
                      color: AppColors.gfGrayText,
                    ),
                    title: const Text(
                      'No Channel',
                      style: TextStyle(color: AppColors.gfOffWhite),
                    ),
                    subtitle: const Text(
                      'Post to general feed',
                      style: TextStyle(color: AppColors.gfGrayText),
                    ),
                    trailing:
                        selectedChannel == null
                            ? const Icon(Icons.check, color: AppColors.gfGreen)
                            : null,
                    onTap: () => onChannelSelected(null),
                  ),

                // Channel options
                ...channels.map((channel) {
                  final isSelected = selectedChannel?.id == channel.id;
                  return ListTile(
                    leading: Container(
                      width: 32,
                      height: 32,
                      decoration: BoxDecoration(
                        color: AppColors.gfGreen.withValues(
                          alpha: 51,
                        ), // 0.2 opacity
                        borderRadius: BorderRadius.circular(8),
                      ),
                      child: Center(
                        child: Text(
                          channel.name.isNotEmpty
                              ? channel.name[0].toUpperCase()
                              : '#',
                          style: const TextStyle(
                            color: AppColors.gfGreen,
                            fontSize: 12,
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                      ),
                    ),
                    title: Text(
                      channel.name,
                      style: TextStyle(
                        color:
                            isSelected
                                ? AppColors.gfGreen
                                : AppColors.gfOffWhite,
                        fontWeight:
                            isSelected ? FontWeight.w600 : FontWeight.normal,
                      ),
                    ),
                    subtitle: Text(
                      '${channel.memberCount} members',
                      style: const TextStyle(color: AppColors.gfGrayText),
                    ),
                    trailing:
                        isSelected
                            ? const Icon(Icons.check, color: AppColors.gfGreen)
                            : null,
                    onTap: () => onChannelSelected(channel),
                  );
                }),
              ],
            ),
          ),
        ],
      ),
    );
  }
}
