import 'package:flutter/material.dart';
import '../../theme/app_theme.dart';

import '../../models/channel_model.dart';
import '../core/gf_card.dart';
import '../core/gf_button.dart';

/// GameFlex channel card component
///
/// A reusable channel card component that displays channel information
/// with consistent styling and interaction patterns.
///
/// Example usage:
/// ```dart
/// GFChannelCard(
///   channel: channel,
///   onTap: () => _navigateToChannel(channel),
///   onJoin: () => _joinChannel(channel),
///   onLeave: () => _leaveChannel(channel),
/// )
/// ```
class GFChannelCard extends StatelessWidget {
  final ChannelModel channel;
  final VoidCallback? onTap;
  final VoidCallback? onJoin;
  final VoidCallback? onLeave;
  final VoidCallback? onMore;
  final bool showJoinButton;
  final bool isJoined;
  final EdgeInsetsGeometry? margin;

  const GFChannelCard({
    super.key,
    required this.channel,
    this.onTap,
    this.onJoin,
    this.onLeave,
    this.onMore,
    this.showJoinButton = true,
    this.isJoined = false,
    this.margin,
  });

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: onTap,
      child: Container(
        margin: margin,
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(16),
          gradient: _getChannelGradient(),
          boxShadow: [
            BoxShadow(
              color: Colors.black.withValues(alpha: 77), // 0.3 opacity
              blurRadius: 8,
              offset: const Offset(0, 4),
            ),
          ],
        ),
        child: Stack(
          children: [
            // Main channel image/background
            Center(
              child: Container(
                width: double.infinity,
                height: double.infinity,
                decoration: BoxDecoration(
                  borderRadius: BorderRadius.circular(16),
                ),
                child: ClipRRect(
                  borderRadius: BorderRadius.circular(16),
                  child: _buildChannelImage(),
                ),
              ),
            ),

            // Overlay with channel name and member count
            Positioned(
              bottom: 0,
              left: 0,
              right: 0,
              child: Padding(
                padding: const EdgeInsets.all(12),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    Text(
                      channel.name,
                      style: const TextStyle(
                        color: Colors.white,
                        fontSize: 14,
                        fontWeight: FontWeight.w600,
                        shadows: [
                          Shadow(
                            color: Colors.black,
                            blurRadius: 2,
                            offset: Offset(1, 1),
                          ),
                        ],
                      ),
                      maxLines: 1,
                      overflow: TextOverflow.ellipsis,
                    ),
                    const SizedBox(height: 2),
                    Text(
                      '${channel.memberCount} members',
                      style: TextStyle(
                        color: Colors.grey.shade300,
                        fontSize: 12,
                        shadows: const [
                          Shadow(
                            color: Colors.black,
                            blurRadius: 2,
                            offset: Offset(1, 1),
                          ),
                        ],
                      ),
                    ),
                  ],
                ),
              ),
            ),

            // Join button overlay (only show if not joined and showJoinButton is true)
            if (showJoinButton && !isJoined)
              Positioned(top: 8, right: 8, child: _buildJoinButton()),
          ],
        ),
      ),
    );
  }

  /// Get gradient colors based on channel name
  LinearGradient _getChannelGradient() {
    // Use actual channel icon if available, otherwise fallback to colored gradient
    if (channel.iconUrl != null && channel.iconUrl!.isNotEmpty) {
      return LinearGradient(
        begin: Alignment.topLeft,
        end: Alignment.bottomRight,
        colors: [
          Colors.black.withValues(alpha: 26), // 0.1 opacity - very subtle
          Colors.black.withValues(alpha: 51), // 0.2 opacity
        ],
      );
    }

    // Create different gradients based on channel name hash
    final hash = channel.name.hashCode;
    final gradients = [
      // Minecraft-style (green/brown)
      const LinearGradient(
        begin: Alignment.topLeft,
        end: Alignment.bottomRight,
        colors: [Color(0xFF4CAF50), Color(0xFF2E7D32)],
      ),
      // Fortnite-style (blue/purple)
      const LinearGradient(
        begin: Alignment.topLeft,
        end: Alignment.bottomRight,
        colors: [Color(0xFF2196F3), Color(0xFF3F51B5)],
      ),
      // Valorant-style (red/orange)
      const LinearGradient(
        begin: Alignment.topLeft,
        end: Alignment.bottomRight,
        colors: [Color(0xFFFF5722), Color(0xFFE91E63)],
      ),
      // Apex-style (orange/yellow)
      const LinearGradient(
        begin: Alignment.topLeft,
        end: Alignment.bottomRight,
        colors: [Color(0xFFFF9800), Color(0xFFFFC107)],
      ),
      // COD-style (dark/gray)
      const LinearGradient(
        begin: Alignment.topLeft,
        end: Alignment.bottomRight,
        colors: [Color(0xFF424242), Color(0xFF616161)],
      ),
      // General-style (teal/cyan)
      const LinearGradient(
        begin: Alignment.topLeft,
        end: Alignment.bottomRight,
        colors: [Color(0xFF009688), Color(0xFF00BCD4)],
      ),
    ];

    return gradients[hash.abs() % gradients.length];
  }

  /// Build channel image or fallback icon
  Widget _buildChannelImage() {
    // Use actual channel icon if available, otherwise fallback to colored icon
    if (channel.iconUrl != null && channel.iconUrl!.isNotEmpty) {
      return Container(
        width: double.infinity,
        height: double.infinity,
        decoration: BoxDecoration(
          color: Colors.black.withValues(
            alpha: 51,
          ), // 0.2 opacity dark background
        ),
        child: ClipRRect(
          borderRadius: const BorderRadius.vertical(top: Radius.circular(16)),
          child: Image.network(
            channel.iconUrl!,
            fit: BoxFit.cover,
            width: double.infinity,
            height: double.infinity,
            errorBuilder: (context, error, stackTrace) {
              // Fallback to icon if image fails to load
              return _buildFallbackIcon();
            },
            loadingBuilder: (context, child, loadingProgress) {
              if (loadingProgress == null) return child;
              return Center(
                child: CircularProgressIndicator(
                  value:
                      loadingProgress.expectedTotalBytes != null
                          ? loadingProgress.cumulativeBytesLoaded /
                              loadingProgress.expectedTotalBytes!
                          : null,
                  valueColor: const AlwaysStoppedAnimation<Color>(Colors.white),
                ),
              );
            },
          ),
        ),
      );
    } else {
      return _buildFallbackIcon();
    }
  }

  /// Build fallback icon when no image is available
  Widget _buildFallbackIcon() {
    return Container(
      width: double.infinity,
      height: double.infinity,
      decoration: BoxDecoration(
        gradient: LinearGradient(
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
          colors: [
            _getChannelColor(
              channel.name,
            ).withValues(alpha: 128), // 0.5 opacity
            _getChannelColor(
              channel.name,
            ).withValues(alpha: 204), // 0.8 opacity
          ],
        ),
      ),
      child: Center(
        child: Icon(
          _getChannelIcon(),
          size: 80,
          color: Colors.white.withValues(alpha: 230), // 0.9 opacity
          shadows: const [
            Shadow(color: Colors.black, blurRadius: 4, offset: Offset(2, 2)),
          ],
        ),
      ),
    );
  }

  /// Get color based on channel name
  Color _getChannelColor(String channelName) {
    final name = channelName.toLowerCase();

    // Define specific colors for known game channels
    if (name.contains('minecraft')) {
      return const Color(0xFF4CAF50); // Minecraft Green
    } else if (name.contains('fortnite')) {
      return const Color(0xFF8E44AD); // Fortnite Purple
    } else if (name.contains('valorant')) {
      return const Color(0xFFE74C3C); // Valorant Red
    } else if (name.contains('league') || name.contains('lol')) {
      return const Color(0xFF3498DB); // League Blue
    } else if (name.contains('apex')) {
      return const Color(0xFFE67E22); // Apex Orange
    } else if (name.contains('gaming') || name.contains('general')) {
      return AppColors.gfGreen;
    } else if (name.contains('call of duty') || name.contains('cod')) {
      return const Color(0xFF2C3E50); // COD Dark Blue
    } else if (name.contains('overwatch')) {
      return const Color(0xFFF39C12); // Overwatch Orange
    } else if (name.contains('rocket league')) {
      return const Color(0xFF9B59B6); // Rocket League Purple
    } else if (name.contains('counter strike') || name.contains('cs')) {
      return const Color(0xFF34495E); // CS Grey
    }

    // Fallback to vibrant hash-based color for unknown channels
    final hash = channelName.hashCode;
    final colors = [
      const Color(0xFF4CAF50), // Green
      const Color(0xFF3498DB), // Blue
      const Color(0xFF9B59B6), // Purple
      const Color(0xFFE74C3C), // Red
      const Color(0xFFE67E22), // Orange
      const Color(0xFF1ABC9C), // Turquoise
      const Color(0xFFF39C12), // Yellow
      const Color(0xFF2ECC71), // Emerald
    ];

    return colors[hash.abs() % colors.length];
  }

  /// Get icon based on channel name
  IconData _getChannelIcon() {
    final name = channel.name.toLowerCase();

    if (name.contains('gaming') || name.contains('game')) {
      return Icons.sports_esports;
    } else if (name.contains('mobile')) {
      return Icons.phone_android;
    } else if (name.contains('competitive') || name.contains('esports')) {
      return Icons.emoji_events;
    } else if (name.contains('indie')) {
      return Icons.lightbulb_outline;
    } else if (name.contains('development') || name.contains('dev')) {
      return Icons.code;
    } else if (name.contains('minecraft')) {
      return Icons.view_module;
    } else if (name.contains('fortnite')) {
      return Icons.architecture;
    } else if (name.contains('valorant')) {
      return Icons.gps_fixed;
    } else if (name.contains('league') || name.contains('lol')) {
      return Icons.shield;
    } else if (name.contains('apex')) {
      return Icons.flight_takeoff;
    } else {
      return Icons.sports_esports; // Default gaming icon
    }
  }

  /// Build join button
  Widget _buildJoinButton() {
    return Container(
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(16),
        color: Colors.black.withValues(alpha: 128), // 0.5 opacity
      ),
      child: Material(
        color: Colors.transparent,
        child: InkWell(
          onTap: onJoin,
          borderRadius: BorderRadius.circular(16),
          child: Container(
            padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
            child: Text(
              'Join',
              style: TextStyle(
                color: AppColors.gfGreen,
                fontSize: 12,
                fontWeight: FontWeight.w600,
              ),
            ),
          ),
        ),
      ),
    );
  }
}

/// Compact channel card for lists
class GFCompactChannelCard extends StatelessWidget {
  final ChannelModel channel;
  final VoidCallback? onTap;
  final bool isSelected;
  final Widget? trailing;

  const GFCompactChannelCard({
    super.key,
    required this.channel,
    this.onTap,
    this.isSelected = false,
    this.trailing,
  });

  @override
  Widget build(BuildContext context) {
    return GFCard(
      onTap: onTap,
      padding: const EdgeInsets.all(12),
      backgroundColor:
          isSelected
              ? AppColors.gfGreen.withValues(alpha: 51) // 0.2 opacity
              : null,
      border: isSelected ? Border.all(color: AppColors.gfGreen) : null,
      child: Row(
        children: [
          // Channel avatar
          Container(
            width: 32,
            height: 32,
            decoration: BoxDecoration(
              color: AppColors.gfGreen.withValues(alpha: 51), // 0.2 opacity
              borderRadius: BorderRadius.circular(8),
            ),
            child: Center(
              child: Text(
                channel.name.isNotEmpty ? channel.name[0].toUpperCase() : '#',
                style: const TextStyle(
                  color: AppColors.gfGreen,
                  fontSize: 14,
                  fontWeight: FontWeight.bold,
                ),
              ),
            ),
          ),
          const SizedBox(width: 12),

          // Channel info
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  channel.name,
                  style: TextStyle(
                    color:
                        isSelected ? AppColors.gfGreen : AppColors.gfOffWhite,
                    fontSize: 14,
                    fontWeight: FontWeight.w600,
                  ),
                ),
                Text(
                  '${channel.memberCount} members',
                  style: const TextStyle(
                    color: AppColors.gfGrayText,
                    fontSize: 12,
                  ),
                ),
              ],
            ),
          ),

          // Trailing widget
          if (trailing != null) trailing!,
        ],
      ),
    );
  }
}

/// Channel list item with selection
class GFChannelListItem extends StatelessWidget {
  final ChannelModel channel;
  final bool isSelected;
  final VoidCallback? onTap;
  final bool showCheckbox;

  const GFChannelListItem({
    super.key,
    required this.channel,
    this.isSelected = false,
    this.onTap,
    this.showCheckbox = false,
  });

  @override
  Widget build(BuildContext context) {
    return ListTile(
      leading: Container(
        width: 40,
        height: 40,
        decoration: BoxDecoration(
          color: AppColors.gfGreen.withValues(alpha: 51), // 0.2 opacity
          borderRadius: BorderRadius.circular(8),
        ),
        child: Center(
          child: Text(
            '#',
            style: const TextStyle(
              color: AppColors.gfGreen,
              fontSize: 16,
              fontWeight: FontWeight.bold,
            ),
          ),
        ),
      ),
      title: Text(
        channel.name,
        style: TextStyle(
          color: isSelected ? AppColors.gfGreen : AppColors.gfOffWhite,
          fontWeight: isSelected ? FontWeight.w600 : FontWeight.normal,
        ),
      ),
      subtitle: Text(
        '${channel.memberCount} members',
        style: const TextStyle(color: AppColors.gfGrayText),
      ),
      trailing:
          showCheckbox
              ? Checkbox(
                value: isSelected,
                onChanged: onTap != null ? (_) => onTap!() : null,
                activeColor: AppColors.gfGreen,
              )
              : isSelected
              ? const Icon(Icons.check, color: AppColors.gfGreen)
              : null,
      onTap: onTap,
      tileColor:
          isSelected
              ? AppColors.gfGreen.withValues(alpha: 26) // 0.1 opacity
              : null,
    );
  }
}

/// Channel header for channel detail screens
class GFChannelHeader extends StatelessWidget {
  final ChannelModel channel;
  final bool isJoined;
  final VoidCallback? onJoin;
  final VoidCallback? onLeave;
  final VoidCallback? onEdit;
  final VoidCallback? onShare;

  const GFChannelHeader({
    super.key,
    required this.channel,
    this.isJoined = false,
    this.onJoin,
    this.onLeave,
    this.onEdit,
    this.onShare,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: const BoxDecoration(
        color: AppColors.gfDarkBackground,
        border: Border(bottom: BorderSide(color: AppColors.gfGrayBorder)),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Channel info
          Row(
            children: [
              Container(
                width: 60,
                height: 60,
                decoration: BoxDecoration(
                  color: AppColors.gfGreen.withValues(alpha: 51), // 0.2 opacity
                  borderRadius: BorderRadius.circular(16),
                ),
                child: Center(
                  child: Text(
                    channel.name.isNotEmpty
                        ? channel.name[0].toUpperCase()
                        : '#',
                    style: const TextStyle(
                      color: AppColors.gfGreen,
                      fontSize: 24,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                ),
              ),
              const SizedBox(width: 16),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      channel.name,
                      style: const TextStyle(
                        color: AppColors.gfOffWhite,
                        fontSize: 24,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    const SizedBox(height: 4),
                    Row(
                      children: [
                        Text(
                          '${channel.memberCount} members',
                          style: const TextStyle(
                            color: AppColors.gfGrayText,
                            fontSize: 14,
                          ),
                        ),
                        const SizedBox(width: 16),
                        Text(
                          '${channel.postCount} posts',
                          style: const TextStyle(
                            color: AppColors.gfGrayText,
                            fontSize: 14,
                          ),
                        ),
                      ],
                    ),
                  ],
                ),
              ),
            ],
          ),

          // Description
          if (channel.description != null &&
              channel.description!.isNotEmpty) ...[
            const SizedBox(height: 12),
            Text(
              channel.description!,
              style: const TextStyle(
                color: AppColors.gfOffWhite,
                fontSize: 14,
                height: 1.4,
              ),
            ),
          ],

          // Action buttons
          const SizedBox(height: 16),
          Row(
            children: [
              Expanded(
                child:
                    isJoined
                        ? GFButton(
                          text: 'Joined',
                          onPressed: onLeave,
                          type: GFButtonType.secondary,
                        )
                        : GFButton(
                          text: 'Join Channel',
                          onPressed: onJoin,
                          type: GFButtonType.primary,
                        ),
              ),
              const SizedBox(width: 12),
              GFButton(
                text: '',
                onPressed: onShare,
                type: GFButtonType.outline,
                icon: Icons.share,
                width: 48,
              ),
              if (onEdit != null) ...[
                const SizedBox(width: 8),
                GFButton(
                  text: '',
                  onPressed: onEdit,
                  type: GFButtonType.outline,
                  icon: Icons.edit,
                  width: 48,
                ),
              ],
            ],
          ),
        ],
      ),
    );
  }
}
