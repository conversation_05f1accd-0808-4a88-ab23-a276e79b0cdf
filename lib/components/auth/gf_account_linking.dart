import 'package:flutter/material.dart';
import '../../theme/app_theme.dart';
import '../../models/linked_account_model.dart';
import '../core/gf_card.dart';
import '../core/gf_button.dart';
import 'gf_signin_buttons.dart';

/// GameFlex account linking components
///
/// Components for managing linked accounts and third-party integrations
/// with consistent styling and interaction patterns.

/// Linked account display widget
class GFLinkedAccountWidget extends StatelessWidget {
  final LinkedAccountModel account;
  final VoidCallback? onUnlink;
  final VoidCallback? onTap;
  final bool showUnlinkButton;

  const GFLinkedAccountWidget({
    super.key,
    required this.account,
    this.onUnlink,
    this.onTap,
    this.showUnlinkButton = true,
  });

  @override
  Widget build(BuildContext context) {
    return GFCard(
      onTap: onTap,
      child: Row(
        children: [
          // Provider icon
          _buildProviderIcon(),
          const SizedBox(width: 12),

          // Account info
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  _getProviderDisplayName(),
                  style: const TextStyle(
                    color: AppColors.gfOffWhite,
                    fontSize: 16,
                    fontWeight: FontWeight.w600,
                  ),
                ),
                const SizedBox(height: 2),
                Text(
                  account.displayName,
                  style: const TextStyle(
                    color: AppColors.gfGrayText,
                    fontSize: 14,
                  ),
                ),
                if (account.email != null) ...[
                  const SizedBox(height: 2),
                  Text(
                    account.email!,
                    style: const TextStyle(
                      color: AppColors.gfGrayText,
                      fontSize: 12,
                    ),
                  ),
                ],
              ],
            ),
          ),

          // Status indicator
          Container(
            padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
            decoration: BoxDecoration(
              color: AppColors.gfGreen.withValues(alpha: 51), // 0.2 opacity
              borderRadius: BorderRadius.circular(12),
            ),
            child: const Text(
              'Connected',
              style: TextStyle(
                color: AppColors.gfGreen,
                fontSize: 12,
                fontWeight: FontWeight.w500,
              ),
            ),
          ),

          // Unlink button
          if (showUnlinkButton && onUnlink != null) ...[
            const SizedBox(width: 8),
            IconButton(
              icon: const Icon(
                Icons.link_off,
                color: AppColors.gfGrayText,
                size: 20,
              ),
              onPressed: onUnlink,
            ),
          ],
        ],
      ),
    );
  }

  Widget _buildProviderIcon() {
    switch (account.provider.toLowerCase()) {
      case 'apple':
        return Container(
          width: 40,
          height: 40,
          decoration: BoxDecoration(
            color: Colors.black,
            borderRadius: BorderRadius.circular(8),
          ),
          child: const Icon(Icons.apple, color: Colors.white, size: 24),
        );
      case 'xbox':
        return Container(
          width: 40,
          height: 40,
          decoration: BoxDecoration(
            color: const Color(0xFF107C10),
            borderRadius: BorderRadius.circular(8),
          ),
          child: const Center(
            child: Text(
              'X',
              style: TextStyle(
                color: Colors.white,
                fontSize: 20,
                fontWeight: FontWeight.bold,
              ),
            ),
          ),
        );
      case 'google':
        return Container(
          width: 40,
          height: 40,
          decoration: BoxDecoration(
            color: Colors.white,
            borderRadius: BorderRadius.circular(8),
            border: Border.all(color: AppColors.gfGrayBorder),
          ),
          child: const Icon(Icons.g_mobiledata, color: Colors.red, size: 24),
        );
      case 'steam':
        return Container(
          width: 40,
          height: 40,
          decoration: BoxDecoration(
            color: const Color(0xFF1B2838),
            borderRadius: BorderRadius.circular(8),
          ),
          child: const Icon(
            Icons.videogame_asset,
            color: Colors.white,
            size: 24,
          ),
        );
      default:
        return Container(
          width: 40,
          height: 40,
          decoration: BoxDecoration(
            color: AppColors.gfDarkBackground100,
            borderRadius: BorderRadius.circular(8),
          ),
          child: const Icon(
            Icons.account_circle,
            color: AppColors.gfGrayText,
            size: 24,
          ),
        );
    }
  }

  String _getProviderDisplayName() {
    switch (account.provider.toLowerCase()) {
      case 'apple':
        return 'Apple ID';
      case 'xbox':
        return 'Xbox Live';
      case 'google':
        return 'Google';
      case 'steam':
        return 'Steam';
      default:
        return account.provider;
    }
  }
}

/// Account linking options
class GFAccountLinkingOptions extends StatelessWidget {
  final List<String> availableProviders;
  final List<String> linkedProviders;
  final Function(String) onLinkProvider;
  final bool isLoading;

  const GFAccountLinkingOptions({
    super.key,
    required this.availableProviders,
    required this.linkedProviders,
    required this.onLinkProvider,
    this.isLoading = false,
  });

  @override
  Widget build(BuildContext context) {
    final unlinkableProviders =
        availableProviders
            .where((provider) => !linkedProviders.contains(provider))
            .toList();

    if (unlinkableProviders.isEmpty) {
      return const GFEmptyCard(
        icon: Icons.link,
        title: 'All accounts linked',
        subtitle: 'You have linked all available account types.',
      );
    }

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const Text(
          'Link Additional Accounts',
          style: TextStyle(
            color: AppColors.gfOffWhite,
            fontSize: 18,
            fontWeight: FontWeight.w600,
          ),
        ),
        const SizedBox(height: 16),
        ...unlinkableProviders.map(
          (provider) => Padding(
            padding: const EdgeInsets.only(bottom: 12),
            child: _buildLinkButton(provider),
          ),
        ),
      ],
    );
  }

  Widget _buildLinkButton(String provider) {
    switch (provider.toLowerCase()) {
      case 'apple':
        return GFAppleSignInButton(
          onPressed: isLoading ? null : () => onLinkProvider(provider),
          isLoading: isLoading,
        );
      case 'xbox':
        return GFXboxSignInButton(
          onPressed: isLoading ? null : () => onLinkProvider(provider),
          isLoading: isLoading,
        );
      case 'google':
        return GFGoogleSignInButton(
          onPressed: isLoading ? null : () => onLinkProvider(provider),
          isLoading: isLoading,
        );
      default:
        return GFButton(
          text: 'Link ${_getProviderDisplayName(provider)}',
          onPressed: isLoading ? null : () => onLinkProvider(provider),
          isLoading: isLoading,
          type: GFButtonType.secondary,
        );
    }
  }

  String _getProviderDisplayName(String provider) {
    switch (provider.toLowerCase()) {
      case 'apple':
        return 'Apple ID';
      case 'xbox':
        return 'Xbox Live';
      case 'google':
        return 'Google';
      case 'steam':
        return 'Steam';
      default:
        return provider;
    }
  }
}

/// Account choice screen for Xbox linking
class GFAccountChoiceWidget extends StatelessWidget {
  final String provider;
  final String? existingAccountName;
  final VoidCallback onCreateNew;
  final VoidCallback onLinkExisting;
  final VoidCallback onCancel;

  const GFAccountChoiceWidget({
    super.key,
    required this.provider,
    this.existingAccountName,
    required this.onCreateNew,
    required this.onLinkExisting,
    required this.onCancel,
  });

  @override
  Widget build(BuildContext context) {
    return GFCard(
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          // Header
          Text(
            'Account Choice',
            style: const TextStyle(
              color: AppColors.gfOffWhite,
              fontSize: 20,
              fontWeight: FontWeight.w600,
            ),
          ),
          const SizedBox(height: 16),

          // Description
          Text(
            existingAccountName != null
                ? 'We found an existing GameFlex account ($existingAccountName) linked to this ${_getProviderDisplayName(provider)} account.'
                : 'Choose how you want to proceed with your ${_getProviderDisplayName(provider)} account.',
            style: const TextStyle(
              color: AppColors.gfGrayText,
              fontSize: 14,
              height: 1.4,
            ),
            textAlign: TextAlign.center,
          ),
          const SizedBox(height: 24),

          // Options
          GFButton(
            text: 'Create New Account',
            onPressed: onCreateNew,
            type: GFButtonType.primary,
            width: double.infinity,
          ),
          const SizedBox(height: 12),

          if (existingAccountName != null)
            GFButton(
              text: 'Use Existing Account',
              onPressed: onLinkExisting,
              type: GFButtonType.secondary,
              width: double.infinity,
            ),

          const SizedBox(height: 12),
          GFButton(
            text: 'Cancel',
            onPressed: onCancel,
            type: GFButtonType.text,
            width: double.infinity,
          ),
        ],
      ),
    );
  }

  String _getProviderDisplayName(String provider) {
    switch (provider.toLowerCase()) {
      case 'apple':
        return 'Apple ID';
      case 'xbox':
        return 'Xbox Live';
      case 'google':
        return 'Google';
      case 'steam':
        return 'Steam';
      default:
        return provider;
    }
  }
}
