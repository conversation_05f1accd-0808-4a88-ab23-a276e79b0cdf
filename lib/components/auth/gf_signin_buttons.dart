import 'package:flutter/material.dart';
import '../../theme/app_theme.dart';
import '../core/gf_button.dart';

/// GameFlex sign-in button components
/// 
/// A collection of sign-in buttons for different authentication providers
/// with consistent styling and GameFlex branding.

/// Apple Sign-In button
class GFAppleSignInButton extends StatelessWidget {
  final VoidCallback? onPressed;
  final bool isLoading;
  final double height;
  final double? width;

  const GFAppleSignInButton({
    super.key,
    this.onPressed,
    this.isLoading = false,
    this.height = 48.0,
    this.width,
  });

  @override
  Widget build(BuildContext context) {
    return SizedBox(
      height: height,
      width: width,
      child: ElevatedButton(
        onPressed: isLoading ? null : onPressed,
        style: ElevatedButton.styleFrom(
          backgroundColor: Colors.black,
          foregroundColor: Colors.white,
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(height / 2),
          ),
          elevation: 0,
          disabledBackgroundColor: Colors.black.withValues(alpha: 153),
          disabledForegroundColor: Colors.white.withValues(alpha: 153),
        ),
        child: isLoading
            ? const SizedBox(
                height: 20,
                width: 20,
                child: CircularProgressIndicator(
                  strokeWidth: 2,
                  valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
                ),
              )
            : Row(
                mainAxisSize: MainAxisSize.min,
                children: [
                  Icon(
                    Icons.apple,
                    size: 20,
                    color: Colors.white,
                  ),
                  const SizedBox(width: 8),
                  const Text(
                    'Continue with Apple',
                    style: TextStyle(
                      fontSize: 16,
                      fontWeight: FontWeight.w600,
                    ),
                  ),
                ],
              ),
      ),
    );
  }
}

/// Xbox Sign-In button
class GFXboxSignInButton extends StatelessWidget {
  final VoidCallback? onPressed;
  final bool isLoading;
  final double height;
  final double? width;

  const GFXboxSignInButton({
    super.key,
    this.onPressed,
    this.isLoading = false,
    this.height = 48.0,
    this.width,
  });

  @override
  Widget build(BuildContext context) {
    return SizedBox(
      height: height,
      width: width,
      child: ElevatedButton(
        onPressed: isLoading ? null : onPressed,
        style: ElevatedButton.styleFrom(
          backgroundColor: const Color(0xFF107C10), // Xbox green
          foregroundColor: Colors.white,
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(height / 2),
          ),
          elevation: 0,
          disabledBackgroundColor: const Color(0xFF107C10).withValues(alpha: 153),
          disabledForegroundColor: Colors.white.withValues(alpha: 153),
        ),
        child: isLoading
            ? const SizedBox(
                height: 20,
                width: 20,
                child: CircularProgressIndicator(
                  strokeWidth: 2,
                  valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
                ),
              )
            : Row(
                mainAxisSize: MainAxisSize.min,
                children: [
                  _buildXboxIcon(),
                  const SizedBox(width: 8),
                  const Text(
                    'Continue with Xbox',
                    style: TextStyle(
                      fontSize: 16,
                      fontWeight: FontWeight.w600,
                    ),
                  ),
                ],
              ),
      ),
    );
  }

  Widget _buildXboxIcon() {
    return Container(
      width: 20,
      height: 20,
      decoration: const BoxDecoration(
        color: Colors.white,
        shape: BoxShape.circle,
      ),
      child: const Center(
        child: Text(
          'X',
          style: TextStyle(
            color: Color(0xFF107C10),
            fontSize: 12,
            fontWeight: FontWeight.bold,
          ),
        ),
      ),
    );
  }
}

/// Google Sign-In button
class GFGoogleSignInButton extends StatelessWidget {
  final VoidCallback? onPressed;
  final bool isLoading;
  final double height;
  final double? width;

  const GFGoogleSignInButton({
    super.key,
    this.onPressed,
    this.isLoading = false,
    this.height = 48.0,
    this.width,
  });

  @override
  Widget build(BuildContext context) {
    return SizedBox(
      height: height,
      width: width,
      child: ElevatedButton(
        onPressed: isLoading ? null : onPressed,
        style: ElevatedButton.styleFrom(
          backgroundColor: Colors.white,
          foregroundColor: Colors.black87,
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(height / 2),
            side: const BorderSide(color: AppColors.gfGrayBorder),
          ),
          elevation: 0,
          disabledBackgroundColor: Colors.white.withValues(alpha: 153),
          disabledForegroundColor: Colors.black87.withValues(alpha: 153),
        ),
        child: isLoading
            ? const SizedBox(
                height: 20,
                width: 20,
                child: CircularProgressIndicator(
                  strokeWidth: 2,
                  valueColor: AlwaysStoppedAnimation<Color>(Colors.black87),
                ),
              )
            : Row(
                mainAxisSize: MainAxisSize.min,
                children: [
                  _buildGoogleIcon(),
                  const SizedBox(width: 8),
                  const Text(
                    'Continue with Google',
                    style: TextStyle(
                      fontSize: 16,
                      fontWeight: FontWeight.w600,
                    ),
                  ),
                ],
              ),
      ),
    );
  }

  Widget _buildGoogleIcon() {
    return Container(
      width: 20,
      height: 20,
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(2),
      ),
      child: const Icon(
        Icons.g_mobiledata,
        size: 20,
        color: Colors.red,
      ),
    );
  }
}

/// Email Sign-In button
class GFEmailSignInButton extends StatelessWidget {
  final VoidCallback? onPressed;
  final bool isLoading;
  final double height;
  final double? width;
  final String text;

  const GFEmailSignInButton({
    super.key,
    this.onPressed,
    this.isLoading = false,
    this.height = 48.0,
    this.width,
    this.text = 'Continue with Email',
  });

  @override
  Widget build(BuildContext context) {
    return GFButton(
      text: text,
      onPressed: onPressed,
      isLoading: isLoading,
      height: height,
      width: width,
      icon: Icons.email_outlined,
      type: GFButtonType.secondary,
    );
  }
}

/// Generic OAuth button
class GFOAuthButton extends StatelessWidget {
  final String provider;
  final String text;
  final Widget icon;
  final Color backgroundColor;
  final Color foregroundColor;
  final VoidCallback? onPressed;
  final bool isLoading;
  final double height;
  final double? width;

  const GFOAuthButton({
    super.key,
    required this.provider,
    required this.text,
    required this.icon,
    required this.backgroundColor,
    required this.foregroundColor,
    this.onPressed,
    this.isLoading = false,
    this.height = 48.0,
    this.width,
  });

  @override
  Widget build(BuildContext context) {
    return SizedBox(
      height: height,
      width: width,
      child: ElevatedButton(
        onPressed: isLoading ? null : onPressed,
        style: ElevatedButton.styleFrom(
          backgroundColor: backgroundColor,
          foregroundColor: foregroundColor,
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(height / 2),
          ),
          elevation: 0,
          disabledBackgroundColor: backgroundColor.withValues(alpha: 153),
          disabledForegroundColor: foregroundColor.withValues(alpha: 153),
        ),
        child: isLoading
            ? SizedBox(
                height: 20,
                width: 20,
                child: CircularProgressIndicator(
                  strokeWidth: 2,
                  valueColor: AlwaysStoppedAnimation<Color>(foregroundColor),
                ),
              )
            : Row(
                mainAxisSize: MainAxisSize.min,
                children: [
                  icon,
                  const SizedBox(width: 8),
                  Text(
                    text,
                    style: const TextStyle(
                      fontSize: 16,
                      fontWeight: FontWeight.w600,
                    ),
                  ),
                ],
              ),
      ),
    );
  }
}

/// Sign-in button group with divider
class GFSignInButtonGroup extends StatelessWidget {
  final List<Widget> buttons;
  final String? dividerText;
  final EdgeInsetsGeometry? padding;

  const GFSignInButtonGroup({
    super.key,
    required this.buttons,
    this.dividerText,
    this.padding,
  });

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: padding ?? EdgeInsets.zero,
      child: Column(
        children: [
          ...buttons.map((button) => Padding(
                padding: const EdgeInsets.only(bottom: 12),
                child: button,
              )),
          if (dividerText != null) ...[
            const SizedBox(height: 8),
            Row(
              children: [
                const Expanded(
                  child: Divider(color: AppColors.gfGrayBorder),
                ),
                Padding(
                  padding: const EdgeInsets.symmetric(horizontal: 16),
                  child: Text(
                    dividerText!,
                    style: const TextStyle(
                      color: AppColors.gfGrayText,
                      fontSize: 14,
                    ),
                  ),
                ),
                const Expanded(
                  child: Divider(color: AppColors.gfGrayBorder),
                ),
              ],
            ),
            const SizedBox(height: 20),
          ],
        ],
      ),
    );
  }
}
