# GameFlex Design System

A comprehensive guide to the GameFlex design system and component library for designers and developers.

## 🎨 Design Principles

### Consistency
- Unified visual language across all components
- Standardized spacing, colors, and typography
- Predictable interaction patterns

### Accessibility
- High contrast ratios for readability
- Touch-friendly target sizes (minimum 44px)
- Screen reader support

### Performance
- Optimized components for smooth animations
- Lazy loading for media content
- Efficient state management

### Modularity
- Reusable components with clear APIs
- Composable design patterns
- Easy customization and theming

## 🎯 Color System

### Primary Colors
```dart
// Brand Colors
gfGreen: #00FF88        // Primary brand color
gfBlue: #0066FF         // Secondary brand color
gfDarkBlue: #003366     // Dark accent

// Background Colors
gfDarkBackground100: #000000    // Pure black
gfDarkBackground: #1A1A1A       // Primary dark
darkBlue: #0A1628              // Navigation background

// Text Colors
gfOffWhite: #F5F5F5     // Primary text
gfGrayText: #999999     // Secondary text
gfGrayBorder: #333333   // Borders and dividers
```

### Usage Guidelines
- **gfGreen**: Primary actions, success states, active elements
- **gfBlue**: Links, information, secondary actions
- **gfDarkBackground**: Card backgrounds, containers
- **gfOffWhite**: Primary text content
- **gfGrayText**: Secondary text, placeholders

## 📝 Typography

### Font Family
- **Primary**: Roboto (Google Fonts)
- **Fallback**: System default sans-serif

### Text Styles
```dart
// Headings
H1: 32px, Bold, gfOffWhite
H2: 24px, Bold, gfOffWhite
H3: 20px, SemiBold, gfOffWhite
H4: 18px, SemiBold, gfOffWhite

// Body Text
Body Large: 16px, Regular, gfOffWhite
Body Medium: 14px, Regular, gfOffWhite
Body Small: 12px, Regular, gfGrayText

// UI Text
Button: 16px, Bold
Caption: 12px, Medium, gfGrayText
Label: 14px, Medium, gfOffWhite
```

## 📏 Spacing System

### Base Unit: 4px
All spacing follows a 4px grid system for consistency.

```dart
// Spacing Scale
xs: 4px    // Tight spacing
sm: 8px    // Small spacing
md: 12px   // Medium spacing
lg: 16px   // Large spacing
xl: 24px   // Extra large spacing
xxl: 32px  // Extra extra large spacing
```

### Common Patterns
- **Card Padding**: 16px (lg)
- **Button Padding**: 16px horizontal, 14px vertical
- **List Item Spacing**: 12px (md)
- **Section Spacing**: 24px (xl)

## 🔘 Component Specifications

### Buttons
```dart
// Dimensions
Height: 48px (default)
Min Width: 120px
Border Radius: 24px (height/2)

// Padding
Horizontal: 16px
Vertical: 14px

// States
Default: Full opacity
Disabled: 60% opacity
Loading: Show spinner, disable interaction
```

### Text Fields
```dart
// Dimensions
Height: 48px
Border Radius: 8px

// Padding
Horizontal: 16px
Vertical: 14px

// States
Default: gfDarkBackground fill
Focused: gfGreen border (1.5px)
Error: Red border (1.5px)
Disabled: 50% opacity
```

### Cards
```dart
// Dimensions
Border Radius: 12px
Margin: 16px horizontal, 8px vertical

// Padding
Default: 16px all sides
Compact: 12px all sides

// Elevation
Default: None
Hover: Subtle shadow (optional)
```

### Avatars
```dart
// Sizes
Small: 32px diameter
Medium: 40px diameter (default)
Large: 60px diameter
Extra Large: 80px diameter

// Border
Width: 2px
Color: White with 50% opacity
```

## 🎭 Component States

### Interactive States
- **Default**: Normal appearance
- **Hover**: Subtle highlight (desktop)
- **Active**: Pressed appearance
- **Disabled**: Reduced opacity, no interaction
- **Loading**: Show progress indicator

### Data States
- **Empty**: Placeholder content with icon
- **Loading**: Skeleton or spinner
- **Error**: Error message with retry option
- **Success**: Confirmation feedback

## 🌙 Dark Theme

GameFlex uses a dark theme by default:

### Background Hierarchy
1. **Level 0**: Pure black (#000000) - App background
2. **Level 1**: Dark gray (#1A1A1A) - Card backgrounds
3. **Level 2**: Lighter gray (#333333) - Elevated elements

### Text Hierarchy
1. **Primary**: Off-white (#F5F5F5) - Main content
2. **Secondary**: Gray (#999999) - Supporting text
3. **Disabled**: Darker gray (#666666) - Inactive text

## 📱 Responsive Design

### Breakpoints
```dart
// Screen Sizes
Mobile: < 768px
Tablet: 768px - 1024px
Desktop: > 1024px
```

### Adaptive Components
- **Navigation**: Bottom tabs on mobile, side nav on desktop
- **Cards**: Full width on mobile, grid on larger screens
- **Modals**: Full screen on mobile, centered on desktop

## 🎬 Animation Guidelines

### Duration
- **Fast**: 150ms - Small state changes
- **Medium**: 300ms - Component transitions
- **Slow**: 500ms - Page transitions

### Easing
- **Standard**: Ease-out for entrances
- **Accelerate**: Ease-in for exits
- **Bounce**: Spring animations for feedback

### Common Animations
```dart
// Fade In/Out
Duration: 300ms
Curve: Ease-out

// Slide Transitions
Duration: 300ms
Curve: Ease-out
Distance: 24px

// Scale Feedback
Duration: 150ms
Curve: Ease-out
Scale: 0.95 → 1.0
```

## 🔧 Customization Guide

### Theme Overrides
```dart
// Custom colors
AppColors.gfGreen = Color(0xFF00CC66);  // New green
AppColors.gfBlue = Color(0xFF0080FF);   // New blue

// Custom typography
textTheme: GoogleFonts.interTextTheme(), // Different font
```

### Component Customization
```dart
// Custom button style
GFButton(
  text: 'Custom',
  backgroundColor: Colors.purple,
  foregroundColor: Colors.white,
  borderRadius: BorderRadius.circular(16),
)
```

## 📋 Design Checklist

When creating new components:

### Visual Design
- [ ] Follows color system
- [ ] Uses correct typography
- [ ] Maintains spacing consistency
- [ ] Supports dark theme
- [ ] Handles different screen sizes

### Interaction Design
- [ ] Clear interactive states
- [ ] Appropriate touch targets
- [ ] Smooth animations
- [ ] Loading and error states
- [ ] Accessibility support

### Implementation
- [ ] Reusable and configurable
- [ ] Performance optimized
- [ ] Well documented
- [ ] Follows naming conventions
- [ ] Includes usage examples

## 🚀 Getting Started

1. **Review Components**: Explore the component library
2. **Understand Patterns**: Learn common design patterns
3. **Customize Theme**: Modify colors and typography
4. **Test Changes**: Verify across different screens
5. **Document Updates**: Keep design system current

This design system ensures consistency while providing flexibility for customization and growth.
