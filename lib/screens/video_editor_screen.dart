import 'dart:async';
import 'dart:io';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:path_provider/path_provider.dart';
import 'package:pro_image_editor/pro_image_editor.dart';
import 'package:pro_video_editor/pro_video_editor.dart';
import 'package:video_player/video_player.dart';
import '../theme/app_theme.dart';
import '../utils/app_logger.dart';

class VideoEditorScreen extends StatefulWidget {
  final File videoFile;
  final Function(File) onVideoEditingComplete;

  const VideoEditorScreen({
    super.key,
    required this.videoFile,
    required this.onVideoEditingComplete,
  });

  @override
  State<VideoEditorScreen> createState() => _VideoEditorScreenState();
}

class _VideoEditorScreenState extends State<VideoEditorScreen> {
  /// The target format for the exported video.
  final _outputFormat = VideoOutputFormat.mp4;

  /// Video editor configuration settings.
  late final VideoEditorConfigs _videoConfigs = const VideoEditorConfigs(
    initialMuted: false,
    initialPlay: false,
    isAudioSupported: true,
    minTrimDuration: Duration(seconds: 1),
  );

  /// Indicates whether a seek operation is in progress.
  bool _isSeeking = false;

  /// Stores the currently selected trim duration span.
  TrimDurationSpan? _durationSpan;

  /// Temporarily stores a pending trim duration span.
  TrimDurationSpan? _tempDurationSpan;

  /// Controls video playback and trimming functionalities.
  ProVideoController? _proVideoController;

  /// Stores generated thumbnails for the trimmer bar and filter background.
  List<ImageProvider>? _thumbnails;

  /// Holds information about the selected video.
  late VideoMetadata _videoMetadata;

  /// Number of thumbnails to generate across the video timeline.
  final int _thumbnailCount = 7;

  /// The video currently loaded in the editor.
  late final EditorVideo _video;

  String? _outputPath;

  late VideoPlayerController _videoController;

  final _taskId = DateTime.now().microsecondsSinceEpoch.toString();

  @override
  void initState() {
    super.initState();
    _video = EditorVideo.file(widget.videoFile);
    _initializePlayer();
  }

  @override
  void dispose() {
    _videoController.dispose();
    super.dispose();
  }

  /// Loads and sets [_videoMetadata] for the given [_video].
  Future<void> _setMetadata() async {
    _videoMetadata = await ProVideoEditor.instance.getMetadata(_video);
  }

  /// Generates thumbnails for the given [_video].
  void _generateThumbnails() {
    WidgetsBinding.instance.addPostFrameCallback((_) async {
      if (!mounted) return;
      var imageWidth =
          MediaQuery.sizeOf(context).width /
          _thumbnailCount *
          MediaQuery.devicePixelRatioOf(context);
      List<Uint8List> thumbnailList = [];

      /// On android `getKeyFrames` is a way faster than `getThumbnails` but
      /// the timestamps are more "random". If you want the best results i
      /// recommend you to use only `getThumbnails`.
      if (!kIsWeb && Platform.isAndroid) {
        thumbnailList = await ProVideoEditor.instance.getKeyFrames(
          KeyFramesConfigs(
            video: _video,
            outputSize: Size.square(imageWidth),
            boxFit: ThumbnailBoxFit.cover,
            maxOutputFrames: _thumbnailCount,
            outputFormat: ThumbnailFormat.jpeg,
          ),
        );
      } else {
        final duration = _videoMetadata.duration;
        final segmentDuration = duration.inMilliseconds / _thumbnailCount;
        thumbnailList = await ProVideoEditor.instance.getThumbnails(
          ThumbnailConfigs(
            video: _video,
            outputSize: Size.square(imageWidth),
            boxFit: ThumbnailBoxFit.cover,
            timestamps: List.generate(_thumbnailCount, (i) {
              final midpointMs = (i + 0.5) * segmentDuration;
              return Duration(milliseconds: midpointMs.round());
            }),
            outputFormat: ThumbnailFormat.jpeg,
          ),
        );
      }

      List<ImageProvider> temporaryThumbnails =
          thumbnailList.map(MemoryImage.new).toList();

      /// Optional precache every thumbnail
      var cacheList = temporaryThumbnails.map(
        (item) => precacheImage(item, context),
      );
      await Future.wait(cacheList);

      _thumbnails = temporaryThumbnails;
      if (_proVideoController != null) {
        _proVideoController!.thumbnails = _thumbnails;
      }
    });
  }

  void _initializePlayer() async {
    await _setMetadata();
    _generateThumbnails();
    _videoController = VideoPlayerController.file(widget.videoFile);
    await Future.wait([
      _videoController.initialize(),
      _videoController.setLooping(false),
      _videoController.setVolume(_videoConfigs.initialMuted ? 0 : 100),
      _videoConfigs.initialPlay
          ? _videoController.play()
          : _videoController.pause(),
    ]);

    if (!mounted) return;

    // Get the actual video display size from the video controller
    final videoSize = _videoController.value.size;
    final actualResolution = Size(videoSize.width, videoSize.height);

    AppLogger.media(
      'Metadata resolution: ${_videoMetadata.resolution.width}x${_videoMetadata.resolution.height}',
    );
    AppLogger.media(
      'Video controller size: ${videoSize.width}x${videoSize.height}',
    );
    AppLogger.media(
      'Video controller aspect ratio: ${_videoController.value.aspectRatio}',
    );
    AppLogger.media(
      'Calculated aspect ratio: ${videoSize.width / videoSize.height}',
    );

    _proVideoController = ProVideoController(
      videoPlayer: _buildVideoPlayer(),
      initialResolution: actualResolution,
      videoDuration: _videoMetadata.duration,
      fileSize: _videoMetadata.fileSize,
      thumbnails: _thumbnails,
    );

    // Initialize trim span to full video duration
    _durationSpan = TrimDurationSpan(
      start: Duration.zero,
      end: _videoMetadata.duration,
    );

    _videoController.addListener(_onDurationChange);
    setState(() {});
  }

  void _onDurationChange() {
    var totalVideoDuration = _videoMetadata.duration;
    var duration = _videoController.value.position;
    _proVideoController!.setPlayTime(duration);

    if (_durationSpan != null && duration >= _durationSpan!.end) {
      _seekToPosition(_durationSpan!);
    } else if (duration >= totalVideoDuration) {
      _seekToPosition(
        TrimDurationSpan(start: Duration.zero, end: totalVideoDuration),
      );
    }
  }

  Future<void> _seekToPosition(TrimDurationSpan span) async {
    _durationSpan = span;
    if (_isSeeking) {
      _tempDurationSpan = span; // Store the latest seek request
      return;
    }

    _isSeeking = true;
    _proVideoController!.pause();
    _proVideoController!.setPlayTime(_durationSpan!.start);
    await _videoController.pause();
    await _videoController.seekTo(span.start);
    _isSeeking = false;

    // Check if there's a pending seek request
    if (_tempDurationSpan != null) {
      TrimDurationSpan nextSeek = _tempDurationSpan!;
      _tempDurationSpan = null; // Clear the pending seek
      await _seekToPosition(nextSeek); // Process the latest request
    }
  }

  /// Generates the final video based on the given [parameters].
  ///
  /// Applies blur, color filters, cropping, rotation, flipping, and trimming
  /// before exporting using FFmpeg. Measures and stores the generation time.
  Future<void> generateVideo(CompleteParameters parameters) async {
    final stopwatch = Stopwatch()..start();
    unawaited(_videoController.pause());

    // Get the actual video display size for proper overlay scaling
    final videoSize = _videoController.value.size;
    AppLogger.media(
      'Export - Video controller size: ${videoSize.width}x${videoSize.height}',
    );
    AppLogger.media(
      'Export - Video controller aspect ratio: ${_videoController.value.aspectRatio}',
    );
    AppLogger.media(
      'Export - Metadata resolution: ${_videoMetadata.resolution.width}x${_videoMetadata.resolution.height}',
    );
    AppLogger.media(
      'Export - Parameters image bytes length: ${parameters.image.length}',
    );
    AppLogger.media('Export - Has layers: ${parameters.layers.isNotEmpty}');
    AppLogger.media('Export - Is transformed: ${parameters.isTransformed}');

    final audioEnabled = _proVideoController?.isAudioEnabled ?? true;
    AppLogger.media('Export - Audio enabled: $audioEnabled');

    var exportModel = RenderVideoModel(
      id: _taskId,
      video: _video,
      outputFormat: _outputFormat,
      enableAudio: audioEnabled,
      imageBytes: parameters.layers.isNotEmpty ? parameters.image : null,
      blur: parameters.blur,
      colorMatrixList: parameters.colorFilters,
      startTime: parameters.startTime,
      endTime: parameters.endTime,
      // Always provide transform to ensure proper overlay scaling
      transform:
          parameters.isTransformed
              ? ExportTransform(
                width: parameters.cropWidth,
                height: parameters.cropHeight,
                rotateTurns: parameters.rotateTurns,
                x: parameters.cropX,
                y: parameters.cropY,
                flipX: parameters.flipX,
                flipY: parameters.flipY,
              )
              : ExportTransform(
                // Explicitly set the video size to ensure proper overlay scaling
                width: videoSize.width.round(),
                height: videoSize.height.round(),
                rotateTurns: 0,
                x: 0,
                y: 0,
                flipX: false,
                flipY: false,
              ),
      // bitrate: _videoMetadata.bitrate, // Commented out like in example
    );

    final directory = await getTemporaryDirectory();
    final now = DateTime.now().millisecondsSinceEpoch;
    _outputPath = await ProVideoEditor.instance.renderVideoToFile(
      '${directory.path}/my_video_$now.mp4',
      exportModel,
    );

    final videoGenerationTime = stopwatch.elapsed;
    AppLogger.media(
      'Video export completed in ${videoGenerationTime.inMilliseconds}ms',
    );
  }

  /// Closes the video editor and opens a preview screen if a video was
  /// exported.
  ///
  /// If [_outputPath] is available, it navigates to the completion callback.
  /// Afterwards, it pops the current editor page.
  void onCloseEditor(EditorMode editorMode) async {
    if (editorMode != EditorMode.main) return Navigator.pop(context);

    if (_outputPath != null) {
      // Return the processed video file
      widget.onVideoEditingComplete(File(_outputPath!));
      _outputPath = null;
    } else {
      return Navigator.pop(context);
    }
  }

  @override
  Widget build(BuildContext context) {
    return AnimatedSwitcher(
      duration: const Duration(milliseconds: 220),
      child:
          _proVideoController == null ? _buildLoadingWidget() : _buildEditor(),
    );
  }

  Widget _buildLoadingWidget() {
    return Scaffold(
      backgroundColor: AppColors.darkBlue,
      body: const Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            CircularProgressIndicator(
              valueColor: AlwaysStoppedAnimation<Color>(AppColors.gfGreen),
            ),
            SizedBox(height: 16),
            Text(
              'Loading video editor...',
              style: TextStyle(color: AppColors.gfOffWhite, fontSize: 16),
            ),
          ],
        ),
      ),
    );
  }

  final _editor = GlobalKey<ProImageEditorState>();

  Widget _buildEditor() {
    return ProImageEditor.video(
      _proVideoController!,
      key: _editor,
      callbacks: ProImageEditorCallbacks(
        onCompleteWithParameters: generateVideo,
        onCloseEditor: onCloseEditor,
        videoEditorCallbacks: VideoEditorCallbacks(
          onPause: _videoController.pause,
          onPlay: _videoController.play,
          onMuteToggle: (isMuted) {
            _videoController.setVolume(isMuted ? 0 : 100);
          },
          onTrimSpanUpdate: (durationSpan) {
            // Enforce 30-second maximum duration
            final trimDuration = durationSpan.end - durationSpan.start;
            if (trimDuration > const Duration(seconds: 30)) {
              // Adjust the end time to maintain 30-second limit
              final adjustedEnd =
                  durationSpan.start + const Duration(seconds: 30);
              durationSpan = TrimDurationSpan(
                start: durationSpan.start,
                end: adjustedEnd,
              );
            }

            // Update the trim span and seek to start position
            _durationSpan = durationSpan;
            if (_videoController.value.isPlaying) {
              _videoController.pause();
            }
            // Seek to the start of the trim span
            _videoController.seekTo(durationSpan.start);
            _proVideoController!.setPlayTime(durationSpan.start);
          },
          onTrimSpanEnd: (durationSpan) {
            // Final trim span update
            _seekToPosition(durationSpan);
          },
        ),
      ),
      configs: _buildEditorConfigs(),
    );
  }

  ProImageEditorConfigs _buildEditorConfigs() {
    return ProImageEditorConfigs(
      designMode: ImageEditorDesignMode.material,
      theme: ThemeData(
        useMaterial3: true,
        colorScheme: ColorScheme.fromSeed(
          seedColor: AppColors.gfGreen,
          brightness: Brightness.dark,
        ),
        scaffoldBackgroundColor: AppColors.darkBlue,
        appBarTheme: const AppBarTheme(
          backgroundColor: AppColors.darkBlue,
          foregroundColor: AppColors.gfOffWhite,
          iconTheme: IconThemeData(color: AppColors.gfOffWhite),
        ),
      ),
      dialogConfigs: DialogConfigs(
        widgets: DialogWidgets(
          loadingDialog: (message, configs) => _buildProgressDialog(),
        ),
      ),
      layerInteraction: const LayerInteractionConfigs(
        hideToolbarOnInteraction: false,
      ),
      paintEditor: const PaintEditorConfigs(
        /// Blur and pixelate are not supported.
        enableModePixelate: false,
        enableModeBlur: false,
      ),
      // Enable emoji editor with proper configuration
      emojiEditor: EmojiEditorConfigs(
        enabled: true,
        checkPlatformCompatibility: !kIsWeb,
        style: EmojiEditorStyle(
          backgroundColor: Colors.transparent,
          textStyle: DefaultEmojiTextStyle.copyWith(
            fontSize: 48, // Consistent emoji size
          ),
          emojiViewConfig: EmojiViewConfig(
            gridPadding: EdgeInsets.zero,
            horizontalSpacing: 0,
            verticalSpacing: 0,
            recentsLimit: 40,
            backgroundColor: Colors.transparent,
            buttonMode: ButtonMode.MATERIAL,
            loadingIndicator: const Center(
              child: CircularProgressIndicator(
                valueColor: AlwaysStoppedAnimation<Color>(AppColors.gfGreen),
              ),
            ),
            columns: 8, // Fixed column count for consistency
            emojiSizeMax: 64,
            replaceEmojiOnLimitExceed: false,
          ),
          bottomActionBarConfig: const BottomActionBarConfig(enabled: false),
        ),
      ),
      // Configure video editor with proper trimming
      videoEditor: _videoConfigs.copyWith(
        playTimeSmoothingDuration: const Duration(milliseconds: 600),
      ),
      // Disable filter editor
      filterEditor: const FilterEditorConfigs(enabled: false),
      // Ensure proper aspect ratio handling for overlays
      mainEditor: MainEditorConfigs(
        enableZoom: true,
        editorMinScale: 1.0,
        editorMaxScale: 3.0,
        enableDoubleTapZoom: true,
        doubleTapZoomFactor: 2.0,
        style: const MainEditorStyle(
          bottomBarColor: AppColors.gfGreen,
          bottomBarBackground: AppColors.darkBlue,
        ),
      ),
    );
  }

  Widget _buildProgressDialog() {
    return StreamBuilder<ProgressModel>(
      stream: ProVideoEditor.instance.progressStreamById(_taskId),
      builder: (context, snapshot) {
        var progress = snapshot.data?.progress ?? 0;
        return AlertDialog(
          backgroundColor: AppColors.darkBlue,
          content: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              CircularProgressIndicator(
                value: progress,
                valueColor: const AlwaysStoppedAnimation<Color>(
                  AppColors.gfGreen,
                ),
              ),
              const SizedBox(height: 16),
              Text(
                'Processing video... ${(progress * 100).toStringAsFixed(1)}%',
                style: const TextStyle(
                  color: AppColors.gfOffWhite,
                  fontSize: 16,
                ),
              ),
            ],
          ),
        );
      },
    );
  }

  Widget _buildVideoPlayer() {
    return Center(
      child: AspectRatio(
        aspectRatio: _videoController.value.size.aspectRatio,
        child: VideoPlayer(_videoController),
      ),
    );
  }
}
