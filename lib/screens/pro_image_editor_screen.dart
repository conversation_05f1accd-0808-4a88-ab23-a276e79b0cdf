import 'dart:io';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:pro_image_editor/pro_image_editor.dart';
import '../theme/app_theme.dart';
import '../models/flare_model.dart';
import '../services/flare_service.dart';

class ProImageEditorScreen extends StatefulWidget {
  final File imageFile;
  final Function(Uint8List) onImageEditingComplete;

  const ProImageEditorScreen({
    super.key,
    required this.imageFile,
    required this.onImageEditingComplete,
  });

  @override
  State<ProImageEditorScreen> createState() => _ProImageEditorScreenState();
}

class _ProImageEditorScreenState extends State<ProImageEditorScreen> {
  final FlareService _flareService = FlareService();

  @override
  Widget build(BuildContext context) {
    return ProImageEditor.file(
      widget.imageFile,
      configs: ProImageEditorConfigs(
        theme: ThemeData(
          useMaterial3: true,
          colorScheme: ColorScheme.fromSeed(
            seedColor: AppColors.gfGreen,
            brightness: Brightness.dark,
          ),
          bottomNavigationBarTheme: const BottomNavigationBarThemeData(
            backgroundColor: AppColors.darkBlue,
            selectedItemColor: AppColors.gfGreen,
            unselectedItemColor: AppColors.gfGrayText,
          ),
        ),
        mainEditor: MainEditorConfigs(
          enableZoom: true,
          editorMinScale: 1.0,
          editorMaxScale: 5.0,
          enableDoubleTapZoom: true,
          doubleTapZoomFactor: 2.0,
          style: const MainEditorStyle(
            bottomBarColor: AppColors.gfGreen, // Make all icons green
            bottomBarBackground: AppColors.darkBlue,
          ),
        ),
        i18n: const I18n(
          stickerEditor: I18nStickerEditor(bottomNavigationBarText: 'Flare'),
        ),
        // Strategic ordering: disable middle editors to bring flare closer to left
        paintEditor: const PaintEditorConfigs(enabled: true),
        textEditor: const TextEditorConfigs(enabled: true),
        cropRotateEditor: const CropRotateEditorConfigs(enabled: true),
        filterEditor: const FilterEditorConfigs(enabled: false),
        tuneEditor: const TuneEditorConfigs(enabled: false),
        blurEditor: const BlurEditorConfigs(enabled: false),
        emojiEditor: const EmojiEditorConfigs(
          enabled: false,
        ), // Disable emoji to move flare left
        stickerEditor: StickerEditorConfigs(
          enabled: true,
          icons: const StickerEditorIcons(
            bottomNavBar: Icons.auto_awesome, // Sparkles icon for flare
          ),
          style: const StickerEditorStyle(
            bottomSheetBackgroundColor: AppColors.darkBlue,
          ),
          builder:
              (setLayer, scrollController) =>
                  _buildFlareStickers(setLayer, scrollController),
        ),
      ),
      callbacks: ProImageEditorCallbacks(
        onImageEditingComplete: (Uint8List bytes) async {
          widget.onImageEditingComplete(bytes);
        },
      ),
    );
  }

  Widget _buildFlareStickers(
    Function(WidgetLayer) setLayer,
    ScrollController scrollController,
  ) {
    return Container(
      height: 300,
      decoration: const BoxDecoration(
        color: AppColors.darkBlue,
        borderRadius: BorderRadius.vertical(top: Radius.circular(16)),
      ),
      child: DefaultTabController(
        length: FlareCategory.values.length,
        child: Column(
          children: [
            // Handle bar
            Container(
              margin: const EdgeInsets.only(top: 8),
              width: 40,
              height: 4,
              decoration: BoxDecoration(
                color: AppColors.gfGrayText,
                borderRadius: BorderRadius.circular(2),
              ),
            ),

            // Header
            const Padding(
              padding: EdgeInsets.all(16.0),
              child: Text(
                'Add Flare Stickers',
                style: TextStyle(
                  fontSize: 18,
                  fontWeight: FontWeight.bold,
                  color: AppColors.gfOffWhite,
                ),
              ),
            ),

            // Category tabs
            TabBar(
              isScrollable: true,
              indicatorColor: AppColors.gfGreen,
              labelColor: AppColors.gfGreen,
              unselectedLabelColor: AppColors.gfGrayText,
              tabs:
                  FlareCategory.values.map((category) {
                    return Tab(text: category.displayName);
                  }).toList(),
            ),

            // Flare grid
            Expanded(
              child: TabBarView(
                children:
                    FlareCategory.values.map((category) {
                      return _buildFlareGrid(category, setLayer);
                    }).toList(),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildFlareGrid(
    FlareCategory category,
    Function(WidgetLayer) setLayer,
  ) {
    final flareItems = _flareService.getFlareForCategory(category);

    return Padding(
      padding: const EdgeInsets.all(16.0),
      child: GridView.builder(
        gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
          crossAxisCount: 4,
          crossAxisSpacing: 12,
          mainAxisSpacing: 12,
          childAspectRatio: 1.0,
        ),
        itemCount: flareItems.length,
        itemBuilder: (context, index) {
          final flareItem = flareItems[index];
          return _buildFlareTile(flareItem, setLayer);
        },
      ),
    );
  }

  Widget _buildFlareTile(FlareItem flareItem, Function(WidgetLayer) setLayer) {
    return GestureDetector(
      onTap: () {
        // Add the flare as a sticker layer
        setLayer(
          WidgetLayer(
            widget: Image.asset(
              flareItem.assetPath,
              width: 100,
              height: 100,
              fit: BoxFit.contain,
            ),
          ),
        );
      },
      child: Container(
        decoration: BoxDecoration(
          color: AppColors.gfDarkBackground100,
          borderRadius: BorderRadius.circular(8),
          border: Border.all(
            color: AppColors.gfGrayText.withValues(alpha: 0.3),
            width: 1,
          ),
        ),
        child: ClipRRect(
          borderRadius: BorderRadius.circular(8),
          child: Image.asset(
            flareItem.assetPath,
            fit: BoxFit.contain,
            errorBuilder: (context, error, stackTrace) {
              return Container(
                color: AppColors.gfDarkBackground100,
                child: const Icon(
                  Icons.image_not_supported,
                  color: AppColors.gfGrayText,
                  size: 24,
                ),
              );
            },
          ),
        ),
      ),
    );
  }
}
