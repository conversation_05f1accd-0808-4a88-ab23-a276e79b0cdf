import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../models/channel_model.dart';
import '../components/index.dart';
import '../providers/channels_provider.dart';
import '../theme/app_theme.dart';
import '../widgets/feed_item.dart';

class ChannelDetailScreen extends StatefulWidget {
  final ChannelModel channel;

  const ChannelDetailScreen({super.key, required this.channel});

  @override
  State<ChannelDetailScreen> createState() => _ChannelDetailScreenState();
}

class _ChannelDetailScreenState extends State<ChannelDetailScreen> {
  final PageController _pageController = PageController();
  int _currentIndex = 0;

  @override
  void initState() {
    super.initState();

    // Load channel posts when screen initializes
    WidgetsBinding.instance.addPostFrameCallback((_) {
      final channelsProvider = Provider.of<ChannelsProvider>(
        context,
        listen: false,
      );
      channelsProvider.selectChannel(widget.channel);
    });
  }

  @override
  void dispose() {
    _pageController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppColors.gfDarkBlue,
      body: Consumer<ChannelsProvider>(
        builder: (context, channelsProvider, child) {
          return Stack(
            children: [
              // Main content
              if (channelsProvider.channelPostsLoading)
                const Center(
                  child: CircularProgressIndicator(
                    valueColor: AlwaysStoppedAnimation<Color>(
                      AppColors.gfGreen,
                    ),
                  ),
                )
              else if (channelsProvider.channelPostsError != null)
                Center(
                  child: Column(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      Icon(
                        Icons.error_outline,
                        size: 64,
                        color: Colors.red.shade400,
                      ),
                      const SizedBox(height: 16),
                      Text(
                        'Failed to load posts',
                        style: Theme.of(context).textTheme.headlineSmall
                            ?.copyWith(color: Colors.white),
                      ),
                      const SizedBox(height: 8),
                      Text(
                        channelsProvider.channelPostsError!,
                        style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                          color: Colors.grey.shade400,
                        ),
                        textAlign: TextAlign.center,
                      ),
                      const SizedBox(height: 24),
                      ElevatedButton(
                        onPressed:
                            () =>
                                channelsProvider.selectChannel(widget.channel),
                        style: ElevatedButton.styleFrom(
                          backgroundColor: AppColors.gfGreen,
                          foregroundColor: AppColors.gfDarkBlue,
                        ),
                        child: const Text('Retry'),
                      ),
                    ],
                  ),
                )
              else if (channelsProvider.channelPosts.isEmpty)
                const Center(
                  child: Column(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      Icon(
                        Icons.post_add_outlined,
                        size: 64,
                        color: Colors.grey,
                      ),
                      SizedBox(height: 16),
                      Text(
                        'No posts yet',
                        style: TextStyle(
                          color: Colors.white,
                          fontSize: 18,
                          fontWeight: FontWeight.w500,
                        ),
                      ),
                      SizedBox(height: 8),
                      Text(
                        'Be the first to post in this channel!',
                        style: TextStyle(color: Colors.grey, fontSize: 14),
                      ),
                    ],
                  ),
                )
              else
                PageView.builder(
                  controller: _pageController,
                  scrollDirection: Axis.vertical,
                  itemCount: channelsProvider.channelPosts.length,
                  physics: const ClampingScrollPhysics(),
                  onPageChanged: (index) {
                    setState(() {
                      _currentIndex = index;
                    });
                  },
                  itemBuilder: (context, index) {
                    final post = channelsProvider.channelPosts[index];
                    return FeedItem(
                      post: post,
                      isVisible: index == _currentIndex,
                    );
                  },
                ),

              // Feed overlays - same as main feed
              if (channelsProvider.channelPosts.isNotEmpty &&
                  _currentIndex < channelsProvider.channelPosts.length) ...[
                // Top overlay - User info
                GFFeedTopOverlay(
                  post: channelsProvider.channelPosts[_currentIndex],
                ),

                // Bottom overlay - Action buttons
                GFFeedBottomOverlay(
                  post: channelsProvider.channelPosts[_currentIndex],
                ),
              ],

              // Channel header overlay
              Positioned(
                top: 0,
                left: 0,
                right: 0,
                child: Container(
                  decoration: BoxDecoration(
                    gradient: LinearGradient(
                      begin: Alignment.topCenter,
                      end: Alignment.bottomCenter,
                      colors: [
                        Colors.black.withValues(alpha: 179), // 0.7 opacity
                        Colors.transparent,
                      ],
                    ),
                  ),
                  child: SafeArea(
                    child: Padding(
                      padding: const EdgeInsets.all(16),
                      child: Row(
                        children: [
                          // Back button
                          IconButton(
                            onPressed: () => Navigator.of(context).pop(),
                            icon: const Icon(
                              Icons.arrow_back,
                              color: Colors.white,
                            ),
                          ),
                          const SizedBox(width: 8),

                          // Channel info
                          Expanded(
                            child: Column(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: [
                                Text(
                                  widget.channel.name,
                                  style: const TextStyle(
                                    color: Colors.white,
                                    fontSize: 18,
                                    fontWeight: FontWeight.bold,
                                  ),
                                ),
                                Text(
                                  '${widget.channel.memberCount} members',
                                  style: TextStyle(
                                    color: Colors.grey.shade300,
                                    fontSize: 14,
                                  ),
                                ),
                              ],
                            ),
                          ),

                          // Join button (only show if not a member)
                          if (!widget.channel.isUserMember)
                            ElevatedButton(
                              onPressed: () => _handleJoin(channelsProvider),
                              style: ElevatedButton.styleFrom(
                                backgroundColor: AppColors.gfGreen,
                                foregroundColor: AppColors.gfDarkBlue,
                                shape: RoundedRectangleBorder(
                                  borderRadius: BorderRadius.circular(20),
                                ),
                                padding: const EdgeInsets.symmetric(
                                  horizontal: 16,
                                  vertical: 8,
                                ),
                              ),
                              child: const Text(
                                'Join',
                                style: TextStyle(
                                  fontSize: 14,
                                  fontWeight: FontWeight.w600,
                                ),
                              ),
                            ),
                        ],
                      ),
                    ),
                  ),
                ),
              ),
            ],
          );
        },
      ),
    );
  }

  void _handleJoin(ChannelsProvider channelsProvider) async {
    final success = await channelsProvider.joinChannel(widget.channel.id);
    if (success) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Joined ${widget.channel.name}'),
            backgroundColor: AppColors.gfGreen,
          ),
        );
      }
    } else {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Failed to join ${widget.channel.name}'),
            backgroundColor: Colors.red.shade700,
          ),
        );
      }
    }
  }
}
