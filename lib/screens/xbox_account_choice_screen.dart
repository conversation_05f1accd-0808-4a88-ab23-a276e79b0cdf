import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../theme/app_theme.dart';

import '../services/api_service.dart';
import '../utils/app_logger.dart';
import '../widgets/xbox_icon.dart';
import '../providers/auth_provider.dart';
import '../services/aws_auth_service.dart' as aws;
import '../models/user_model.dart';
import 'login_screen.dart';
import 'username_selection_screen.dart';

class XboxAccountChoiceScreen extends StatefulWidget {
  final Map<String, dynamic> accountOptions;
  final Map<String, dynamic> xboxData;

  const XboxAccountChoiceScreen({
    super.key,
    required this.accountOptions,
    required this.xboxData,
  });

  @override
  State<XboxAccountChoiceScreen> createState() =>
      _XboxAccountChoiceScreenState();
}

class _XboxAccountChoiceScreenState extends State<XboxAccountChoiceScreen> {
  bool _isLoading = false;

  @override
  Widget build(BuildContext context) {
    final existingAccount =
        widget.accountOptions['existingAccount'] as Map<String, dynamic>;
    final xboxGamertag = widget.xboxData['gamertag'] as String;

    return Scaffold(
      backgroundColor: AppColors.gfDarkBackground100,
      appBar: AppBar(
        backgroundColor: AppColors.gfDarkBackground100,
        elevation: 0,
        title: const Text(
          'Xbox Account Choice',
          style: TextStyle(
            color: AppColors.gfOffWhite,
            fontSize: 20,
            fontWeight: FontWeight.w600,
          ),
        ),
        leading: IconButton(
          icon: const Icon(Icons.close, color: AppColors.gfOffWhite),
          onPressed: () => Navigator.of(context).pop(),
        ),
      ),
      body: SafeArea(
        child: Padding(
          padding: const EdgeInsets.all(24.0),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Xbox account info
              Container(
                padding: const EdgeInsets.all(16),
                decoration: BoxDecoration(
                  color: AppColors.gfCardBackground,
                  borderRadius: BorderRadius.circular(12),
                  border: Border.all(
                    color: const Color(0xFF107C10), // Xbox green
                    width: 2,
                  ),
                ),
                child: Row(
                  children: [
                    Container(
                      width: 48,
                      height: 48,
                      decoration: BoxDecoration(
                        color: const Color(0xFF107C10),
                        borderRadius: BorderRadius.circular(8),
                      ),
                      child: const Center(
                        child: XboxIcon(size: 24, color: Colors.white),
                      ),
                    ),
                    const SizedBox(width: 16),
                    Expanded(
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          const Text(
                            'Xbox Live',
                            style: TextStyle(
                              color: AppColors.gfOffWhite,
                              fontSize: 16,
                              fontWeight: FontWeight.w600,
                            ),
                          ),
                          const SizedBox(height: 4),
                          Text(
                            xboxGamertag,
                            style: TextStyle(
                              color: AppColors.gfOffWhite.withAlpha(
                                204,
                              ), // 0.8 opacity
                              fontSize: 14,
                            ),
                          ),
                        ],
                      ),
                    ),
                  ],
                ),
              ),

              const SizedBox(height: 32),

              const Text(
                'This Xbox account was previously linked to an existing GameFlex account. What would you like to do?',
                style: TextStyle(
                  color: AppColors.gfOffWhite,
                  fontSize: 16,
                  height: 1.5,
                ),
              ),

              const SizedBox(height: 32),

              // Option 1: Link to existing account
              _buildOptionCard(
                title: 'Sign in to existing account',
                subtitle:
                    'Log in to ${existingAccount['username'] ?? existingAccount['firstName'] ?? 'your account'} to re-link Xbox',
                icon: Icons.person,
                onTap: () => _goToLoginForRelink(),
              ),

              const SizedBox(height: 16),

              // Option 2: Create new account
              _buildOptionCard(
                title: 'Create new account',
                subtitle: 'Sign up for a new GameFlex account',
                icon: Icons.add_circle_outline,
                onTap: () => _goToSignUp(),
              ),

              const Spacer(),

              if (_isLoading)
                const Center(
                  child: CircularProgressIndicator(color: AppColors.gfGreen),
                ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildOptionCard({
    required String title,
    required String subtitle,
    required IconData icon,
    required VoidCallback onTap,
  }) {
    return GestureDetector(
      onTap: _isLoading ? null : onTap,
      child: Container(
        padding: const EdgeInsets.all(20),
        decoration: BoxDecoration(
          color: AppColors.gfCardBackground,
          borderRadius: BorderRadius.circular(12),
          border: Border.all(
            color: AppColors.gfGreen.withAlpha(77), // 0.3 opacity
            width: 1,
          ),
        ),
        child: Row(
          children: [
            Container(
              width: 48,
              height: 48,
              decoration: BoxDecoration(
                color: AppColors.gfGreen.withAlpha(51), // 0.2 opacity
                borderRadius: BorderRadius.circular(8),
              ),
              child: Icon(icon, color: AppColors.gfGreen, size: 24),
            ),
            const SizedBox(width: 16),
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    title,
                    style: const TextStyle(
                      color: AppColors.gfOffWhite,
                      fontSize: 16,
                      fontWeight: FontWeight.w600,
                    ),
                  ),
                  const SizedBox(height: 4),
                  Text(
                    subtitle,
                    style: TextStyle(
                      color: AppColors.gfOffWhite.withAlpha(179), // 0.7 opacity
                      fontSize: 14,
                    ),
                  ),
                ],
              ),
            ),
            Icon(Icons.arrow_forward_ios, color: AppColors.gfGreen, size: 16),
          ],
        ),
      ),
    );
  }

  void _goToLoginForRelink() {
    AppLogger.info('Navigating to login screen for account re-linking');

    // Show a message explaining what the user needs to do
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(
          'Please log in to your existing account to re-link your Xbox account',
        ),
        backgroundColor: AppColors.gfGreen,
        duration: Duration(seconds: 3),
      ),
    );

    // Navigate back to login screen
    Navigator.of(context).pushAndRemoveUntil(
      MaterialPageRoute(builder: (context) => const LoginScreen()),
      (route) => false,
    );
  }

  Future<void> _goToSignUp() async {
    setState(() {
      _isLoading = true;
    });

    try {
      AppLogger.info('Creating new Xbox account');

      final response = await ApiService.instance.makeRequest(
        method: 'POST',
        path: '/xbox/create-new-account',
        body: {'xboxData': widget.xboxData},
      );

      if (response.statusCode == 201) {
        final data = ApiService.instance.parseResponse(response);
        AppLogger.info('Successfully created new Xbox account');

        // Parse user data and tokens from response
        final userData = data['user'] as Map<String, dynamic>?;
        final tokensData = data['tokens'] as Map<String, dynamic>?;

        if (userData != null && tokensData != null) {
          // Create user model
          final user = UserModel.fromJson(userData);

          // Store the tokens and user data in AWS auth service
          await aws.AwsAuthService.instance.storeXboxAuthData(
            user,
            tokensData['accessToken'] as String,
            tokensData['refreshToken'] as String,
            tokensData['idToken'] as String,
          );

          if (mounted) {
            // Update auth provider with the new user
            final authProvider = Provider.of<AuthProvider>(
              context,
              listen: false,
            );
            authProvider.setUserFromXboxCreation(user);

            // Navigate to username selection
            Navigator.of(context).pushReplacement(
              MaterialPageRoute(
                builder: (context) => const UsernameSelectionScreen(),
              ),
            );
          }
        } else {
          // Missing required data in response
          if (mounted) {
            ScaffoldMessenger.of(context).showSnackBar(
              const SnackBar(
                content: Text(
                  'Account created but missing authentication data',
                ),
                backgroundColor: Colors.red,
              ),
            );
          }
        }
      } else {
        final data = ApiService.instance.parseResponse(response);
        final errorMessage =
            data['error'] ?? 'Failed to create new Xbox account';
        AppLogger.error('Failed to create new Xbox account: $errorMessage');

        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text('Failed to create new account: $errorMessage'),
              backgroundColor: Colors.red,
            ),
          );
        }
      }
    } catch (e) {
      AppLogger.error('Error creating new Xbox account: $e');

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Error creating new account: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    } finally {
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
      }
    }
  }
}
