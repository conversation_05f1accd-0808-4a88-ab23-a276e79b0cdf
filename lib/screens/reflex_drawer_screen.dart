import 'package:flutter/material.dart';
import 'package:provider/provider.dart';

import '../models/reflex_model.dart';
import '../models/post_model.dart';
import '../providers/reflex_provider.dart';
import '../theme/app_theme.dart';
import '../utils/app_logger.dart';
import '../widgets/reflex_floating_card.dart';
import '../screens/reflexes_feed_screen.dart';

class ReflexDrawerScreen extends StatefulWidget {
  final PostModel post;
  final int initialIndex;

  const ReflexDrawerScreen({
    super.key,
    required this.post,
    this.initialIndex = 0,
  });

  @override
  State<ReflexDrawerScreen> createState() => _ReflexDrawerScreenState();
}

class _ReflexDrawerScreenState extends State<ReflexDrawerScreen>
    with TickerProviderStateMixin {
  final PageController _pageController = PageController();
  late AnimationController _slideController;
  late Animation<Offset> _slideAnimation;
  
  int _currentIndex = 0;
  List<ReflexModel> _allReflexes = [];
  bool _isLoading = false;
  String? _error;

  @override
  void initState() {
    super.initState();
    _currentIndex = widget.initialIndex;
    
    // Initialize slide animation controller
    _slideController = AnimationController(
      duration: const Duration(milliseconds: 300),
      vsync: this,
    );
    
    _slideAnimation = Tween<Offset>(
      begin: const Offset(1.0, 0.0), // Start from right
      end: Offset.zero,
    ).animate(CurvedAnimation(
      parent: _slideController,
      curve: Curves.easeInOut,
    ));
    
    _loadReflexesFromPosts();
    
    // Start slide animation
    _slideController.forward();
  }

  @override
  void dispose() {
    _pageController.dispose();
    _slideController.dispose();
    super.dispose();
  }

  Future<void> _loadReflexesFromPosts() async {
    if (_isLoading) return;

    setState(() {
      _isLoading = true;
      _error = null;
    });

    try {
      AppLogger.debug(
        'ReflexDrawerScreen: Loading reflexes for post ${widget.post.id}',
      );

      final reflexProvider = Provider.of<ReflexProvider>(
        context,
        listen: false,
      );

      // Load reflexes for the specific post only
      await reflexProvider.loadReflexesForPost(widget.post.id);
      final postReflexes = reflexProvider.getReflexesForPost(widget.post.id);

      // Sort by creation time (most recent first)
      postReflexes.sort((a, b) => b.createdAt.compareTo(a.createdAt));

      setState(() {
        _allReflexes = postReflexes;
        _isLoading = false;
      });

      // Set initial page after loading
      if (_allReflexes.isNotEmpty && _currentIndex < _allReflexes.length) {
        WidgetsBinding.instance.addPostFrameCallback((_) {
          if (_pageController.hasClients) {
            _pageController.animateToPage(
              _currentIndex,
              duration: const Duration(milliseconds: 300),
              curve: Curves.easeInOut,
            );
          }
        });
      }

      AppLogger.debug(
        'ReflexDrawerScreen: Loaded ${postReflexes.length} reflexes for post ${widget.post.id}',
      );
    } catch (e, stackTrace) {
      AppLogger.error(
        'ReflexDrawerScreen: Error loading reflexes',
        error: e,
        stackTrace: stackTrace,
      );
      setState(() {
        _error = e.toString();
        _isLoading = false;
      });
    }
  }

  Future<void> _refreshReflexes() async {
    setState(() {
      _allReflexes.clear();
    });
    await _loadReflexesFromPosts();
  }

  void _navigateToFullReflex(int index) {
    AppLogger.debug(
      'ReflexDrawerScreen: Navigating to full reflex at index $index',
    );
    
    Navigator.of(context).push(
      MaterialPageRoute(
        builder: (context) => ReflexesFeedScreen(
          post: widget.post,
          initialIndex: index,
        ),
      ),
    ).then((_) {
      // When returning from full reflex, maintain the same position
      if (_pageController.hasClients && index < _allReflexes.length) {
        _pageController.animateToPage(
          index,
          duration: const Duration(milliseconds: 300),
          curve: Curves.easeInOut,
        );
      }
    });
  }

  void _closeDrawer() {
    _slideController.reverse().then((_) {
      Navigator.of(context).pop();
    });
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.black.withValues(alpha: 0.8),
      body: SlideTransition(
        position: _slideAnimation,
        child: _buildBody(),
      ),
    );
  }

  Widget _buildBody() {
    if (_isLoading) {
      return const Center(
        child: CircularProgressIndicator(
          valueColor: AlwaysStoppedAnimation<Color>(AppColors.gfGreen),
        ),
      );
    }

    if (_error != null) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            const Icon(
              Icons.error_outline,
              color: Colors.red,
              size: 64,
            ),
            const SizedBox(height: 16),
            Text(
              'Error loading reflexes',
              style: const TextStyle(
                color: AppColors.gfOffWhite,
                fontSize: 18,
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 8),
            Text(
              _error!,
              style: const TextStyle(
                color: AppColors.gfGrayText,
                fontSize: 14,
              ),
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 24),
            ElevatedButton(
              onPressed: _refreshReflexes,
              style: ElevatedButton.styleFrom(
                backgroundColor: AppColors.gfGreen,
              ),
              child: const Text('Retry'),
            ),
          ],
        ),
      );
    }

    if (_allReflexes.isEmpty) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            const Icon(
              Icons.photo_library_outlined,
              color: AppColors.gfGrayText,
              size: 64,
            ),
            const SizedBox(height: 16),
            const Text(
              'No reflexes yet',
              style: TextStyle(
                color: AppColors.gfOffWhite,
                fontSize: 18,
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 8),
            const Text(
              'Be the first to create a reflex for this post!',
              style: TextStyle(
                color: AppColors.gfGrayText,
                fontSize: 14,
              ),
              textAlign: TextAlign.center,
            ),
          ],
        ),
      );
    }

    return _buildDrawerContent();
  }

  Widget _buildDrawerContent() {
    return GestureDetector(
      onTap: _closeDrawer,
      child: Container(
        color: Colors.transparent,
        child: Stack(
          children: [
            // Main floating cards area
            Center(
              child: SizedBox(
                height: MediaQuery.of(context).size.height * 0.7,
                child: PageView.builder(
                  controller: _pageController,
                  itemCount: _allReflexes.length,
                  onPageChanged: (index) {
                    setState(() {
                      _currentIndex = index;
                    });
                  },
                  itemBuilder: (context, index) {
                    final reflex = _allReflexes[index];
                    return ReflexFloatingCard(
                      reflex: reflex,
                      isCenter: index == _currentIndex,
                      onTap: () => _navigateToFullReflex(index),
                    );
                  },
                ),
              ),
            ),
            // Close button
            Positioned(
              top: MediaQuery.of(context).padding.top + 16,
              right: 16,
              child: GestureDetector(
                onTap: _closeDrawer,
                child: Container(
                  padding: const EdgeInsets.all(8),
                  decoration: BoxDecoration(
                    color: Colors.black.withValues(alpha: 0.5),
                    shape: BoxShape.circle,
                  ),
                  child: const Icon(
                    Icons.close,
                    color: AppColors.gfOffWhite,
                    size: 24,
                  ),
                ),
              ),
            ),
            // Page indicator
            if (_allReflexes.length > 1)
              Positioned(
                bottom: 50,
                left: 0,
                right: 0,
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: List.generate(
                    _allReflexes.length,
                    (index) => Container(
                      margin: const EdgeInsets.symmetric(horizontal: 4),
                      width: 8,
                      height: 8,
                      decoration: BoxDecoration(
                        shape: BoxShape.circle,
                        color: index == _currentIndex
                            ? AppColors.gfGreen
                            : AppColors.gfGrayText.withValues(alpha: 0.5),
                      ),
                    ),
                  ),
                ),
              ),
          ],
        ),
      ),
    );
  }
}
