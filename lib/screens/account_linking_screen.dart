import 'package:flutter/material.dart';
import '../theme/app_theme.dart';

import '../services/xbox_auth_service.dart';
import '../utils/app_logger.dart';
import '../widgets/xbox_icon.dart';
import 'xbox_link_screen.dart';

class AccountLinkingScreen extends StatefulWidget {
  const AccountLinkingScreen({super.key});

  @override
  State<AccountLinkingScreen> createState() => _AccountLinkingScreenState();
}

class _AccountLinkingScreenState extends State<AccountLinkingScreen> {
  bool _isLoading = false;
  bool _isXboxLinked = false;
  String? _linkedXboxGamertag;

  @override
  void initState() {
    super.initState();
    _checkLinkedAccounts();
  }

  Future<void> _checkLinkedAccounts() async {
    setState(() {
      _isLoading = true;
    });

    try {
      // Check Xbox account status
      final isXboxLinked = await XboxAuthService.instance.isXboxAccountLinked();
      final xboxGamertag = XboxAuthService.instance.linkedGamertag;

      if (mounted) {
        setState(() {
          _isXboxLinked = isXboxLinked;
          _linkedXboxGamertag = xboxGamertag;
        });
      }
    } catch (e) {
      AppLogger.error('Error checking linked accounts: $e');
    } finally {
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
      }
    }
  }

  Future<void> _navigateToXboxLinking() async {
    final result = await Navigator.of(
      context,
    ).push(MaterialPageRoute(builder: (context) => const XboxLinkScreen()));

    // Refresh account status after returning
    if (result != null || mounted) {
      _checkLinkedAccounts();
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppColors.gfDarkBackground,
      appBar: AppBar(
        backgroundColor: AppColors.gfDarkBackground,
        elevation: 0,
        leading: IconButton(
          icon: const Icon(Icons.arrow_back, color: AppColors.gfOffWhite),
          onPressed: () => Navigator.of(context).pop(),
        ),
        title: const Text(
          'Link Accounts',
          style: TextStyle(
            color: AppColors.gfOffWhite,
            fontSize: 20,
            fontWeight: FontWeight.bold,
          ),
        ),
      ),
      body: SafeArea(
        child:
            _isLoading
                ? const Center(
                  child: CircularProgressIndicator(color: AppColors.gfGreen),
                )
                : SingleChildScrollView(
                  padding: const EdgeInsets.all(24.0),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      // Header section
                      const Text(
                        'Connect Your Gaming Accounts',
                        style: TextStyle(
                          fontSize: 28,
                          fontWeight: FontWeight.bold,
                          color: AppColors.gfOffWhite,
                        ),
                      ),
                      const SizedBox(height: 12),
                      const Text(
                        'Link your gaming accounts to easily share screenshots, clips, and achievements with the GameFlex community.',
                        style: TextStyle(
                          fontSize: 16,
                          color: AppColors.gfGrayText,
                          height: 1.4,
                        ),
                      ),
                      const SizedBox(height: 32),

                      // Gaming Platforms Section
                      const Text(
                        'Gaming Platforms',
                        style: TextStyle(
                          fontSize: 20,
                          fontWeight: FontWeight.w600,
                          color: AppColors.gfOffWhite,
                        ),
                      ),
                      const SizedBox(height: 16),

                      // Xbox Account Card
                      _buildAccountCard(
                        title: 'Xbox Live',
                        description:
                            'Access your Xbox screenshots and game clips',
                        icon: _buildXboxIcon(),
                        isLinked: _isXboxLinked,
                        linkedInfo:
                            _linkedXboxGamertag != null
                                ? 'Gamertag: $_linkedXboxGamertag'
                                : null,
                        onTap: _navigateToXboxLinking,
                      ),

                      const SizedBox(height: 16),

                      // PlayStation Account Card (Coming Soon)
                      _buildAccountCard(
                        title: 'PlayStation',
                        description:
                            'Share your PlayStation screenshots and trophies',
                        icon: _buildPlayStationIcon(),
                        isLinked: false,
                        isComingSoon: true,
                        onTap: () {
                          ScaffoldMessenger.of(context).showSnackBar(
                            const SnackBar(
                              content: Text(
                                'PlayStation integration coming soon!',
                              ),
                              backgroundColor: AppColors.gfGreen,
                            ),
                          );
                        },
                      ),

                      const SizedBox(height: 16),

                      // Epic Games Account Card (Coming Soon)
                      _buildAccountCard(
                        title: 'Epic Games',
                        description:
                            'Connect your Epic Games account and achievements',
                        icon: _buildEpicGamesIcon(),
                        isLinked: false,
                        isComingSoon: true,
                        onTap: () {
                          ScaffoldMessenger.of(context).showSnackBar(
                            const SnackBar(
                              content: Text(
                                'Epic Games integration coming soon!',
                              ),
                              backgroundColor: AppColors.gfGreen,
                            ),
                          );
                        },
                      ),

                      const SizedBox(height: 32),

                      // Social Platforms Section
                      const Text(
                        'Social Platforms',
                        style: TextStyle(
                          fontSize: 20,
                          fontWeight: FontWeight.w600,
                          color: AppColors.gfOffWhite,
                        ),
                      ),
                      const SizedBox(height: 16),

                      // Twitch Account Card (Coming Soon)
                      _buildAccountCard(
                        title: 'Twitch',
                        description: 'Connect your Twitch channel and streams',
                        icon: _buildTwitchIcon(),
                        isLinked: false,
                        isComingSoon: true,
                        onTap: () {
                          ScaffoldMessenger.of(context).showSnackBar(
                            const SnackBar(
                              content: Text('Twitch integration coming soon!'),
                              backgroundColor: AppColors.gfGreen,
                            ),
                          );
                        },
                      ),

                      const SizedBox(height: 16),

                      // YouTube Account Card (Coming Soon)
                      _buildAccountCard(
                        title: 'YouTube',
                        description: 'Share your YouTube gaming content',
                        icon: _buildYouTubeIcon(),
                        isLinked: false,
                        isComingSoon: true,
                        onTap: () {
                          ScaffoldMessenger.of(context).showSnackBar(
                            const SnackBar(
                              content: Text('YouTube integration coming soon!'),
                              backgroundColor: AppColors.gfGreen,
                            ),
                          );
                        },
                      ),

                      const SizedBox(height: 16),

                      // Discord Account Card (Coming Soon)
                      _buildAccountCard(
                        title: 'Discord',
                        description: 'Show your Discord status and activity',
                        icon: _buildDiscordIcon(),
                        isLinked: false,
                        isComingSoon: true,
                        onTap: () {
                          ScaffoldMessenger.of(context).showSnackBar(
                            const SnackBar(
                              content: Text('Discord integration coming soon!'),
                              backgroundColor: AppColors.gfGreen,
                            ),
                          );
                        },
                      ),

                      const SizedBox(height: 32),

                      // Help text
                      Container(
                        padding: const EdgeInsets.all(16),
                        decoration: BoxDecoration(
                          color: AppColors.gfCardBackground,
                          borderRadius: BorderRadius.circular(12),
                          border: Border.all(color: AppColors.gfGrayBorder),
                        ),
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Row(
                              children: [
                                const Icon(
                                  Icons.info_outline,
                                  color: AppColors.gfGreen,
                                  size: 20,
                                ),
                                const SizedBox(width: 8),
                                const Text(
                                  'Privacy & Security',
                                  style: TextStyle(
                                    fontSize: 16,
                                    fontWeight: FontWeight.w600,
                                    color: AppColors.gfOffWhite,
                                  ),
                                ),
                              ],
                            ),
                            const SizedBox(height: 8),
                            const Text(
                              'Your linked accounts are secure and only used to access your public gaming content. You can unlink any account at any time.',
                              style: TextStyle(
                                fontSize: 14,
                                color: AppColors.gfGrayText,
                                height: 1.4,
                              ),
                            ),
                          ],
                        ),
                      ),
                    ],
                  ),
                ),
      ),
    );
  }

  Widget _buildAccountCard({
    required String title,
    required String description,
    required Widget icon,
    required bool isLinked,
    String? linkedInfo,
    bool isComingSoon = false,
    required VoidCallback onTap,
  }) {
    return GestureDetector(
      onTap: isComingSoon ? onTap : onTap,
      child: Container(
        padding: const EdgeInsets.all(16),
        decoration: BoxDecoration(
          color: AppColors.gfCardBackground,
          borderRadius: BorderRadius.circular(12),
          border: Border.all(
            color: isLinked ? AppColors.gfGreen : AppColors.gfGrayBorder,
            width: isLinked ? 2 : 1,
          ),
        ),
        child: Row(
          children: [
            // Platform icon
            Container(
              width: 48,
              height: 48,
              decoration: BoxDecoration(
                color: AppColors.gfDarkBackground,
                borderRadius: BorderRadius.circular(8),
              ),
              child: icon,
            ),
            const SizedBox(width: 16),

            // Platform info
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Row(
                    children: [
                      Text(
                        title,
                        style: const TextStyle(
                          fontSize: 16,
                          fontWeight: FontWeight.w600,
                          color: AppColors.gfOffWhite,
                        ),
                      ),
                      if (isComingSoon) ...[
                        const SizedBox(width: 8),
                        Container(
                          padding: const EdgeInsets.symmetric(
                            horizontal: 6,
                            vertical: 2,
                          ),
                          decoration: BoxDecoration(
                            color: AppColors.gfGrayText.withValues(alpha: 0.2),
                            borderRadius: BorderRadius.circular(4),
                          ),
                          child: const Text(
                            'Coming Soon',
                            style: TextStyle(
                              fontSize: 10,
                              color: AppColors.gfGrayText,
                              fontWeight: FontWeight.w500,
                            ),
                          ),
                        ),
                      ],
                    ],
                  ),
                  const SizedBox(height: 4),
                  Text(
                    description,
                    style: const TextStyle(
                      fontSize: 14,
                      color: AppColors.gfGrayText,
                    ),
                  ),
                  if (isLinked && linkedInfo != null) ...[
                    const SizedBox(height: 4),
                    Text(
                      linkedInfo,
                      style: const TextStyle(
                        fontSize: 12,
                        color: AppColors.gfGreen,
                        fontWeight: FontWeight.w500,
                      ),
                    ),
                  ],
                ],
              ),
            ),

            // Status indicator
            if (isLinked)
              const Icon(Icons.check_circle, color: AppColors.gfGreen, size: 24)
            else if (isComingSoon)
              const Icon(Icons.schedule, color: AppColors.gfGrayText, size: 24)
            else
              const Icon(
                Icons.arrow_forward_ios,
                color: AppColors.gfGrayText,
                size: 16,
              ),
          ],
        ),
      ),
    );
  }

  Widget _buildXboxIcon() {
    return Container(
      width: 48,
      height: 48,
      decoration: BoxDecoration(
        color: const Color(0xFF107C10), // Xbox green
        borderRadius: BorderRadius.circular(8),
      ),
      child: const Center(child: XboxIcon(size: 28, color: Colors.white)),
    );
  }

  Widget _buildPlayStationIcon() {
    return Container(
      width: 48,
      height: 48,
      decoration: BoxDecoration(
        color: const Color(0xFF003791), // PlayStation blue
        borderRadius: BorderRadius.circular(8),
      ),
      child: const Icon(Icons.sports_esports, color: Colors.white, size: 24),
    );
  }

  Widget _buildEpicGamesIcon() {
    return Container(
      width: 48,
      height: 48,
      decoration: BoxDecoration(
        color: const Color(0xFF313131), // Epic Games dark gray
        borderRadius: BorderRadius.circular(8),
      ),
      child: const Icon(Icons.games, color: Colors.white, size: 24),
    );
  }

  Widget _buildTwitchIcon() {
    return Container(
      width: 48,
      height: 48,
      decoration: BoxDecoration(
        color: const Color(0xFF9146FF), // Twitch purple
        borderRadius: BorderRadius.circular(8),
      ),
      child: const Icon(Icons.live_tv, color: Colors.white, size: 24),
    );
  }

  Widget _buildYouTubeIcon() {
    return Container(
      width: 48,
      height: 48,
      decoration: BoxDecoration(
        color: const Color(0xFFFF0000), // YouTube red
        borderRadius: BorderRadius.circular(8),
      ),
      child: const Icon(Icons.play_arrow, color: Colors.white, size: 24),
    );
  }

  Widget _buildDiscordIcon() {
    return Container(
      width: 48,
      height: 48,
      decoration: BoxDecoration(
        color: const Color(0xFF5865F2), // Discord blurple
        borderRadius: BorderRadius.circular(8),
      ),
      child: const Icon(
        Icons.chat_bubble_outline,
        color: Colors.white,
        size: 24,
      ),
    );
  }
}
