import 'dart:io';
import 'dart:developer' as developer;
import 'package:flutter/material.dart';
import 'package:path/path.dart' as path;
import '../theme/app_theme.dart';
import '../components/index.dart';
import '../models/channel_model.dart';
import '../services/aws_posts_service.dart';
import '../services/aws_media_service.dart';
import '../services/enhanced_media_service.dart';
import '../components/upload_progress_dialog.dart';
import '../screens/home_screen.dart';
import '../utils/app_logger.dart';
import '../utils/video_utils.dart';

class PostCompositionScreen extends StatefulWidget {
  final File? croppedImageFile;
  final File? videoFile;

  const PostCompositionScreen({
    super.key,
    this.croppedImageFile,
    this.videoFile,
  });

  @override
  State<PostCompositionScreen> createState() => _PostCompositionScreenState();
}

class _PostCompositionScreenState extends State<PostCompositionScreen> {
  final TextEditingController _textController = TextEditingController();
  final FocusNode _textFocusNode = FocusNode();
  bool _isPosting = false;
  final int _maxCharacters = 500;

  // Multi-step post creation state
  String _statusMessage = '';

  // Channel selection state
  ChannelModel? _selectedChannel;

  @override
  void initState() {
    super.initState();
    AppLogger.debug('PostComposition: Screen initialized');
    AppLogger.debug(
      'PostComposition: Has cropped image: ${widget.croppedImageFile != null}',
    );
    AppLogger.debug('PostComposition: Has video: ${widget.videoFile != null}');
    if (widget.croppedImageFile != null) {
      AppLogger.debug(
        'PostComposition: Cropped image path: ${widget.croppedImageFile!.path}',
      );
      AppLogger.debug(
        'PostComposition: Cropped image exists: ${widget.croppedImageFile!.existsSync()}',
      );
    }
    if (widget.videoFile != null) {
      AppLogger.debug('PostComposition: Video path: ${widget.videoFile!.path}');
      AppLogger.debug(
        'PostComposition: Video exists: ${widget.videoFile!.existsSync()}',
      );
    }
  }

  @override
  void dispose() {
    _textController.dispose();
    _textFocusNode.dispose();
    super.dispose();
  }

  Future<void> _submitPost() async {
    if (_isPosting) return;

    AppLogger.debug('PostComposition: Starting post submission');
    AppLogger.debug(
      'PostComposition: Content length: ${_textController.text.trim().length}',
    );
    AppLogger.debug(
      'PostComposition: Selected channel: ${_selectedChannel?.id}',
    );
    AppLogger.debug(
      'PostComposition: Selected channel name: ${_selectedChannel?.name}',
    );
    AppLogger.debug(
      'PostComposition: Has image: ${widget.croppedImageFile != null}',
    );
    AppLogger.debug('PostComposition: Has video: ${widget.videoFile != null}');

    setState(() {
      _isPosting = true;
      _statusMessage = 'Creating draft post...';
    });

    try {
      // Step 1: Create draft post
      AppLogger.debug('PostComposition: Creating draft post...');
      final draftPost = await AwsPostsService.instance.createDraftPost(
        content: _textController.text.trim(),
        channelId: _selectedChannel?.id,
      );

      if (draftPost == null) {
        AppLogger.error(
          'PostComposition: Failed to create draft post - null response',
        );
        throw Exception('Failed to create draft post');
      }

      AppLogger.debug(
        'PostComposition: Draft post created successfully with ID: ${draftPost.id}',
      );

      // Step 2: Upload media if image or video is provided
      String? mediaId;
      if (widget.croppedImageFile != null || widget.videoFile != null) {
        AppLogger.debug('PostComposition: Starting media upload process');
        final File mediaFile = widget.croppedImageFile ?? widget.videoFile!;
        final fileName = path.basename(mediaFile.path);
        final fileExtension = path.extension(fileName).toLowerCase();

        AppLogger.debug('PostComposition: Media file name: $fileName');
        AppLogger.debug('PostComposition: File extension: $fileExtension');
        AppLogger.debug(
          'PostComposition: File size: ${await mediaFile.length()} bytes',
        );

        String mimeType;
        String mediaType;

        if (widget.videoFile != null) {
          // Handle video files
          mediaType = 'video';
          mimeType = VideoUtils.getMimeType(widget.videoFile!);
          AppLogger.debug(
            'PostComposition: Processing video file with MIME type: $mimeType',
          );
        } else {
          // Handle image files
          mediaType = 'image';
          switch (fileExtension) {
            case '.jpg':
            case '.jpeg':
              mimeType = 'image/jpeg';
              break;
            case '.png':
              mimeType = 'image/png';
              break;
            case '.gif':
              mimeType = 'image/gif';
              break;
            default:
              mimeType = 'image/jpeg';
          }
          AppLogger.debug(
            'PostComposition: Processing image file with MIME type: $mimeType',
          );
        }

        // Use enhanced media service with progress dialog
        if (!mounted) return;
        AppLogger.debug('PostComposition: Showing enhanced upload dialog');
        final uploadResult = await showEnhancedUploadDialog(
          context: context,
          file: mediaFile,
          fileName: fileName,
          fileType: mimeType,
          mediaType: mediaType,
          title: 'Uploading ${mediaType == 'video' ? 'Video' : 'Image'}',
        );

        AppLogger.debug('PostComposition: Upload dialog completed');
        AppLogger.debug(
          'PostComposition: Upload result is null: ${uploadResult == null}',
        );
        if (uploadResult != null) {
          AppLogger.debug(
            'PostComposition: Upload success: ${uploadResult.isSuccess}',
          );
          AppLogger.debug(
            'PostComposition: Upload rejected: ${uploadResult.isRejected}',
          );
          AppLogger.debug(
            'PostComposition: Upload limit exceeded: ${uploadResult.isLimitExceeded}',
          );
          AppLogger.debug('PostComposition: Media ID: ${uploadResult.mediaId}');
          AppLogger.debug(
            'PostComposition: Upload message: ${uploadResult.message}',
          );
        }

        if (uploadResult == null || !uploadResult.isSuccess) {
          AppLogger.error('PostComposition: Upload failed or was rejected');
          // Handle upload failure, rejection, or limit exceeded
          if (uploadResult?.isRejected == true) {
            AppLogger.error('PostComposition: Content was rejected');
            _showDetailedErrorDialog(
              'Content Rejected',
              uploadResult!.userFriendlyMessage,
              isRejection: true,
            );
          } else if (uploadResult?.isLimitExceeded == true) {
            AppLogger.error('PostComposition: Upload limit exceeded');
            _showDetailedErrorDialog(
              'Upload Limit Exceeded',
              uploadResult!.userFriendlyMessage,
              isLimitExceeded: true,
            );
          } else {
            AppLogger.error(
              'PostComposition: Upload failed with unknown error',
            );
            _showDetailedErrorDialog(
              'Upload Failed',
              uploadResult?.userFriendlyMessage ??
                  'Failed to upload media. Please try again.',
            );
          }
          return; // Exit early on upload failure
        }

        mediaId = uploadResult.mediaId!;
        AppLogger.debug(
          'PostComposition: Media uploaded successfully with ID: $mediaId',
        );

        setState(() {
          _statusMessage = 'Attaching media to post...';
        });

        // Step 3: Attach media to post
        AppLogger.debug('PostComposition: Attaching media to post...');
        final updatedPost = await AwsPostsService.instance.attachMediaToPost(
          postId: draftPost.id,
          mediaId: mediaId,
        );

        if (updatedPost == null) {
          AppLogger.error(
            'PostComposition: Failed to attach media to post - null response',
          );
          throw Exception('Failed to attach media to post');
        }

        AppLogger.debug('PostComposition: Media attached successfully to post');
      } else {
        AppLogger.debug(
          'PostComposition: No media to upload, proceeding to publish',
        );
      }

      setState(() {
        _statusMessage = 'Publishing post...';
      });

      // Step 4: Publish the post
      AppLogger.debug('PostComposition: Publishing post...');
      final publishedPost = await AwsPostsService.instance.publishPost(
        draftPost.id,
      );

      if (publishedPost == null) {
        AppLogger.error(
          'PostComposition: Failed to publish post - null response',
        );
        throw Exception('Failed to publish post');
      }

      AppLogger.debug(
        'PostComposition: Post published successfully with ID: ${publishedPost.id}',
      );

      if (mounted) {
        AppLogger.debug(
          'PostComposition: Showing success message and navigating to home',
        );
        // Show success message
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('Post created successfully!'),
            backgroundColor: AppColors.gfGreen,
          ),
        );

        // Navigate directly to home screen with posts tab selected
        Navigator.of(context).pushAndRemoveUntil(
          MaterialPageRoute(builder: (context) => const HomeScreen()),
          (route) => false, // Remove all previous routes
        );
      }
    } catch (e) {
      AppLogger.error('PostComposition: Error creating post', error: e);
      AppLogger.error('PostComposition: Error type: ${e.runtimeType}');
      AppLogger.error('PostComposition: Error message: ${e.toString()}');
      _showErrorSnackBar(
        'An error occurred while creating your post: ${e.toString()}',
      );
    } finally {
      AppLogger.debug('PostComposition: Cleaning up submission state');
      if (mounted) {
        setState(() {
          _isPosting = false;
          _statusMessage = '';
        });
      }
    }
  }

  void _showErrorSnackBar(String message) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(message),
        backgroundColor: Colors.red,
        duration: const Duration(seconds: 3),
      ),
    );
  }

  void _showDetailedErrorDialog(
    String title,
    String message, {
    bool isRejection = false,
    bool isLimitExceeded = false,
  }) {
    showDialog(
      context: context,
      builder:
          (context) => AlertDialog(
            backgroundColor: AppColors.gfDarkBackground,
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(16),
            ),
            title: Row(
              children: [
                Icon(
                  isRejection
                      ? Icons.block
                      : (isLimitExceeded ? Icons.warning : Icons.error),
                  color:
                      isRejection
                          ? Colors.red
                          : (isLimitExceeded ? Colors.orange : Colors.red),
                  size: 24,
                ),
                const SizedBox(width: 12),
                Expanded(
                  child: Text(
                    title,
                    style: TextStyle(
                      color:
                          isRejection
                              ? Colors.red
                              : (isLimitExceeded ? Colors.orange : Colors.red),
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                ),
              ],
            ),
            content: Container(
              constraints: const BoxConstraints(maxWidth: 300),
              child: Text(
                message,
                style: const TextStyle(
                  color: AppColors.gfGrayText,
                  height: 1.4,
                ),
              ),
            ),
            actions: [
              TextButton(
                onPressed: () => Navigator.of(context).pop(),
                child: const Text(
                  'OK',
                  style: TextStyle(
                    color: AppColors.gfTeal,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ),
            ],
          ),
    );
  }

  @override
  Widget build(BuildContext context) {
    final remainingCharacters = _maxCharacters - _textController.text.length;

    return Scaffold(
      backgroundColor: AppColors.darkBlue,
      resizeToAvoidBottomInset: true,
      appBar: AppBar(
        title: const Text(
          'Create Post',
          style: TextStyle(
            color: AppColors.gfGreen,
            fontWeight: FontWeight.bold,
          ),
        ),
        backgroundColor: AppColors.darkBlue,
        iconTheme: const IconThemeData(color: AppColors.gfOffWhite),
        elevation: 0,
        actions: [
          if (!_isPosting)
            ValueListenableBuilder<TextEditingValue>(
              valueListenable: _textController,
              builder: (context, value, child) {
                final hasText = value.text.trim().isNotEmpty;
                return TextButton(
                  onPressed: hasText ? _submitPost : null,
                  child: Text(
                    'Post',
                    style: TextStyle(
                      color: hasText ? AppColors.gfGreen : AppColors.gfGrayText,
                      fontWeight: FontWeight.bold,
                      fontSize: 16,
                    ),
                  ),
                );
              },
            ),
        ],
      ),
      body: GestureDetector(
        onTap: () {
          // Dismiss keyboard when tapping outside
          FocusScope.of(context).unfocus();
        },
        child: SafeArea(
          child: Padding(
            padding: const EdgeInsets.all(16.0),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                // Text input label - now at the top
                Text(
                  widget.croppedImageFile != null
                      ? 'Add a caption'
                      : 'What\'s on your mind?',
                  style: TextStyle(
                    fontSize: 18,
                    fontWeight: FontWeight.bold,
                    color: AppColors.gfOffWhite,
                  ),
                ),

                const SizedBox(height: 12),

                // Text input field - now at the top with minimum height
                Container(
                  constraints: const BoxConstraints(
                    minHeight:
                        120, // Ensure minimum height so text is always visible
                  ),
                  padding: const EdgeInsets.all(16),
                  decoration: BoxDecoration(
                    color: AppColors.gfCardBackground,
                    borderRadius: BorderRadius.circular(12),
                    border: Border.all(color: AppColors.gfGrayBorder),
                  ),
                  child: TextField(
                    controller: _textController,
                    focusNode: _textFocusNode,
                    maxLines: null,
                    maxLength: _maxCharacters,
                    style: const TextStyle(
                      color: AppColors.gfOffWhite,
                      fontSize: 16,
                    ),
                    decoration: InputDecoration(
                      hintText:
                          widget.croppedImageFile != null
                              ? 'Add a caption...'
                              : 'What\'s on your mind?',
                      hintStyle: const TextStyle(
                        color: AppColors.gfGrayText,
                        fontSize: 16,
                      ),
                      border: InputBorder.none,
                      counterText: '', // Hide the built-in counter
                      suffixIcon: ValueListenableBuilder<TextEditingValue>(
                        valueListenable: _textController,
                        builder: (context, value, child) {
                          return value.text.trim().isNotEmpty
                              ? IconButton(
                                icon: const Icon(
                                  Icons.clear,
                                  color: AppColors.gfGrayText,
                                  size: 18,
                                ),
                                onPressed: () {
                                  _textController.clear();
                                  _textFocusNode.unfocus();
                                },
                              )
                              : const SizedBox.shrink();
                        },
                      ),
                    ),
                    // Remove onChanged to prevent unnecessary rebuilds
                    // The AppBar will be rebuilt when needed via other mechanisms
                  ),
                ),

                const SizedBox(height: 16),

                // Channel selection widget - keeping original for now
                // TODO: Replace with GFChannelSelectionWidget when channels are loaded
                Container(
                  padding: const EdgeInsets.all(16),
                  decoration: BoxDecoration(
                    color: AppColors.gfDarkBackground,
                    borderRadius: BorderRadius.circular(12),
                    border: Border.all(color: AppColors.gfGrayBorder),
                  ),
                  child: Row(
                    children: [
                      const Icon(Icons.tag, color: AppColors.gfGrayText),
                      const SizedBox(width: 12),
                      Expanded(
                        child: Text(
                          _selectedChannel?.name ?? 'No Channel Selected',
                          style: const TextStyle(
                            color: AppColors.gfOffWhite,
                            fontSize: 16,
                          ),
                        ),
                      ),
                      const Icon(
                        Icons.keyboard_arrow_down,
                        color: AppColors.gfGrayText,
                      ),
                    ],
                  ),
                ),

                const SizedBox(height: 16),

                // Media preview (image or video) - now below the text input
                if (widget.croppedImageFile != null || widget.videoFile != null)
                  Expanded(
                    child: Container(
                      width: double.infinity,
                      decoration: BoxDecoration(
                        borderRadius: BorderRadius.circular(12),
                        border: Border.all(color: AppColors.gfGrayBorder),
                      ),
                      child: ClipRRect(
                        borderRadius: BorderRadius.circular(12),
                        child:
                            widget.videoFile != null
                                ? Builder(
                                  builder: (context) {
                                    AppLogger.debug(
                                      'PostComposition: Displaying video: ${widget.videoFile!.path}',
                                    );
                                    return GFVideoPlayer(
                                      videoUrl: widget.videoFile!.path,
                                      autoPlay: false,
                                      showControls: true,
                                    );
                                  },
                                )
                                : Builder(
                                  builder: (context) {
                                    AppLogger.debug(
                                      'PostComposition: Displaying image: ${widget.croppedImageFile!.path}',
                                    );
                                    return GFImage(
                                      imageUrl: widget.croppedImageFile!.path,
                                      fit: BoxFit.contain,
                                    );
                                  },
                                ),
                      ),
                    ),
                  ),

                if (widget.croppedImageFile != null || widget.videoFile != null)
                  const SizedBox(height: 16),

                const SizedBox(height: 12),

                // Character counter
                Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    Text(
                      '$remainingCharacters characters remaining',
                      style: TextStyle(
                        color:
                            remainingCharacters < 50
                                ? Colors.orange
                                : AppColors.gfGrayText,
                        fontSize: 14,
                      ),
                    ),
                    if (_isPosting)
                      const SizedBox(
                        width: 20,
                        height: 20,
                        child: CircularProgressIndicator(
                          color: AppColors.gfGreen,
                          strokeWidth: 2,
                        ),
                      ),
                  ],
                ),

                const SizedBox(height: 24),

                // Submit button
                SizedBox(
                  width: double.infinity,
                  height: 50,
                  child: ElevatedButton(
                    onPressed:
                        _isPosting || _textController.text.trim().isEmpty
                            ? null
                            : _submitPost,
                    style: ElevatedButton.styleFrom(
                      backgroundColor: AppColors.gfGreen,
                      foregroundColor: AppColors.darkBlue,
                      disabledBackgroundColor: AppColors.gfGrayBorder,
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(12),
                      ),
                    ),
                    child:
                        _isPosting
                            ? Column(
                              mainAxisAlignment: MainAxisAlignment.center,
                              children: [
                                const SizedBox(
                                  width: 20,
                                  height: 20,
                                  child: CircularProgressIndicator(
                                    color: AppColors.darkBlue,
                                    strokeWidth: 2,
                                  ),
                                ),
                                if (_statusMessage.isNotEmpty) ...[
                                  const SizedBox(height: 4),
                                  Text(
                                    _statusMessage,
                                    style: const TextStyle(
                                      fontSize: 12,
                                      color: AppColors.darkBlue,
                                    ),
                                  ),
                                ],
                              ],
                            )
                            : const Text(
                              'Create Post',
                              style: TextStyle(
                                fontSize: 16,
                                fontWeight: FontWeight.bold,
                              ),
                            ),
                  ),
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }
}
