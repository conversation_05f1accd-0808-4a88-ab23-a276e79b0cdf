import 'package:flutter/material.dart';
import 'package:provider/provider.dart';

import '../models/reflex_model.dart';
import '../models/post_model.dart';
import '../providers/reflex_provider.dart';
import '../theme/app_theme.dart';
import '../utils/app_logger.dart';
import '../widgets/reflex_item.dart';

class ReflexesFeedScreen extends StatefulWidget {
  final PostModel post;
  final int initialIndex;

  const ReflexesFeedScreen({
    super.key,
    required this.post,
    this.initialIndex = 0,
  });

  @override
  State<ReflexesFeedScreen> createState() => _ReflexesFeedScreenState();
}

class _ReflexesFeedScreenState extends State<ReflexesFeedScreen> {
  final PageController _pageController = PageController();
  int _currentIndex = 0;
  List<ReflexModel> _allReflexes = [];
  bool _isLoading = false;
  String? _error;

  @override
  void initState() {
    super.initState();
    _currentIndex = widget.initialIndex;
    _loadReflexesFromPosts();
  }

  @override
  void dispose() {
    _pageController.dispose();
    super.dispose();
  }

  Future<void> _loadReflexesFromPosts() async {
    if (_isLoading) return;

    setState(() {
      _isLoading = true;
      _error = null;
    });

    try {
      AppLogger.debug(
        'ReflexesFeedScreen: Loading reflexes for post ${widget.post.id}',
      );

      final reflexProvider = Provider.of<ReflexProvider>(
        context,
        listen: false,
      );

      // Load reflexes for the specific post only
      await reflexProvider.loadReflexesForPost(widget.post.id);
      final postReflexes = reflexProvider.getReflexesForPost(widget.post.id);

      // Sort by creation time (most recent first)
      postReflexes.sort((a, b) => b.createdAt.compareTo(a.createdAt));

      setState(() {
        _allReflexes = postReflexes;
        _isLoading = false;
      });

      // Set initial page after loading
      if (_allReflexes.isNotEmpty && _currentIndex < _allReflexes.length) {
        WidgetsBinding.instance.addPostFrameCallback((_) {
          if (_pageController.hasClients) {
            _pageController.animateToPage(
              _currentIndex,
              duration: const Duration(milliseconds: 300),
              curve: Curves.easeInOut,
            );
          }
        });
      }

      AppLogger.debug(
        'ReflexesFeedScreen: Loaded ${postReflexes.length} reflexes for post ${widget.post.id}',
      );
    } catch (e, stackTrace) {
      AppLogger.error(
        'ReflexesFeedScreen: Error loading reflexes',
        error: e,
        stackTrace: stackTrace,
      );
      setState(() {
        _error = e.toString();
        _isLoading = false;
      });
    }
  }

  Future<void> _refreshReflexes() async {
    setState(() {
      _allReflexes.clear();
    });
    await _loadReflexesFromPosts();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.black,
      appBar: AppBar(
        title: Text(
          'Reflexes for ${widget.post.authorDisplayName}\'s Post',
          style: const TextStyle(
            color: AppColors.gfGreen,
            fontWeight: FontWeight.bold,
          ),
        ),
        backgroundColor: Colors.black,
        iconTheme: const IconThemeData(color: AppColors.gfOffWhite),
        actions: [
          IconButton(
            icon: const Icon(Icons.refresh, color: AppColors.gfOffWhite),
            onPressed: _refreshReflexes,
          ),
        ],
      ),
      body: _buildBody(),
    );
  }

  Widget _buildBody() {
    if (_isLoading && _allReflexes.isEmpty) {
      return const Center(
        child: CircularProgressIndicator(
          valueColor: AlwaysStoppedAnimation<Color>(AppColors.gfGreen),
        ),
      );
    }

    if (_error != null && _allReflexes.isEmpty) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            const Icon(Icons.error_outline, color: Colors.red, size: 64),
            const SizedBox(height: 16),
            const Text(
              'Error loading reflexes',
              style: TextStyle(
                color: Colors.white,
                fontSize: 18,
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 8),
            Text(
              _error ?? 'Unknown error',
              style: const TextStyle(color: AppColors.gfGrayText, fontSize: 14),
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 24),
            ElevatedButton(
              onPressed: _refreshReflexes,
              style: ElevatedButton.styleFrom(
                backgroundColor: AppColors.gfGreen,
                foregroundColor: Colors.black,
              ),
              child: const Text('Retry'),
            ),
          ],
        ),
      );
    }

    if (_allReflexes.isEmpty) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            const Icon(
              Icons.sports_martial_arts,
              color: AppColors.gfGrayText,
              size: 64,
            ),
            const SizedBox(height: 16),
            const Text(
              'No reflexes yet',
              style: TextStyle(
                color: Colors.white,
                fontSize: 18,
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 8),
            const Text(
              'No reflexes for this post yet',
              style: TextStyle(color: AppColors.gfGrayText, fontSize: 14),
              textAlign: TextAlign.center,
            ),
          ],
        ),
      );
    }

    return RefreshIndicator(
      onRefresh: _refreshReflexes,
      color: AppColors.gfGreen,
      backgroundColor: AppColors.gfDarkBackground,
      child: Stack(
        children: [
          PageView.builder(
            controller: _pageController,
            scrollDirection: Axis.horizontal,
            itemCount: _allReflexes.length,
            onPageChanged: (index) {
              AppLogger.debug(
                'ReflexesFeedScreen: Page changed to index $index',
              );
              setState(() {
                _currentIndex = index;
              });
            },
            itemBuilder: (context, index) {
              if (index >= _allReflexes.length) {
                return const Center(
                  child: CircularProgressIndicator(
                    valueColor: AlwaysStoppedAnimation<Color>(
                      AppColors.gfGreen,
                    ),
                  ),
                );
              }

              final reflex = _allReflexes[index];
              return ReflexItem(
                reflex: reflex,
                isVisible: index == _currentIndex,
              );
            },
          ),
          // Invisible gesture detector on the left edge for swipe right when at first reflex
          if (_currentIndex == 0)
            Positioned(
              left: 0,
              top: 0,
              bottom: 0,
              width: 50, // 50px wide invisible area on the left edge
              child: GestureDetector(
                onPanEnd: (details) {
                  final velocityX = details.velocity.pixelsPerSecond.dx;
                  final velocityY = details.velocity.pixelsPerSecond.dy;

                  // Check for swipe right
                  if (velocityX > 200 && velocityY.abs() < 400) {
                    AppLogger.debug(
                      'ReflexesFeedScreen: Swipe right detected at first reflex, navigating back',
                    );
                    Navigator.of(context).pop();
                  }
                },
                child: Container(color: Colors.transparent),
              ),
            ),
        ],
      ),
    );
  }
}
