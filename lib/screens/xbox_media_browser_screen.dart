import 'package:flutter/material.dart';
import 'dart:io';
import 'dart:typed_data';
import '../theme/app_theme.dart';
import '../utils/app_logger.dart';
import '../services/xbox_auth_service.dart';
import '../services/xbox_media_service.dart';
import '../models/xbox_media_model.dart';
import '../widgets/common/gf_button.dart';
import 'xbox_link_screen.dart';
import 'post_composition_screen.dart';
import 'pro_image_editor_screen.dart';
import 'video_editor_screen.dart';

import 'package:path_provider/path_provider.dart';
import 'package:path/path.dart' as path;

class XboxMediaBrowserScreen extends StatefulWidget {
  const XboxMediaBrowserScreen({super.key});

  @override
  State<XboxMediaBrowserScreen> createState() => _XboxMediaBrowserScreenState();
}

class _XboxMediaBrowserScreenState extends State<XboxMediaBrowserScreen>
    with TickerProviderStateMixin {
  late TabController _tabController;
  bool _isLoading = true;
  bool _isXboxLinked = false;
  String? _error;

  List<XboxMediaItem> _screenshots = [];
  List<XboxMediaItem> _gameClips = [];
  final List<XboxMediaItem> _selectedItems = [];
  bool _isDownloading = false;
  double _downloadProgress = 0.0;

  // Track download status for each media item
  final Map<String, bool> _downloadStatus = {};

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 2, vsync: this);
    AppLogger.info('Xbox media browser screen - initializing');
    _checkXboxAccountAndLoadMedia();
  }

  @override
  void dispose() {
    _tabController.dispose();
    super.dispose();
  }

  Future<void> _checkXboxAccountAndLoadMedia() async {
    try {
      setState(() {
        _isLoading = true;
        _error = null;
      });

      // Check if Xbox account is linked
      final isLinked = await XboxAuthService.instance.isXboxAccountLinked();

      if (!isLinked) {
        setState(() {
          _isXboxLinked = false;
          _isLoading = false;
        });
        return;
      }

      setState(() {
        _isXboxLinked = true;
      });

      // Load Xbox media
      await _loadXboxMedia();
    } catch (e) {
      setState(() {
        _error = 'Failed to load Xbox media: $e';
        _isLoading = false;
      });
      AppLogger.error('Error loading Xbox media: $e');
    }
  }

  Future<void> _loadXboxMedia() async {
    try {
      // Load screenshots
      final screenshotsResponse = await XboxMediaService.instance
          .getScreenshots(numItems: 50);

      // Load game clips
      final gameClipsResponse = await XboxMediaService.instance.getGameClips(
        numItems: 25,
      );

      if (mounted) {
        final screenshots = screenshotsResponse?.items ?? [];
        final gameClips = gameClipsResponse?.items ?? [];

        // Check download status for all items
        final allItems = [...screenshots, ...gameClips];
        for (final item in allItems) {
          final isDownloaded = await XboxMediaService.instance
              .isMediaDownloaded(item);
          _downloadStatus[item.id] = isDownloaded;
        }

        setState(() {
          _screenshots = screenshots;
          _gameClips = gameClips;
          _isLoading = false;
        });
      }
    } catch (e) {
      if (mounted) {
        setState(() {
          _error = 'Failed to load Xbox media: $e';
          _isLoading = false;
        });
      }
      AppLogger.error('Error loading Xbox media: $e');
    }
  }

  Future<void> _downloadSelectedItems() async {
    if (_selectedItems.isEmpty) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('Please select items to download'),
          backgroundColor: Colors.orange,
        ),
      );
      return;
    }

    setState(() {
      _isDownloading = true;
      _downloadProgress = 0.0;
    });

    try {
      final downloadedFiles = await XboxMediaService.instance
          .downloadMultipleItems(
            _selectedItems,
            onProgress: (completed, total) {
              if (mounted) {
                setState(() {
                  _downloadProgress = completed / total;
                });
              }
            },
          );

      if (mounted) {
        setState(() {
          _isDownloading = false;
          _selectedItems.clear();
        });

        // Return the downloaded files to the previous screen
        Navigator.of(context).pop(downloadedFiles);
      }
    } catch (e) {
      if (mounted) {
        setState(() {
          _isDownloading = false;
        });

        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Download failed: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
      AppLogger.error('Error downloading Xbox media: $e');
    }
  }

  void _toggleItemSelection(XboxMediaItem item) {
    setState(() {
      if (_selectedItems.contains(item)) {
        _selectedItems.remove(item);
      } else {
        _selectedItems.add(item);
      }
    });
  }

  /// Handle individual media item tap - download or continue to editor
  Future<void> _onMediaItemTap(XboxMediaItem item) async {
    final isDownloaded = _downloadStatus[item.id] ?? false;

    try {
      File? mediaFile;

      if (isDownloaded) {
        // Get the existing downloaded file
        mediaFile = await XboxMediaService.instance.getDownloadedMediaFile(
          item,
        );
        if (mediaFile == null) {
          // File was deleted, update status and download again
          setState(() {
            _downloadStatus[item.id] = false;
          });
          return _onMediaItemTap(item); // Retry
        }
      } else {
        // Download the media item
        setState(() {
          _isDownloading = true;
        });

        mediaFile = await XboxMediaService.instance.downloadMediaItem(item);

        if (mediaFile != null) {
          setState(() {
            _downloadStatus[item.id] = true;
            _isDownloading = false;
          });
        } else {
          setState(() {
            _isDownloading = false;
          });

          if (mounted) {
            ScaffoldMessenger.of(context).showSnackBar(
              SnackBar(
                content: Text('Failed to download ${item.type.name}'),
                backgroundColor: Colors.red,
              ),
            );
          }
          return;
        }
      }

      // Navigate to appropriate editor
      if (mounted) {
        if (item.type == XboxMediaType.screenshot) {
          // Navigate to image editor first
          Navigator.of(context).push(
            MaterialPageRoute(
              builder:
                  (context) => ProImageEditorScreen(
                    imageFile: mediaFile!,
                    onImageEditingComplete: (bytes) async {
                      // Save edited image and navigate to post composition
                      await _handleEditedImage(bytes);
                    },
                  ),
            ),
          );
        } else {
          // Navigate to video editor first
          Navigator.of(context).push(
            MaterialPageRoute(
              builder:
                  (context) => VideoEditorScreen(
                    videoFile: mediaFile!,
                    onVideoEditingComplete: (editedVideoFile) async {
                      // Navigate to post composition with edited video
                      await _handleEditedVideo(editedVideoFile);
                    },
                  ),
            ),
          );
        }
      }
    } catch (e) {
      setState(() {
        _isDownloading = false;
      });

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('Error: $e'), backgroundColor: Colors.red),
        );
      }
      AppLogger.error('Error handling Xbox media item tap: $e');
    }
  }

  /// Save edited image bytes to a temporary file and navigate to post composition
  Future<void> _handleEditedImage(Uint8List bytes) async {
    try {
      // Get temporary directory
      final tempDir = await getTemporaryDirectory();
      final timestamp = DateTime.now().millisecondsSinceEpoch;
      final fileName = 'xbox_edited_image_$timestamp.jpg';
      final filePath = path.join(tempDir.path, fileName);

      // Save bytes to file
      final file = File(filePath);
      await file.writeAsBytes(bytes);

      if (mounted) {
        // Navigate to post composition screen
        Navigator.of(context).pushReplacement(
          MaterialPageRoute(
            builder: (context) => PostCompositionScreen(croppedImageFile: file),
          ),
        );
      }
    } catch (e) {
      AppLogger.error('Error handling edited Xbox image: $e');
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Failed to process edited image: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  /// Handle edited video file and navigate to post composition
  Future<void> _handleEditedVideo(File videoFile) async {
    try {
      if (mounted) {
        // Navigate to post composition screen
        Navigator.of(context).pushReplacement(
          MaterialPageRoute(
            builder: (context) => PostCompositionScreen(videoFile: videoFile),
          ),
        );
      }
    } catch (e) {
      AppLogger.error('Error handling edited Xbox video: $e');
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Failed to process edited video: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppColors.gfDarkBackground,
      appBar: AppBar(
        title: const Text('Xbox Media'),
        backgroundColor: AppColors.gfDarkBackground,
        foregroundColor: AppColors.gfOffWhite,
        elevation: 0,
        bottom:
            _isXboxLinked
                ? TabBar(
                  controller: _tabController,
                  labelColor: AppColors.gfGreen,
                  unselectedLabelColor: AppColors.gfGrayText,
                  indicatorColor: AppColors.gfGreen,
                  tabs: const [
                    Tab(text: 'Screenshots'),
                    Tab(text: 'Game Clips'),
                  ],
                )
                : null,
        actions: [
          if (_selectedItems.isNotEmpty)
            TextButton(
              onPressed: _isDownloading ? null : _downloadSelectedItems,
              child: Text(
                _isDownloading
                    ? 'Downloading...'
                    : 'Download (${_selectedItems.length})',
                style: const TextStyle(color: AppColors.gfGreen),
              ),
            ),
          if (_isXboxLinked && _selectedItems.isEmpty)
            IconButton(
              onPressed: _isLoading ? null : _loadXboxMedia,
              icon: const Icon(Icons.refresh, color: AppColors.gfGreen),
              tooltip: 'Refresh',
            ),
        ],
      ),
      body: _buildBody(),
    );
  }

  Widget _buildBody() {
    if (_isLoading) {
      return const Center(
        child: CircularProgressIndicator(color: AppColors.gfGreen),
      );
    }

    if (!_isXboxLinked) {
      return _buildNotLinkedView();
    }

    if (_error != null) {
      return _buildErrorView();
    }

    return Column(
      children: [
        if (_isDownloading) _buildDownloadProgress(),
        Expanded(
          child: TabBarView(
            controller: _tabController,
            children: [
              _buildMediaGrid(_screenshots),
              _buildMediaGrid(_gameClips),
            ],
          ),
        ),
      ],
    );
  }

  Widget _buildNotLinkedView() {
    return Center(
      child: Padding(
        padding: const EdgeInsets.all(24.0),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            const Icon(
              Icons.videogame_asset,
              size: 80,
              color: AppColors.gfGrayText,
            ),
            const SizedBox(height: 24),
            const Text(
              'Xbox Account Not Linked',
              style: TextStyle(
                fontSize: 24,
                fontWeight: FontWeight.bold,
                color: AppColors.gfOffWhite,
              ),
            ),
            const SizedBox(height: 16),
            const Text(
              'Link your Xbox account to browse and download your screenshots and game clips.',
              style: TextStyle(
                fontSize: 16,
                color: AppColors.gfGrayText,
                height: 1.4,
              ),
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 32),
            SizedBox(
              width: double.infinity,
              child: GFButton(
                text: 'Link Xbox Account',
                onPressed: () async {
                  final result = await Navigator.of(context).push(
                    MaterialPageRoute(
                      builder: (context) => const XboxLinkScreen(),
                    ),
                  );

                  // Refresh the screen after returning from link screen
                  if (result != null || mounted) {
                    _checkXboxAccountAndLoadMedia();
                  }
                },
                type: GFButtonType.primary,
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildErrorView() {
    return Center(
      child: Padding(
        padding: const EdgeInsets.all(24.0),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            const Icon(Icons.error_outline, size: 80, color: Colors.red),
            const SizedBox(height: 24),
            const Text(
              'Error Loading Media',
              style: TextStyle(
                fontSize: 24,
                fontWeight: FontWeight.bold,
                color: AppColors.gfOffWhite,
              ),
            ),
            const SizedBox(height: 16),
            Text(
              _error!,
              style: const TextStyle(
                fontSize: 16,
                color: AppColors.gfGrayText,
                height: 1.4,
              ),
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 32),
            SizedBox(
              width: double.infinity,
              child: GFButton(
                text: 'Retry',
                onPressed: _checkXboxAccountAndLoadMedia,
                type: GFButtonType.primary,
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildDownloadProgress() {
    return Container(
      padding: const EdgeInsets.all(16),
      color: AppColors.gfCardBackground,
      child: Column(
        children: [
          Text(
            'Downloading ${_selectedItems.length} items...',
            style: const TextStyle(
              color: AppColors.gfOffWhite,
              fontWeight: FontWeight.w600,
            ),
          ),
          const SizedBox(height: 8),
          LinearProgressIndicator(
            value: _downloadProgress,
            backgroundColor: AppColors.gfGrayBorder,
            valueColor: const AlwaysStoppedAnimation<Color>(AppColors.gfGreen),
          ),
        ],
      ),
    );
  }

  Widget _buildMediaGrid(List<XboxMediaItem> items) {
    if (items.isEmpty) {
      return const Center(
        child: Text(
          'No media found',
          style: TextStyle(color: AppColors.gfGrayText, fontSize: 16),
        ),
      );
    }

    return GridView.builder(
      padding: const EdgeInsets.all(16),
      gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
        crossAxisCount: 2,
        crossAxisSpacing: 12,
        mainAxisSpacing: 12,
        childAspectRatio: 1.0,
      ),
      itemCount: items.length,
      itemBuilder: (context, index) {
        final item = items[index];
        final isSelected = _selectedItems.contains(item);
        final isDownloaded = _downloadStatus[item.id] ?? false;

        return GestureDetector(
          onTap: () => _onMediaItemTap(item),
          onLongPress: () => _toggleItemSelection(item),
          child: Container(
            decoration: BoxDecoration(
              borderRadius: BorderRadius.circular(12),
              border: Border.all(
                color: isSelected ? AppColors.gfGreen : AppColors.gfGrayBorder,
                width: isSelected ? 3 : 1,
              ),
            ),
            child: ClipRRect(
              borderRadius: BorderRadius.circular(12),
              child: Stack(
                children: [
                  // Thumbnail
                  Container(
                    width: double.infinity,
                    height: double.infinity,
                    color: AppColors.gfCardBackground,
                    child:
                        item.thumbnailUrl.isNotEmpty
                            ? Image.network(
                              item.thumbnailUrl,
                              fit: BoxFit.cover,
                              errorBuilder: (context, error, stackTrace) {
                                return const Icon(
                                  Icons.broken_image,
                                  color: AppColors.gfGrayText,
                                  size: 48,
                                );
                              },
                            )
                            : Icon(
                              item.type == XboxMediaType.screenshot
                                  ? Icons.photo
                                  : Icons.videocam,
                              color: AppColors.gfGrayText,
                              size: 48,
                            ),
                  ),

                  // Selection overlay
                  if (isSelected)
                    Container(
                      width: double.infinity,
                      height: double.infinity,
                      color: AppColors.gfGreen.withValues(alpha: 0.3),
                      child: const Center(
                        child: Icon(
                          Icons.check_circle,
                          color: AppColors.gfGreen,
                          size: 32,
                        ),
                      ),
                    ),

                  // Media type indicator
                  Positioned(
                    top: 8,
                    left: 8,
                    child: Container(
                      padding: const EdgeInsets.symmetric(
                        horizontal: 6,
                        vertical: 2,
                      ),
                      decoration: BoxDecoration(
                        color: Colors.black.withValues(alpha: 0.7),
                        borderRadius: BorderRadius.circular(4),
                      ),
                      child: Icon(
                        item.type == XboxMediaType.screenshot
                            ? Icons.photo_camera
                            : Icons.videocam,
                        color: Colors.white,
                        size: 12,
                      ),
                    ),
                  ),

                  // Download status indicator
                  Positioned(
                    top: 8,
                    right: 8,
                    child: Container(
                      width: 24,
                      height: 24,
                      decoration: BoxDecoration(
                        color:
                            isDownloaded
                                ? AppColors.gfGreen
                                : Colors.black.withValues(alpha: 0.7),
                        shape: BoxShape.circle,
                      ),
                      child: Icon(
                        isDownloaded ? Icons.check : Icons.download,
                        color: Colors.white,
                        size: 12,
                      ),
                    ),
                  ),

                  // Action button overlay
                  Positioned(
                    bottom: 8,
                    right: 8,
                    child: Container(
                      padding: const EdgeInsets.symmetric(
                        horizontal: 8,
                        vertical: 4,
                      ),
                      decoration: BoxDecoration(
                        color: AppColors.gfGreen,
                        borderRadius: BorderRadius.circular(12),
                      ),
                      child:
                          _isDownloading
                              ? const SizedBox(
                                width: 12,
                                height: 12,
                                child: CircularProgressIndicator(
                                  strokeWidth: 2,
                                  color: Colors.white,
                                ),
                              )
                              : Text(
                                isDownloaded ? 'Continue' : 'Download',
                                style: const TextStyle(
                                  color: Colors.white,
                                  fontSize: 10,
                                  fontWeight: FontWeight.w600,
                                ),
                              ),
                    ),
                  ),

                  // Game title
                  Positioned(
                    bottom: 0,
                    left: 0,
                    right: 0,
                    child: Container(
                      padding: const EdgeInsets.all(8),
                      decoration: BoxDecoration(
                        gradient: LinearGradient(
                          begin: Alignment.topCenter,
                          end: Alignment.bottomCenter,
                          colors: [
                            Colors.transparent,
                            Colors.black.withValues(alpha: 0.8),
                          ],
                        ),
                      ),
                      child: Text(
                        item.gameTitle,
                        style: const TextStyle(
                          color: Colors.white,
                          fontSize: 12,
                          fontWeight: FontWeight.w600,
                        ),
                        maxLines: 1,
                        overflow: TextOverflow.ellipsis,
                      ),
                    ),
                  ),
                ],
              ),
            ),
          ),
        );
      },
    );
  }
}
