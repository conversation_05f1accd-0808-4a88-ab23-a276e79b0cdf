import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../theme/app_theme.dart';
import '../components/index.dart';
import '../providers/auth_provider.dart';

class EditProfileScreen extends StatefulWidget {
  const EditProfileScreen({super.key});

  @override
  State<EditProfileScreen> createState() => _EditProfileScreenState();
}

class _EditProfileScreenState extends State<EditProfileScreen> {
  final _formKey = GlobalKey<FormState>();
  final _currentPasswordController = TextEditingController();
  final _newPasswordController = TextEditingController();
  final _confirmPasswordController = TextEditingController();

  bool _isCurrentPasswordVisible = false;
  bool _isNewPasswordVisible = false;
  bool _isConfirmPasswordVisible = false;
  bool _isLoading = false;

  @override
  void dispose() {
    _currentPasswordController.dispose();
    _newPasswordController.dispose();
    _confirmPasswordController.dispose();
    super.dispose();
  }

  void _toggleCurrentPasswordVisibility() {
    setState(() {
      _isCurrentPasswordVisible = !_isCurrentPasswordVisible;
    });
  }

  void _toggleNewPasswordVisibility() {
    setState(() {
      _isNewPasswordVisible = !_isNewPasswordVisible;
    });
  }

  void _toggleConfirmPasswordVisibility() {
    setState(() {
      _isConfirmPasswordVisible = !_isConfirmPasswordVisible;
    });
  }

  Future<void> _handleChangePassword() async {
    if (!_formKey.currentState!.validate()) {
      return;
    }

    setState(() {
      _isLoading = true;
    });

    final authProvider = Provider.of<AuthProvider>(context, listen: false);

    try {
      // First verify current password by attempting to sign in
      final email = authProvider.user?.email;
      if (email == null) {
        _showErrorSnackBar('Unable to verify current user');
        return;
      }

      // Sign in with current password to verify it's correct
      final signInSuccess = await authProvider.signIn(
        email: email,
        password: _currentPasswordController.text,
      );

      if (!signInSuccess) {
        _showErrorSnackBar('Current password is incorrect');
        return;
      }

      // If current password is correct, update to new password
      final changeSuccess = await authProvider.changePassword(
        _newPasswordController.text,
      );

      if (changeSuccess) {
        _showSuccessSnackBar('Password changed successfully');
        _clearForm();
      } else {
        _showErrorSnackBar(
          authProvider.errorMessage ?? 'Failed to change password',
        );
      }
    } catch (e) {
      _showErrorSnackBar('An error occurred: $e');
    } finally {
      setState(() {
        _isLoading = false;
      });
    }
  }

  void _clearForm() {
    _currentPasswordController.clear();
    _newPasswordController.clear();
    _confirmPasswordController.clear();
  }

  void _showErrorSnackBar(String message) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(content: Text(message), backgroundColor: Colors.red.shade600),
    );
  }

  void _showSuccessSnackBar(String message) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(content: Text(message), backgroundColor: AppColors.gfGreen),
    );
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppColors.gfDarkBackground,
      appBar: AppBar(
        title: const Text(
          'Edit Profile',
          style: TextStyle(color: AppColors.gfOffWhite),
        ),
        backgroundColor: AppColors.gfDarkBackground,
        iconTheme: const IconThemeData(color: AppColors.gfGreen),
        elevation: 0,
      ),
      body: SafeArea(
        child: SingleChildScrollView(
          padding: const EdgeInsets.all(24.0),
          child: Form(
            key: _formKey,
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                // Change Password Section
                Container(
                  width: double.infinity,
                  padding: const EdgeInsets.all(20.0),
                  decoration: BoxDecoration(
                    color: AppColors.gfDarkBlue.withValues(
                      alpha: 128,
                    ), // 0.5 opacity
                    borderRadius: BorderRadius.circular(12),
                    border: Border.all(
                      color: AppColors.gfGreen.withValues(
                        alpha: 77,
                      ), // 0.3 opacity
                      width: 1,
                    ),
                  ),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      const Text(
                        'Change Password',
                        style: TextStyle(
                          fontSize: 20,
                          fontWeight: FontWeight.bold,
                          color: AppColors.gfOffWhite,
                        ),
                      ),
                      const SizedBox(height: 8),
                      const Text(
                        'Enter your current password and choose a new one',
                        style: TextStyle(
                          fontSize: 14,
                          color: AppColors.gfGrayText,
                        ),
                      ),
                      const SizedBox(height: 24),

                      // Current Password Field
                      GFTextField(
                        label: 'Current Password',
                        hint: 'Enter your current password',
                        controller: _currentPasswordController,
                        obscureText: !_isCurrentPasswordVisible,
                        validator: (value) {
                          if (value == null || value.isEmpty) {
                            return 'Please enter your current password';
                          }
                          return null;
                        },
                        suffixIcon: IconButton(
                          icon: Icon(
                            _isCurrentPasswordVisible
                                ? Icons.visibility_off
                                : Icons.visibility,
                            color: AppColors.gfGrayText,
                          ),
                          onPressed: _toggleCurrentPasswordVisibility,
                        ),
                      ),

                      const SizedBox(height: 20),

                      // New Password Field
                      GFTextField(
                        label: 'New Password',
                        hint: 'Enter your new password',
                        controller: _newPasswordController,
                        obscureText: !_isNewPasswordVisible,
                        validator: (value) {
                          if (value == null || value.isEmpty) {
                            return 'Please enter a new password';
                          }
                          if (value.length < 6) {
                            return 'Password must be at least 6 characters';
                          }
                          if (value == _currentPasswordController.text) {
                            return 'New password must be different from current password';
                          }
                          return null;
                        },
                        suffixIcon: IconButton(
                          icon: Icon(
                            _isNewPasswordVisible
                                ? Icons.visibility_off
                                : Icons.visibility,
                            color: AppColors.gfGrayText,
                          ),
                          onPressed: _toggleNewPasswordVisibility,
                        ),
                      ),

                      const SizedBox(height: 20),

                      // Confirm New Password Field
                      GFTextField(
                        label: 'Confirm New Password',
                        hint: 'Confirm your new password',
                        controller: _confirmPasswordController,
                        obscureText: !_isConfirmPasswordVisible,
                        validator: (value) {
                          if (value == null || value.isEmpty) {
                            return 'Please confirm your new password';
                          }
                          if (value != _newPasswordController.text) {
                            return 'Passwords do not match';
                          }
                          return null;
                        },
                        suffixIcon: IconButton(
                          icon: Icon(
                            _isConfirmPasswordVisible
                                ? Icons.visibility_off
                                : Icons.visibility,
                            color: AppColors.gfGrayText,
                          ),
                          onPressed: _toggleConfirmPasswordVisibility,
                        ),
                      ),

                      const SizedBox(height: 32),

                      // Change Password Button
                      SizedBox(
                        width: double.infinity,
                        child: GFButton(
                          text:
                              _isLoading
                                  ? 'Changing Password...'
                                  : 'Change Password',
                          onPressed: _isLoading ? null : _handleChangePassword,
                          type: GFButtonType.primary,
                          isEnabled: !_isLoading,
                        ),
                      ),
                    ],
                  ),
                ),

                const SizedBox(height: 32),

                // Additional profile editing sections can be added here in the future
                // For example: Update Display Name, Update Email, etc.
              ],
            ),
          ),
        ),
      ),
    );
  }
}
