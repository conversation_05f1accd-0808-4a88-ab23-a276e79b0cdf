import 'dart:io';
import 'dart:typed_data';
import 'package:flutter/material.dart';
import 'package:video_player/video_player.dart';
import 'package:pro_video_editor/pro_video_editor.dart';
import '../theme/app_theme.dart';
import '../utils/app_logger.dart';

class VideoFrameSelectorScreen extends StatefulWidget {
  final String videoUrl;
  final Function(Uint8List) onFrameSelected;

  const VideoFrameSelectorScreen({
    super.key,
    required this.videoUrl,
    required this.onFrameSelected,
  });

  @override
  State<VideoFrameSelectorScreen> createState() =>
      _VideoFrameSelectorScreenState();
}

class _VideoFrameSelectorScreenState extends State<VideoFrameSelectorScreen> {
  late VideoPlayerController _videoController;
  bool _isInitialized = false;
  bool _isCapturing = false;
  Duration _currentPosition = Duration.zero;
  Duration _videoDuration = Duration.zero;

  @override
  void initState() {
    super.initState();
    _initializeVideo();
  }

  @override
  void dispose() {
    _videoController.dispose();
    super.dispose();
  }

  Future<void> _initializeVideo() async {
    try {
      AppLogger.media(
        'VideoFrameSelector: Initializing video from ${widget.videoUrl}',
      );

      // Check if it's a network URL or local file
      if (widget.videoUrl.startsWith('http')) {
        _videoController = VideoPlayerController.networkUrl(
          Uri.parse(widget.videoUrl),
        );
      } else {
        _videoController = VideoPlayerController.file(File(widget.videoUrl));
      }

      await _videoController.initialize();

      setState(() {
        _isInitialized = true;
        _videoDuration = _videoController.value.duration;
      });

      // Listen to position changes
      _videoController.addListener(_onVideoPositionChanged);

      AppLogger.media(
        'VideoFrameSelector: Video initialized, duration: ${_videoDuration.inSeconds}s',
      );
    } catch (e, stackTrace) {
      AppLogger.error(
        'VideoFrameSelector: Error initializing video',
        error: e,
        stackTrace: stackTrace,
      );
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Failed to load video: $e'),
            backgroundColor: Colors.red,
          ),
        );
        Navigator.of(context).pop();
      }
    }
  }

  void _onVideoPositionChanged() {
    if (mounted) {
      setState(() {
        _currentPosition = _videoController.value.position;
      });
    }
  }

  Future<void> _captureCurrentFrame() async {
    if (_isCapturing || !_isInitialized) return;

    setState(() {
      _isCapturing = true;
    });

    try {
      AppLogger.media(
        'VideoFrameSelector: Capturing frame at ${_currentPosition.inSeconds}s',
      );

      // Pause the video to get a stable frame
      await _videoController.pause();

      // Create EditorVideo from the source
      final EditorVideo video;
      if (widget.videoUrl.startsWith('http')) {
        video = EditorVideo.network(widget.videoUrl);
      } else {
        video = EditorVideo.file(File(widget.videoUrl));
      }

      // Generate thumbnail at current position
      final thumbnailBytes = await ProVideoEditor.instance.getThumbnails(
        ThumbnailConfigs(
          video: video,
          outputSize: const Size(1080, 1920), // High resolution for editing
          boxFit: ThumbnailBoxFit.contain,
          timestamps: [_currentPosition],
          outputFormat: ThumbnailFormat.jpeg,
        ),
      );

      if (thumbnailBytes.isNotEmpty) {
        AppLogger.media('VideoFrameSelector: Frame captured successfully');
        widget.onFrameSelected(thumbnailBytes.first);
        if (mounted) {
          Navigator.of(context).pop();
        }
      } else {
        throw Exception('Failed to capture frame');
      }
    } catch (e, stackTrace) {
      AppLogger.error(
        'VideoFrameSelector: Error capturing frame',
        error: e,
        stackTrace: stackTrace,
      );
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Failed to capture frame: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    } finally {
      if (mounted) {
        setState(() {
          _isCapturing = false;
        });
      }
    }
  }

  void _seekToPosition(double value) {
    final position = Duration(
      milliseconds: (value * _videoDuration.inMilliseconds).round(),
    );
    _videoController.seekTo(position);
  }

  String _formatDuration(Duration duration) {
    final minutes = duration.inMinutes;
    final seconds = duration.inSeconds % 60;
    return '${minutes.toString().padLeft(2, '0')}:${seconds.toString().padLeft(2, '0')}';
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.black,
      appBar: AppBar(
        backgroundColor: AppColors.darkBlue,
        foregroundColor: AppColors.gfOffWhite,
        title: const Text('Select Frame'),
        leading: IconButton(
          icon: const Icon(Icons.close),
          onPressed: () => Navigator.of(context).pop(),
        ),
      ),
      body:
          !_isInitialized
              ? const Center(
                child: CircularProgressIndicator(
                  valueColor: AlwaysStoppedAnimation<Color>(AppColors.gfGreen),
                ),
              )
              : Column(
                children: [
                  // Video player
                  Expanded(
                    child: Center(
                      child: AspectRatio(
                        aspectRatio: _videoController.value.aspectRatio,
                        child: VideoPlayer(_videoController),
                      ),
                    ),
                  ),

                  // Controls
                  Container(
                    padding: const EdgeInsets.all(16),
                    color: AppColors.darkBlue,
                    child: Column(
                      children: [
                        // Time display
                        Row(
                          mainAxisAlignment: MainAxisAlignment.spaceBetween,
                          children: [
                            Text(
                              _formatDuration(_currentPosition),
                              style: const TextStyle(
                                color: AppColors.gfOffWhite,
                                fontSize: 16,
                              ),
                            ),
                            Text(
                              _formatDuration(_videoDuration),
                              style: const TextStyle(
                                color: AppColors.gfOffWhite,
                                fontSize: 16,
                              ),
                            ),
                          ],
                        ),

                        const SizedBox(height: 8),

                        // Seek bar
                        SliderTheme(
                          data: SliderTheme.of(context).copyWith(
                            activeTrackColor: AppColors.gfGreen,
                            inactiveTrackColor: AppColors.gfGrayBorder,
                            thumbColor: AppColors.gfGreen,
                            overlayColor: AppColors.gfGreen.withValues(
                              alpha: 0.2,
                            ),
                          ),
                          child: Slider(
                            value:
                                _videoDuration.inMilliseconds > 0
                                    ? _currentPosition.inMilliseconds /
                                        _videoDuration.inMilliseconds
                                    : 0.0,
                            onChanged: _seekToPosition,
                          ),
                        ),

                        const SizedBox(height: 16),

                        // Action buttons
                        Row(
                          mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                          children: [
                            // Play/Pause button
                            ElevatedButton.icon(
                              onPressed: () {
                                if (_videoController.value.isPlaying) {
                                  _videoController.pause();
                                } else {
                                  _videoController.play();
                                }
                              },
                              icon: Icon(
                                _videoController.value.isPlaying
                                    ? Icons.pause
                                    : Icons.play_arrow,
                              ),
                              label: Text(
                                _videoController.value.isPlaying
                                    ? 'Pause'
                                    : 'Play',
                              ),
                              style: ElevatedButton.styleFrom(
                                backgroundColor: AppColors.gfGrayBorder,
                                foregroundColor: AppColors.gfOffWhite,
                              ),
                            ),

                            // Capture frame button
                            ElevatedButton.icon(
                              onPressed:
                                  _isCapturing ? null : _captureCurrentFrame,
                              icon:
                                  _isCapturing
                                      ? const SizedBox(
                                        width: 16,
                                        height: 16,
                                        child: CircularProgressIndicator(
                                          strokeWidth: 2,
                                          valueColor:
                                              AlwaysStoppedAnimation<Color>(
                                                Colors.white,
                                              ),
                                        ),
                                      )
                                      : const Icon(Icons.camera_alt),
                              label: Text(
                                _isCapturing
                                    ? 'Capturing...'
                                    : 'Select This Frame',
                              ),
                              style: ElevatedButton.styleFrom(
                                backgroundColor: AppColors.gfGreen,
                                foregroundColor: Colors.white,
                              ),
                            ),
                          ],
                        ),

                        const SizedBox(height: 8),

                        // Instructions
                        const Text(
                          'Scrub through the video and tap "Select This Frame" to use the current frame for your reflex',
                          style: TextStyle(
                            color: AppColors.gfGrayText,
                            fontSize: 12,
                          ),
                          textAlign: TextAlign.center,
                        ),
                      ],
                    ),
                  ),
                ],
              ),
    );
  }
}
