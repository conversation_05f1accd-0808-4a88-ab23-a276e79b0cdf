import 'package:flutter/material.dart';
import '../../theme/app_theme.dart';

enum GFButtonType { primary, secondary, outline, text, danger }

class GFButton extends StatelessWidget {
  final String text;
  final VoidCallback? onPressed;
  final GFButtonType type;
  final double height;
  final double? width;
  final bool isEnabled;

  const GFButton({
    super.key,
    required this.text,
    this.onPressed,
    this.type = GFButtonType.primary,
    this.height = 48.0,
    this.width,
    this.isEnabled = true,
  });

  @override
  Widget build(BuildContext context) {
    switch (type) {
      case GFButtonType.primary:
        return _buildPrimaryButton();
      case GFButtonType.secondary:
        return _buildSecondaryButton();
      case GFButtonType.outline:
        return _buildOutlineButton();
      case GFButtonType.text:
        return _buildTextButton();
      case GFButtonType.danger:
        return _buildDangerButton();
    }
  }

  Widget _buildPrimaryButton() {
    return SizedBox(
      height: height,
      width: width,
      child: ElevatedButton(
        onPressed: isEnabled ? onPressed : null,
        style: ElevatedButton.styleFrom(
          backgroundColor: AppColors.gfGreen,
          foregroundColor: Colors.black,
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(height / 2),
          ),
          elevation: 0,
          disabledBackgroundColor: AppColors.gfGreen.withValues(
            alpha: 153,
          ), // 0.6 opacity
          disabledForegroundColor: Colors.black.withValues(
            alpha: 153,
          ), // 0.6 opacity
        ),
        child: Text(
          text,
          style: const TextStyle(fontSize: 16, fontWeight: FontWeight.bold),
        ),
      ),
    );
  }

  Widget _buildOutlineButton() {
    return SizedBox(
      height: height,
      width: width,
      child: OutlinedButton(
        onPressed: isEnabled ? onPressed : null,
        style: OutlinedButton.styleFrom(
          foregroundColor: AppColors.gfGreen,
          side: BorderSide(
            color:
                isEnabled
                    ? AppColors.gfGreen
                    : AppColors.gfGreen.withValues(alpha: 153), // 0.6 opacity
          ),
          shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(8)),
        ),
        child: Text(
          text,
          style: TextStyle(
            fontSize: 16,
            fontWeight: FontWeight.bold,
            color:
                isEnabled
                    ? AppColors.gfGreen
                    : AppColors.gfGreen.withValues(alpha: 153), // 0.6 opacity
          ),
        ),
      ),
    );
  }

  Widget _buildTextButton() {
    return TextButton(
      onPressed: isEnabled ? onPressed : null,
      style: TextButton.styleFrom(
        foregroundColor: AppColors.gfGreen,
        padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
      ),
      child: Text(
        text,
        style: TextStyle(
          fontSize: 16,
          fontWeight: FontWeight.bold,
          color:
              isEnabled
                  ? AppColors.gfGreen
                  : AppColors.gfGreen.withValues(alpha: 153), // 0.6 opacity
        ),
      ),
    );
  }

  Widget _buildSecondaryButton() {
    return SizedBox(
      height: height,
      width: width,
      child: ElevatedButton(
        onPressed: isEnabled ? onPressed : null,
        style: ElevatedButton.styleFrom(
          backgroundColor: AppColors.gfDarkBackground,
          foregroundColor: AppColors.gfOffWhite,
          side: BorderSide(
            color:
                isEnabled
                    ? AppColors.gfGreen.withValues(alpha: 128) // 0.5 opacity
                    : AppColors.gfGreen.withValues(alpha: 77), // 0.3 opacity
            width: 1,
          ),
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(height / 2),
          ),
          elevation: 0,
          disabledBackgroundColor: AppColors.gfDarkBackground.withValues(
            alpha: 153,
          ), // 0.6 opacity
          disabledForegroundColor: AppColors.gfOffWhite.withValues(
            alpha: 153,
          ), // 0.6 opacity
        ),
        child: Text(
          text,
          style: const TextStyle(fontSize: 16, fontWeight: FontWeight.bold),
        ),
      ),
    );
  }

  Widget _buildDangerButton() {
    return SizedBox(
      height: height,
      width: width,
      child: ElevatedButton(
        onPressed: isEnabled ? onPressed : null,
        style: ElevatedButton.styleFrom(
          backgroundColor: Colors.red.shade600,
          foregroundColor: Colors.white,
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(height / 2),
          ),
          elevation: 0,
          disabledBackgroundColor: Colors.red.shade600.withValues(
            alpha: 153,
          ), // 0.6 opacity
          disabledForegroundColor: Colors.white.withValues(
            alpha: 153,
          ), // 0.6 opacity
        ),
        child: Text(
          text,
          style: const TextStyle(fontSize: 16, fontWeight: FontWeight.bold),
        ),
      ),
    );
  }
}
