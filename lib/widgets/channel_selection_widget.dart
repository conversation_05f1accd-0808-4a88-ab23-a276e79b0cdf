import 'package:flutter/material.dart';
import '../models/channel_model.dart';
import '../services/aws_channels_service.dart';
import '../theme/app_theme.dart';
import '../utils/app_logger.dart';
import 'dart:developer' as developer;

class ChannelSelectionWidget extends StatefulWidget {
  final String? selectedChannelId;
  final Function(ChannelModel?) onChannelSelected;
  final bool showNoChannelOption;

  const ChannelSelectionWidget({
    super.key,
    this.selectedChannelId,
    required this.onChannelSelected,
    this.showNoChannelOption = true,
  });

  @override
  State<ChannelSelectionWidget> createState() => _ChannelSelectionWidgetState();
}

class _ChannelSelectionWidgetState extends State<ChannelSelectionWidget> {
  List<ChannelModel> _userChannels = [];
  bool _isLoading = true;
  String? _error;
  ChannelModel? _selectedChannel;

  @override
  void initState() {
    super.initState();
    _loadUserChannels();
    _initializeSelectedChannel();
  }

  void _initializeSelectedChannel() {
    if (widget.selectedChannelId != null) {
      // Find the selected channel from the loaded channels
      try {
        _selectedChannel = _userChannels.firstWhere(
          (channel) => channel.id == widget.selectedChannelId,
        );
      } catch (e) {
        // Channel not found, set to null
        _selectedChannel = null;
      }
    }
  }

  Future<void> _loadUserChannels() async {
    try {
      setState(() {
        _isLoading = true;
        _error = null;
      });

      developer.log('ChannelSelectionWidget: Loading user channels');
      final channels = await AwsChannelsService.instance.getUserChannels();

      setState(() {
        _userChannels = channels;
        _isLoading = false;
        _initializeSelectedChannel();
      });

      developer.log(
        'ChannelSelectionWidget: Loaded ${channels.length} user channels',
      );
    } catch (e, stackTrace) {
      developer.log(
        'ChannelSelectionWidget: Error loading user channels',
        error: e,
        stackTrace: stackTrace,
      );
      AppLogger.error(
        'Error loading user channels',
        error: e,
        stackTrace: stackTrace,
      );

      setState(() {
        _error = 'Failed to load channels: $e';
        _isLoading = false;
      });
    }
  }

  void _selectChannel(ChannelModel? channel) {
    setState(() {
      _selectedChannel = channel;
    });
    widget.onChannelSelected(channel);
  }

  Future<void> _showChannelSelectionModal() async {
    if (_isLoading || _userChannels.isEmpty) return;

    final result = await showModalBottomSheet<Map<String, dynamic>?>(
      context: context,
      isScrollControlled: true,
      backgroundColor: Colors.transparent,
      builder:
          (context) => _ChannelSelectionModal(
            channels: _userChannels,
            selectedChannel: _selectedChannel,
            showNoChannelOption: widget.showNoChannelOption,
          ),
    );

    // Only update if user actually made a selection (not just dismissed modal)
    if (result != null) {
      final selectedChannel = result['channel'] as ChannelModel?;
      _selectChannel(selectedChannel);
    }
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: AppColors.darkBlue,
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: AppColors.gfGrayText.withValues(alpha: 0.3)),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        mainAxisSize: MainAxisSize.min,
        children: [
          Row(
            children: [
              Icon(Icons.forum_outlined, color: AppColors.gfGreen, size: 20),
              const SizedBox(width: 8),
              Text(
                'Post to Channel',
                style: TextStyle(
                  color: AppColors.gfOffWhite,
                  fontSize: 16,
                  fontWeight: FontWeight.w600,
                ),
              ),
            ],
          ),
          const SizedBox(height: 12),

          if (_isLoading)
            Center(
              child: CircularProgressIndicator(
                valueColor: AlwaysStoppedAnimation<Color>(AppColors.gfGreen),
                strokeWidth: 2,
              ),
            )
          else if (_error != null)
            Column(
              children: [
                Text(
                  _error!,
                  style: TextStyle(color: Colors.red, fontSize: 14),
                ),
                const SizedBox(height: 8),
                TextButton(
                  onPressed: _loadUserChannels,
                  child: Text(
                    'Retry',
                    style: TextStyle(color: AppColors.gfGreen),
                  ),
                ),
              ],
            )
          else
            GestureDetector(
              onTap: _showChannelSelectionModal,
              child: Container(
                padding: const EdgeInsets.all(12),
                decoration: BoxDecoration(
                  color: AppColors.gfDarkBlue.withValues(alpha: 0.3),
                  borderRadius: BorderRadius.circular(8),
                  border: Border.all(
                    color: AppColors.gfGrayText.withValues(alpha: 0.2),
                    width: 1,
                  ),
                ),
                child: Row(
                  children: [
                    Icon(
                      _selectedChannel != null ? Icons.forum : Icons.add,
                      color: AppColors.gfGrayText,
                      size: 20,
                    ),
                    const SizedBox(width: 12),
                    Expanded(
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Text(
                            _selectedChannel?.name ?? 'Select Channel',
                            style: TextStyle(
                              color: AppColors.gfOffWhite,
                              fontSize: 14,
                              fontWeight: FontWeight.w500,
                            ),
                          ),
                          if (_selectedChannel != null)
                            Text(
                              '${_selectedChannel!.memberCount} members',
                              style: TextStyle(
                                color: AppColors.gfGrayText,
                                fontSize: 12,
                              ),
                            )
                          else
                            Text(
                              'Tap to choose a channel or post without one',
                              style: TextStyle(
                                color: AppColors.gfGrayText,
                                fontSize: 12,
                              ),
                            ),
                        ],
                      ),
                    ),
                    Icon(
                      Icons.arrow_drop_down,
                      color: AppColors.gfGrayText,
                      size: 24,
                    ),
                  ],
                ),
              ),
            ),
        ],
      ),
    );
  }
}

class _ChannelSelectionModal extends StatefulWidget {
  final List<ChannelModel> channels;
  final ChannelModel? selectedChannel;
  final bool showNoChannelOption;

  const _ChannelSelectionModal({
    required this.channels,
    this.selectedChannel,
    this.showNoChannelOption = true,
  });

  @override
  State<_ChannelSelectionModal> createState() => _ChannelSelectionModalState();
}

class _ChannelSelectionModalState extends State<_ChannelSelectionModal> {
  final TextEditingController _searchController = TextEditingController();
  List<ChannelModel> _filteredChannels = [];

  @override
  void initState() {
    super.initState();
    _filteredChannels = widget.channels;
    _searchController.addListener(_filterChannels);
  }

  @override
  void dispose() {
    _searchController.dispose();
    super.dispose();
  }

  void _filterChannels() {
    final query = _searchController.text.toLowerCase();
    setState(() {
      if (query.isEmpty) {
        _filteredChannels = widget.channels;
      } else {
        _filteredChannels =
            widget.channels
                .where((channel) => channel.name.toLowerCase().contains(query))
                .toList();
      }
    });
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      height: MediaQuery.of(context).size.height * 0.75,
      decoration: BoxDecoration(
        color: AppColors.darkBlue,
        borderRadius: const BorderRadius.vertical(top: Radius.circular(20)),
      ),
      child: Column(
        children: [
          // Handle bar
          Container(
            margin: const EdgeInsets.only(top: 8),
            width: 40,
            height: 4,
            decoration: BoxDecoration(
              color: AppColors.gfGrayText.withValues(alpha: 0.5),
              borderRadius: BorderRadius.circular(2),
            ),
          ),

          // Header
          Padding(
            padding: const EdgeInsets.all(16),
            child: Row(
              children: [
                Icon(Icons.forum_outlined, color: AppColors.gfGreen, size: 24),
                const SizedBox(width: 12),
                Text(
                  'Select Channel',
                  style: TextStyle(
                    color: AppColors.gfOffWhite,
                    fontSize: 18,
                    fontWeight: FontWeight.w600,
                  ),
                ),
                const Spacer(),
                IconButton(
                  onPressed: () => Navigator.of(context).pop(),
                  icon: Icon(Icons.close, color: AppColors.gfGrayText),
                ),
              ],
            ),
          ),

          // Search bar
          Padding(
            padding: const EdgeInsets.symmetric(horizontal: 16),
            child: TextField(
              controller: _searchController,
              style: TextStyle(color: AppColors.gfOffWhite),
              decoration: InputDecoration(
                hintText: 'Search channels...',
                hintStyle: TextStyle(color: AppColors.gfGrayText),
                prefixIcon: Icon(Icons.search, color: AppColors.gfGrayText),
                filled: true,
                fillColor: AppColors.gfDarkBlue.withValues(alpha: 0.3),
                border: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(12),
                  borderSide: BorderSide.none,
                ),
                contentPadding: const EdgeInsets.symmetric(
                  horizontal: 16,
                  vertical: 12,
                ),
              ),
            ),
          ),

          const SizedBox(height: 16),

          // Channel list
          Expanded(
            child: ListView(
              padding: const EdgeInsets.symmetric(horizontal: 16),
              children: [
                // No channel option
                if (widget.showNoChannelOption)
                  _buildModalChannelOption(
                    null,
                    'No Channel',
                    'Post to your general feed',
                    Icons.public,
                  ),

                if (widget.showNoChannelOption && _filteredChannels.isNotEmpty)
                  const SizedBox(height: 8),

                // Filtered channels
                ..._filteredChannels.map(
                  (channel) => _buildModalChannelOption(
                    channel,
                    channel.name,
                    '${channel.memberCount} members',
                    Icons.forum,
                  ),
                ),

                if (_filteredChannels.isEmpty &&
                    _searchController.text.isNotEmpty)
                  Padding(
                    padding: const EdgeInsets.symmetric(vertical: 32),
                    child: Text(
                      'No channels found matching "${_searchController.text}"',
                      style: TextStyle(
                        color: AppColors.gfGrayText,
                        fontSize: 14,
                      ),
                      textAlign: TextAlign.center,
                    ),
                  ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildModalChannelOption(
    ChannelModel? channel,
    String title,
    String subtitle,
    IconData icon,
  ) {
    final isSelected =
        (widget.selectedChannel?.id == channel?.id) ||
        (widget.selectedChannel == null && channel == null);

    return GestureDetector(
      onTap: () => Navigator.of(context).pop({'channel': channel}),
      child: Container(
        margin: const EdgeInsets.only(bottom: 8),
        padding: const EdgeInsets.all(12),
        decoration: BoxDecoration(
          color:
              isSelected
                  ? AppColors.gfGreen.withValues(alpha: 0.1)
                  : AppColors.gfDarkBlue.withValues(alpha: 0.3),
          borderRadius: BorderRadius.circular(8),
          border: Border.all(
            color:
                isSelected
                    ? AppColors.gfGreen
                    : AppColors.gfGrayText.withValues(alpha: 0.2),
            width: isSelected ? 2 : 1,
          ),
        ),
        child: Row(
          children: [
            Icon(
              icon,
              color: isSelected ? AppColors.gfGreen : AppColors.gfGrayText,
              size: 20,
            ),
            const SizedBox(width: 12),
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    title,
                    style: TextStyle(
                      color: AppColors.gfOffWhite,
                      fontSize: 14,
                      fontWeight:
                          isSelected ? FontWeight.w600 : FontWeight.w500,
                    ),
                  ),
                  Text(
                    subtitle,
                    style: TextStyle(color: AppColors.gfGrayText, fontSize: 12),
                  ),
                ],
              ),
            ),
            if (isSelected)
              Icon(Icons.check_circle, color: AppColors.gfGreen, size: 20),
          ],
        ),
      ),
    );
  }
}
