import 'package:flutter/material.dart';
import '../components/index.dart';

/// Discord-style reaction display - only shows reactions that have been used
/// This is a wrapper around the new GFReactionBar component for backward compatibility
class ReactionBar extends StatelessWidget {
  final Map<String, int> reactions;
  final String? currentUserReaction; // Single reaction per user
  final void Function(String emoji) onReact;
  final VoidCallback? onShowReactionPicker;
  final VoidCallback? onShowReactionDetails;

  const ReactionBar({
    super.key,
    required this.reactions,
    this.currentUserReaction,
    required this.onReact,
    this.onShowReactionPicker,
    this.onShowReactionDetails,
  });

  @override
  Widget build(BuildContext context) {
    return GFReactionBar(
      reactions: reactions,
      currentUserReaction: currentUserReaction,
      onReact: onReact,
      onShowReactionPicker: onShowReactionPicker,
      onShowReactionDetails: onShowReactionDetails,
    );
  }
}
