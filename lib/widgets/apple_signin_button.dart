import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:sign_in_with_apple/sign_in_with_apple.dart';
import '../providers/auth_provider.dart';
import '../services/apple_signin_service.dart';

/// Apple Sign In button widget
class AppleSignInButton extends StatefulWidget {
  final VoidCallback? onPressed;
  final bool enabled;
  final String text;

  const AppleSignInButton({
    super.key,
    this.onPressed,
    this.enabled = true,
    this.text = 'Sign in with Apple',
  });

  @override
  State<AppleSignInButton> createState() => _AppleSignInButtonState();
}

class _AppleSignInButtonState extends State<AppleSignInButton> {
  bool _isLoading = false;
  bool _isAvailable = false;

  @override
  void initState() {
    super.initState();
    _checkAvailability();
  }

  Future<void> _checkAvailability() async {
    final isAvailable = await AppleSignInService.instance.isAvailable();
    if (mounted) {
      setState(() {
        _isAvailable = isAvailable;
      });
    }
  }

  Future<void> _handleSignIn() async {
    if (!widget.enabled || _isLoading) return;

    setState(() {
      _isLoading = true;
    });

    try {
      final authProvider = Provider.of<AuthProvider>(context, listen: false);
      final success = await authProvider.signInWithApple();

      if (success && widget.onPressed != null) {
        widget.onPressed!();
      }
    } finally {
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
      }
    }
  }

  void _onSignInPressed() {
    _handleSignIn();
  }

  @override
  Widget build(BuildContext context) {
    // Don't show the button if Apple Sign In is not available
    if (!_isAvailable) {
      return const SizedBox.shrink();
    }

    final VoidCallback? callback =
        (widget.enabled && !_isLoading) ? _onSignInPressed : null;

    return SignInWithAppleButton(
      onPressed: callback,
      text: widget.text,
      height: 50,
      style: SignInWithAppleButtonStyle.black,
      borderRadius: BorderRadius.circular(8),
    );
  }
}

/// Custom Apple Sign In button with more control over styling
class CustomAppleSignInButton extends StatefulWidget {
  final VoidCallback? onPressed;
  final bool enabled;
  final String text;
  final Color? backgroundColor;
  final Color? textColor;
  final double height;
  final BorderRadius? borderRadius;

  const CustomAppleSignInButton({
    super.key,
    this.onPressed,
    this.enabled = true,
    this.text = 'Sign in with Apple',
    this.backgroundColor,
    this.textColor,
    this.height = 50,
    this.borderRadius,
  });

  @override
  State<CustomAppleSignInButton> createState() =>
      _CustomAppleSignInButtonState();
}

class _CustomAppleSignInButtonState extends State<CustomAppleSignInButton> {
  bool _isLoading = false;
  bool _isAvailable = false;

  @override
  void initState() {
    super.initState();
    _checkAvailability();
  }

  Future<void> _checkAvailability() async {
    final isAvailable = await AppleSignInService.instance.isAvailable();
    if (mounted) {
      setState(() {
        _isAvailable = isAvailable;
      });
    }
  }

  Future<void> _handleSignIn() async {
    if (!widget.enabled || _isLoading) return;

    setState(() {
      _isLoading = true;
    });

    try {
      final authProvider = Provider.of<AuthProvider>(context, listen: false);
      final success = await authProvider.signInWithApple();

      if (success && widget.onPressed != null) {
        widget.onPressed!();
      }
    } finally {
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
      }
    }
  }

  void _onSignInPressed() {
    _handleSignIn();
  }

  @override
  Widget build(BuildContext context) {
    // Don't show the button if Apple Sign In is not available
    if (!_isAvailable) {
      return const SizedBox.shrink();
    }

    final VoidCallback? callback =
        (widget.enabled && !_isLoading) ? _onSignInPressed : null;

    return SizedBox(
      height: widget.height,
      width: double.infinity,
      child: ElevatedButton.icon(
        onPressed: callback,
        style: ElevatedButton.styleFrom(
          backgroundColor: widget.backgroundColor ?? Colors.black,
          foregroundColor: widget.textColor ?? Colors.white,
          shape: RoundedRectangleBorder(
            borderRadius: widget.borderRadius ?? BorderRadius.circular(8),
          ),
          elevation: 0,
        ),
        icon:
            _isLoading
                ? SizedBox(
                  width: 20,
                  height: 20,
                  child: CircularProgressIndicator(
                    strokeWidth: 2,
                    valueColor: AlwaysStoppedAnimation<Color>(
                      widget.textColor ?? Colors.white,
                    ),
                  ),
                )
                : Icon(
                  Icons.apple,
                  size: 20,
                  color: widget.textColor ?? Colors.white,
                ),
        label: Text(
          widget.text,
          style: TextStyle(
            fontSize: 16,
            fontWeight: FontWeight.w600,
            color: widget.textColor ?? Colors.white,
          ),
        ),
      ),
    );
  }
}
