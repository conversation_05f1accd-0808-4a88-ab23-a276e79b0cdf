import 'package:flutter/material.dart';
import 'package:cached_network_image/cached_network_image.dart';
import '../models/xbox_media_model.dart';
import '../theme/app_theme.dart';

class XboxMediaItemWidget extends StatelessWidget {
  final XboxMediaItem mediaItem;
  final VoidCallback? onTap;
  final bool showDetails;

  const XboxMediaItemWidget({
    super.key,
    required this.mediaItem,
    this.onTap,
    this.showDetails = true,
  });

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: onTap,
      child: Container(
        decoration: BoxDecoration(
          color: AppColors.gfCardBackground,
          borderRadius: BorderRadius.circular(12),
          border: Border.all(color: AppColors.gfGrayBorder, width: 1),
        ),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.stretch,
          children: [
            // Media thumbnail
            Expanded(
              child: ClipRRect(
                borderRadius: const BorderRadius.only(
                  topLeft: Radius.circular(12),
                  topRight: Radius.circular(12),
                ),
                child: Stack(
                  children: [
                    // Thumbnail image
                    CachedNetworkImage(
                      imageUrl: mediaItem.thumbnailUrl,
                      fit: BoxFit.cover,
                      width: double.infinity,
                      height: double.infinity,
                      placeholder:
                          (context, url) => Container(
                            color: AppColors.gfDarkBackground40,
                            child: Center(
                              child: CircularProgressIndicator(
                                color: AppColors.gfGreen,
                                strokeWidth: 2,
                              ),
                            ),
                          ),
                      errorWidget:
                          (context, url, error) => Container(
                            color: AppColors.gfDarkBackground40,
                            child: Column(
                              mainAxisAlignment: MainAxisAlignment.center,
                              children: [
                                Icon(
                                  mediaItem.isScreenshot
                                      ? Icons.photo_outlined
                                      : Icons.videocam_outlined,
                                  color: AppColors.gfGrayText,
                                  size: 32,
                                ),
                                const SizedBox(height: 8),
                                Text(
                                  'No Preview',
                                  style: TextStyle(
                                    color: AppColors.gfGrayText,
                                    fontSize: 12,
                                  ),
                                ),
                              ],
                            ),
                          ),
                    ),

                    // Media type indicator
                    Positioned(
                      top: 8,
                      left: 8,
                      child: Container(
                        padding: const EdgeInsets.symmetric(
                          horizontal: 6,
                          vertical: 2,
                        ),
                        decoration: BoxDecoration(
                          color: Colors.black.withValues(alpha: 0.7),
                          borderRadius: BorderRadius.circular(4),
                        ),
                        child: Row(
                          mainAxisSize: MainAxisSize.min,
                          children: [
                            Icon(
                              mediaItem.isScreenshot
                                  ? Icons.photo_camera
                                  : Icons.videocam,
                              color: Colors.white,
                              size: 12,
                            ),
                            if (mediaItem.isGameClip &&
                                mediaItem.duration != null) ...[
                              const SizedBox(width: 4),
                              Text(
                                mediaItem.formattedDuration,
                                style: const TextStyle(
                                  color: Colors.white,
                                  fontSize: 10,
                                  fontWeight: FontWeight.w500,
                                ),
                              ),
                            ],
                          ],
                        ),
                      ),
                    ),

                    // Resolution indicator
                    Positioned(
                      top: 8,
                      right: 8,
                      child: Container(
                        padding: const EdgeInsets.symmetric(
                          horizontal: 6,
                          vertical: 2,
                        ),
                        decoration: BoxDecoration(
                          color: Colors.black.withValues(alpha: 0.7),
                          borderRadius: BorderRadius.circular(4),
                        ),
                        child: Text(
                          mediaItem.resolutionString,
                          style: const TextStyle(
                            color: Colors.white,
                            fontSize: 10,
                            fontWeight: FontWeight.w500,
                          ),
                        ),
                      ),
                    ),
                  ],
                ),
              ),
            ),

            // Media details
            if (showDetails)
              Padding(
                padding: const EdgeInsets.all(12),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    // Title
                    Text(
                      mediaItem.title.isNotEmpty
                          ? mediaItem.title
                          : 'Untitled ${mediaItem.isScreenshot ? 'Screenshot' : 'Game Clip'}',
                      style: TextStyle(
                        color: AppColors.gfOffWhite,
                        fontSize: 14,
                        fontWeight: FontWeight.w600,
                      ),
                      maxLines: 1,
                      overflow: TextOverflow.ellipsis,
                    ),
                    const SizedBox(height: 4),

                    // Game title
                    Text(
                      mediaItem.gameTitle,
                      style: TextStyle(
                        color: AppColors.gfGrayText,
                        fontSize: 12,
                      ),
                      maxLines: 1,
                      overflow: TextOverflow.ellipsis,
                    ),
                    const SizedBox(height: 4),

                    // Date and file size
                    Row(
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      children: [
                        Text(
                          _formatDate(mediaItem.dateTaken),
                          style: TextStyle(
                            color: AppColors.gfGrayText,
                            fontSize: 10,
                          ),
                        ),
                        Text(
                          mediaItem.formattedFileSize,
                          style: TextStyle(
                            color: AppColors.gfGrayText,
                            fontSize: 10,
                          ),
                        ),
                      ],
                    ),
                  ],
                ),
              ),
          ],
        ),
      ),
    );
  }

  String _formatDate(DateTime date) {
    final now = DateTime.now();
    final difference = now.difference(date);

    if (difference.inDays == 0) {
      return 'Today';
    } else if (difference.inDays == 1) {
      return 'Yesterday';
    } else if (difference.inDays < 7) {
      return '${difference.inDays} days ago';
    } else if (difference.inDays < 30) {
      final weeks = (difference.inDays / 7).floor();
      return weeks == 1 ? '1 week ago' : '$weeks weeks ago';
    } else if (difference.inDays < 365) {
      final months = (difference.inDays / 30).floor();
      return months == 1 ? '1 month ago' : '$months months ago';
    } else {
      final years = (difference.inDays / 365).floor();
      return years == 1 ? '1 year ago' : '$years years ago';
    }
  }
}
