import 'dart:ui';
import 'package:flutter/material.dart';
import 'package:provider/provider.dart';

import '../models/reflex_model.dart';
import '../models/post_model.dart';
import '../providers/reflex_provider.dart';

import '../theme/app_theme.dart';
import '../utils/app_logger.dart';
import '../widgets/reflex_floating_card.dart';
import '../screens/reflexes_feed_screen.dart';

class ReflexDrawerOverlay extends StatefulWidget {
  final PostModel post;
  final int initialIndex;
  final VoidCallback onClose;
  final VoidCallback? onPauseVideo;

  const ReflexDrawerOverlay({
    super.key,
    required this.post,
    this.initialIndex = 0,
    required this.onClose,
    this.onPauseVideo,
  });

  @override
  State<ReflexDrawerOverlay> createState() => _ReflexDrawerOverlayState();
}

class _ReflexDrawerOverlayState extends State<ReflexDrawerOverlay>
    with TickerProviderStateMixin {
  final ScrollController _scrollController = ScrollController();
  late AnimationController _slideController;
  late Animation<Offset> _slideAnimation;
  late AnimationController _fadeController;
  late Animation<double> _fadeAnimation;

  int _currentIndex = 0;
  List<ReflexModel> _allReflexes = [];
  bool _isLoading = false;
  String? _error;

  // For smooth scrolling and preloading
  final double _cardWidth = 320.0;
  final double _cardSpacing = 25.0;
  final Map<int, bool> _loadedImages = {};
  final Set<int> _loadingImages = {};

  @override
  void initState() {
    super.initState();
    _currentIndex = widget.initialIndex;

    // Pause video if callback provided
    if (widget.onPauseVideo != null) {
      widget.onPauseVideo!();
    }

    // Add scroll listener for smooth scrolling
    _scrollController.addListener(_onScroll);

    // Initialize slide animation controller
    _slideController = AnimationController(
      duration: const Duration(milliseconds: 300),
      vsync: this,
    );

    _slideAnimation = Tween<Offset>(
      begin: const Offset(1.0, 0.0), // Start from right
      end: Offset.zero,
    ).animate(
      CurvedAnimation(parent: _slideController, curve: Curves.easeInOut),
    );

    // Initialize fade animation controller
    _fadeController = AnimationController(
      duration: const Duration(milliseconds: 300),
      vsync: this,
    );

    _fadeAnimation = Tween<double>(begin: 0.0, end: 1.0).animate(
      CurvedAnimation(parent: _fadeController, curve: Curves.easeInOut),
    );

    // Load reflexes after the frame is built to avoid setState during build
    WidgetsBinding.instance.addPostFrameCallback((_) {
      _loadReflexesFromPosts();
    });

    // Start animations
    _fadeController.forward();
    _slideController.forward();
  }

  @override
  void dispose() {
    _scrollController.dispose();
    _slideController.dispose();
    _fadeController.dispose();
    super.dispose();
  }

  void _onScroll() {
    if (!_scrollController.hasClients || _allReflexes.isEmpty) return;

    final scrollOffset = _scrollController.offset;
    final cardTotalWidth = _cardWidth + _cardSpacing;
    final newIndex = (scrollOffset / cardTotalWidth).round().clamp(
      0,
      _allReflexes.length - 1,
    );

    if (newIndex != _currentIndex) {
      setState(() {
        _currentIndex = newIndex;
      });

      // Preload images around current index
      _preloadImagesAroundIndex(newIndex);
    }
  }

  void _preloadImagesAroundIndex(int centerIndex) {
    const preloadRange = 2; // Load 2 images before and after current

    for (
      int i = centerIndex - preloadRange;
      i <= centerIndex + preloadRange;
      i++
    ) {
      if (i >= 0 &&
          i < _allReflexes.length &&
          !_loadedImages.containsKey(i) &&
          !_loadingImages.contains(i)) {
        _loadingImages.add(i);
        _preloadReflexImage(i);
      }
    }

    // Cancel loading for images too far away
    final toCancel = <int>[];
    for (final index in _loadingImages) {
      if ((index - centerIndex).abs() > preloadRange + 1) {
        toCancel.add(index);
      }
    }

    for (final index in toCancel) {
      _loadingImages.remove(index);
    }
  }

  Future<void> _preloadReflexImage(int index) async {
    if (index < 0 || index >= _allReflexes.length) return;

    final reflex = _allReflexes[index];
    if (reflex.reflexType == ReflexType.customImage && reflex.mediaId != null) {
      try {
        final mediaUrl = await reflex.getMediaUrl();
        if (mediaUrl != null && mounted) {
          // Preload the image
          await precacheImage(NetworkImage(mediaUrl), context);
          if (mounted) {
            setState(() {
              _loadedImages[index] = true;
              _loadingImages.remove(index);
            });
          }
        }
      } catch (e) {
        if (mounted) {
          _loadingImages.remove(index);
        }
      }
    } else {
      // For flare reflexes, mark as loaded immediately
      if (mounted) {
        setState(() {
          _loadedImages[index] = true;
          _loadingImages.remove(index);
        });
      }
    }
  }

  Future<void> _loadReflexesFromPosts() async {
    if (_isLoading) return;

    setState(() {
      _isLoading = true;
      _error = null;
    });

    try {
      AppLogger.debug(
        'ReflexDrawerOverlay: Loading reflexes for post ${widget.post.id}',
      );

      final reflexProvider = Provider.of<ReflexProvider>(
        context,
        listen: false,
      );

      // Load reflexes for the specific post only
      await reflexProvider.loadReflexesForPost(widget.post.id);
      final postReflexes = reflexProvider.getReflexesForPost(widget.post.id);

      // Sort by creation time (most recent first)
      postReflexes.sort((a, b) => b.createdAt.compareTo(a.createdAt));

      if (mounted) {
        setState(() {
          _allReflexes = postReflexes;
          _isLoading = false;
        });

        // Set initial scroll position after loading
        if (_allReflexes.isNotEmpty && _currentIndex < _allReflexes.length) {
          WidgetsBinding.instance.addPostFrameCallback((_) {
            if (_scrollController.hasClients) {
              final cardTotalWidth = _cardWidth + _cardSpacing;
              final targetOffset = _currentIndex * cardTotalWidth;
              _scrollController.animateTo(
                targetOffset,
                duration: const Duration(milliseconds: 300),
                curve: Curves.easeInOut,
              );
            }
          });

          // Start preloading images around initial index
          _preloadImagesAroundIndex(_currentIndex);
        }
      }

      AppLogger.debug(
        'ReflexDrawerOverlay: Loaded ${postReflexes.length} reflexes for post ${widget.post.id}',
      );
    } catch (e, stackTrace) {
      AppLogger.error(
        'ReflexDrawerOverlay: Error loading reflexes',
        error: e,
        stackTrace: stackTrace,
      );
      if (mounted) {
        setState(() {
          _error = e.toString();
          _isLoading = false;
        });
      }
    }
  }

  void _navigateToFullReflex(int index) {
    AppLogger.debug(
      'ReflexDrawerOverlay: Navigating to full reflex at index $index',
    );

    Navigator.of(context)
        .push(
          MaterialPageRoute(
            builder:
                (context) =>
                    ReflexesFeedScreen(post: widget.post, initialIndex: index),
          ),
        )
        .then((_) {
          // When returning from full reflex, maintain the same position
          if (mounted &&
              _scrollController.hasClients &&
              index < _allReflexes.length) {
            final cardTotalWidth = _cardWidth + _cardSpacing;
            final targetOffset = index * cardTotalWidth;
            _scrollController.animateTo(
              targetOffset,
              duration: const Duration(milliseconds: 300),
              curve: Curves.easeInOut,
            );
          }
        });
  }

  void _closeDrawer() {
    _fadeController.reverse();
    _slideController.reverse().then((_) {
      if (mounted) {
        widget.onClose();
      }
    });
  }

  @override
  Widget build(BuildContext context) {
    return AnimatedBuilder(
      animation: _fadeAnimation,
      builder: (context, child) {
        return BackdropFilter(
          filter: ImageFilter.blur(
            sigmaX: 10.0 * _fadeAnimation.value,
            sigmaY: 10.0 * _fadeAnimation.value,
          ),
          child: Container(
            color: Colors.black.withValues(alpha: 0.3 * _fadeAnimation.value),
            child: SlideTransition(
              position: _slideAnimation,
              child: _buildBody(),
            ),
          ),
        );
      },
    );
  }

  Widget _buildBody() {
    if (_isLoading) {
      return const Center(
        child: CircularProgressIndicator(
          valueColor: AlwaysStoppedAnimation<Color>(AppColors.gfGreen),
        ),
      );
    }

    if (_error != null) {
      return _buildErrorStateCard();
    }

    if (_allReflexes.isEmpty) {
      return _buildEmptyStateCard();
    }

    return _buildDrawerContent();
  }

  Widget _buildDrawerContent() {
    return GestureDetector(
      onTap: _closeDrawer,
      child: Container(
        color: Colors.transparent,
        child: Stack(
          children: [
            // Main floating cards area
            Center(
              child: SizedBox(
                height: MediaQuery.of(context).size.height * 0.6,
                child: ListView.builder(
                  controller: _scrollController,
                  scrollDirection: Axis.horizontal,
                  itemCount: _allReflexes.length,
                  physics: const BouncingScrollPhysics(),
                  padding: EdgeInsets.symmetric(horizontal: _cardSpacing),
                  itemBuilder: (context, index) {
                    final reflex = _allReflexes[index];
                    final isCenter = index == _currentIndex;
                    final isLoaded = _loadedImages[index] ?? false;

                    return Container(
                      width: _cardWidth,
                      margin: EdgeInsets.only(right: _cardSpacing),
                      child: ReflexFloatingCard(
                        reflex: reflex,
                        isCenter: isCenter,
                        isLoaded: isLoaded,
                        onTap: () => _navigateToFullReflex(index),
                      ),
                    );
                  },
                ),
              ),
            ),
            // Close button
            Positioned(
              top: MediaQuery.of(context).padding.top + 16,
              right: 16,
              child: GestureDetector(
                onTap: _closeDrawer,
                child: Container(
                  padding: const EdgeInsets.all(8),
                  decoration: BoxDecoration(
                    color: Colors.black.withValues(alpha: 0.5),
                    shape: BoxShape.circle,
                  ),
                  child: const Icon(
                    Icons.close,
                    color: AppColors.gfOffWhite,
                    size: 24,
                  ),
                ),
              ),
            ),
            // Page indicator
            if (_allReflexes.length > 1)
              Positioned(
                bottom: 50,
                left: 0,
                right: 0,
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: List.generate(
                    _allReflexes.length,
                    (index) => Container(
                      margin: const EdgeInsets.symmetric(horizontal: 4),
                      width: 8,
                      height: 8,
                      decoration: BoxDecoration(
                        shape: BoxShape.circle,
                        color:
                            index == _currentIndex
                                ? AppColors.gfGreen
                                : AppColors.gfGrayText.withValues(alpha: 0.5),
                      ),
                    ),
                  ),
                ),
              ),
          ],
        ),
      ),
    );
  }

  Widget _buildErrorStateCard() {
    return GestureDetector(
      onTap: _closeDrawer,
      child: Container(
        color: Colors.transparent,
        child: Stack(
          children: [
            // Error state card in center
            Center(
              child: Container(
                margin: const EdgeInsets.symmetric(horizontal: 40),
                padding: const EdgeInsets.all(32),
                decoration: BoxDecoration(
                  color: AppColors.gfDarkBackground,
                  borderRadius: BorderRadius.circular(20),
                  border: Border.all(
                    color: Colors.red.withValues(alpha: 0.3),
                    width: 1,
                  ),
                  boxShadow: [
                    BoxShadow(
                      color: Colors.black.withValues(alpha: 0.3),
                      blurRadius: 20,
                      spreadRadius: 5,
                      offset: const Offset(0, 10),
                    ),
                  ],
                ),
                child: Column(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    const Icon(
                      Icons.error_outline,
                      color: Colors.red,
                      size: 64,
                    ),
                    const SizedBox(height: 16),
                    const Text(
                      'Error loading reflexes',
                      style: TextStyle(
                        color: AppColors.gfOffWhite,
                        fontSize: 18,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    const SizedBox(height: 8),
                    Text(
                      _error!,
                      style: const TextStyle(
                        color: AppColors.gfGrayText,
                        fontSize: 14,
                      ),
                      textAlign: TextAlign.center,
                    ),
                    const SizedBox(height: 20),
                    ElevatedButton(
                      onPressed: _loadReflexesFromPosts,
                      style: ElevatedButton.styleFrom(
                        backgroundColor: AppColors.gfGreen,
                        foregroundColor: Colors.white,
                      ),
                      child: const Text('Retry'),
                    ),
                    const SizedBox(height: 16),
                    Container(
                      padding: const EdgeInsets.symmetric(
                        horizontal: 16,
                        vertical: 8,
                      ),
                      decoration: BoxDecoration(
                        color: AppColors.gfGrayBorder.withValues(alpha: 0.2),
                        borderRadius: BorderRadius.circular(12),
                      ),
                      child: const Text(
                        'Tap anywhere to close',
                        style: TextStyle(
                          color: AppColors.gfGrayText,
                          fontSize: 12,
                        ),
                      ),
                    ),
                  ],
                ),
              ),
            ),
            // Close button
            Positioned(
              top: MediaQuery.of(context).padding.top + 16,
              right: 16,
              child: GestureDetector(
                onTap: _closeDrawer,
                child: Container(
                  padding: const EdgeInsets.all(8),
                  decoration: BoxDecoration(
                    color: Colors.black.withValues(alpha: 0.5),
                    shape: BoxShape.circle,
                  ),
                  child: const Icon(
                    Icons.close,
                    color: AppColors.gfOffWhite,
                    size: 24,
                  ),
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildEmptyStateCard() {
    return GestureDetector(
      onTap: _closeDrawer,
      child: Container(
        color: Colors.transparent,
        child: Stack(
          children: [
            // Empty state card in center
            Center(
              child: Container(
                margin: const EdgeInsets.symmetric(horizontal: 40),
                padding: const EdgeInsets.all(32),
                decoration: BoxDecoration(
                  color: AppColors.gfDarkBackground,
                  borderRadius: BorderRadius.circular(20),
                  border: Border.all(
                    color: AppColors.gfGrayBorder.withValues(alpha: 0.3),
                    width: 1,
                  ),
                  boxShadow: [
                    BoxShadow(
                      color: Colors.black.withValues(alpha: 0.3),
                      blurRadius: 20,
                      spreadRadius: 5,
                      offset: const Offset(0, 10),
                    ),
                  ],
                ),
                child: Column(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    const Icon(
                      Icons.photo_library_outlined,
                      color: AppColors.gfGrayText,
                      size: 64,
                    ),
                    const SizedBox(height: 16),
                    const Text(
                      'No reflexes yet',
                      style: TextStyle(
                        color: AppColors.gfOffWhite,
                        fontSize: 18,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    const SizedBox(height: 8),
                    const Text(
                      'Be the first to create a reflex for this post!',
                      style: TextStyle(
                        color: AppColors.gfGrayText,
                        fontSize: 14,
                      ),
                      textAlign: TextAlign.center,
                    ),
                    const SizedBox(height: 20),
                    Container(
                      padding: const EdgeInsets.symmetric(
                        horizontal: 16,
                        vertical: 8,
                      ),
                      decoration: BoxDecoration(
                        color: AppColors.gfGrayBorder.withValues(alpha: 0.2),
                        borderRadius: BorderRadius.circular(12),
                      ),
                      child: const Text(
                        'Tap anywhere to close',
                        style: TextStyle(
                          color: AppColors.gfGrayText,
                          fontSize: 12,
                        ),
                      ),
                    ),
                  ],
                ),
              ),
            ),
            // Close button
            Positioned(
              top: MediaQuery.of(context).padding.top + 16,
              right: 16,
              child: GestureDetector(
                onTap: _closeDrawer,
                child: Container(
                  padding: const EdgeInsets.all(8),
                  decoration: BoxDecoration(
                    color: Colors.black.withValues(alpha: 0.5),
                    shape: BoxShape.circle,
                  ),
                  child: const Icon(
                    Icons.close,
                    color: AppColors.gfOffWhite,
                    size: 24,
                  ),
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }
}
