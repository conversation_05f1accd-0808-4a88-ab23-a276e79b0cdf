// lib/widgets/flare_selection_widget.dart

import 'package:flutter/material.dart';
import '../models/flare_model.dart';
import '../services/flare_service.dart';
import '../theme/app_theme.dart';
import '../utils/app_logger.dart';

class FlareSelectionWidget extends StatefulWidget {
  final Function(FlareItem) onFlareSelected;

  const FlareSelectionWidget({super.key, required this.onFlareSelected});

  @override
  State<FlareSelectionWidget> createState() => _FlareSelectionWidgetState();
}

class _FlareSelectionWidgetState extends State<FlareSelectionWidget>
    with SingleTickerProviderStateMixin {
  late TabController _tabController;
  final FlareService _flareService = FlareService();
  late List<FlareCategory> _categories;

  @override
  void initState() {
    super.initState();
    _categories = _flareService.getCategories();
    _tabController = TabController(length: _categories.length, vsync: this);
  }

  @override
  void dispose() {
    _tabController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      height: MediaQuery.of(context).size.height * 0.6,
      decoration: const BoxDecoration(
        color: AppColors.gfDarkBackground,
        borderRadius: BorderRadius.vertical(top: Radius.circular(20)),
      ),
      child: Column(
        children: [
          // Handle bar
          Container(
            margin: const EdgeInsets.only(top: 8),
            width: 40,
            height: 4,
            decoration: BoxDecoration(
              color: AppColors.gfGrayText,
              borderRadius: BorderRadius.circular(2),
            ),
          ),

          // Header
          Padding(
            padding: const EdgeInsets.all(16.0),
            child: Row(
              children: [
                const Text(
                  'Add Flare',
                  style: TextStyle(
                    fontSize: 20,
                    fontWeight: FontWeight.bold,
                    color: AppColors.gfOffWhite,
                  ),
                ),
                const Spacer(),
                IconButton(
                  onPressed: () => Navigator.of(context).pop(),
                  icon: const Icon(Icons.close, color: AppColors.gfOffWhite),
                ),
              ],
            ),
          ),

          // Category tabs
          Container(
            decoration: const BoxDecoration(
              border: Border(
                bottom: BorderSide(color: AppColors.gfGrayText, width: 0.5),
              ),
            ),
            child: TabBar(
              controller: _tabController,
              isScrollable: true,
              indicatorColor: AppColors.gfGreen,
              labelColor: AppColors.gfGreen,
              unselectedLabelColor: AppColors.gfGrayText,
              labelStyle: const TextStyle(
                fontSize: 14,
                fontWeight: FontWeight.w600,
              ),
              unselectedLabelStyle: const TextStyle(
                fontSize: 14,
                fontWeight: FontWeight.normal,
              ),
              tabs:
                  _categories.map((category) {
                    return Tab(text: category.displayName);
                  }).toList(),
            ),
          ),

          // Flare grid
          Expanded(
            child: TabBarView(
              controller: _tabController,
              children:
                  _categories.map((category) {
                    return _buildFlareGrid(category);
                  }).toList(),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildFlareGrid(FlareCategory category) {
    final flareItems = _flareService.getFlareForCategory(category);

    if (flareItems.isEmpty) {
      return const Center(
        child: Text(
          'No flare available',
          style: TextStyle(color: AppColors.gfGrayText, fontSize: 16),
        ),
      );
    }

    return Padding(
      padding: const EdgeInsets.all(16.0),
      child: GridView.builder(
        gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
          crossAxisCount: 4,
          crossAxisSpacing: 12,
          mainAxisSpacing: 12,
          childAspectRatio: 1.0,
        ),
        itemCount: flareItems.length,
        itemBuilder: (context, index) {
          final flareItem = flareItems[index];
          return _buildFlareTile(flareItem);
        },
      ),
    );
  }

  Widget _buildFlareTile(FlareItem flareItem) {
    return GestureDetector(
      onTap: () {
        widget.onFlareSelected(flareItem);
        Navigator.of(context).pop();
      },
      child: Container(
        decoration: BoxDecoration(
          color: AppColors.gfDarkBackground100,
          borderRadius: BorderRadius.circular(8),
          border: Border.all(
            color: AppColors.gfGrayText.withValues(alpha: 0.3),
            width: 1,
          ),
        ),
        child: ClipRRect(
          borderRadius: BorderRadius.circular(8),
          child: Stack(
            children: [
              // Flare image
              Positioned.fill(
                child: Image.asset(
                  flareItem.assetPath,
                  fit: BoxFit.contain,
                  errorBuilder: (context, error, stackTrace) {
                    AppLogger.error('Error loading flare: ${flareItem.assetPath}');
                    return Container(
                      color: AppColors.gfDarkBackground100,
                      child: const Icon(
                        Icons.image_not_supported,
                        color: AppColors.gfGrayText,
                        size: 24,
                      ),
                    );
                  },
                ),
              ),

              // Hover effect
              Positioned.fill(
                child: Material(
                  color: Colors.transparent,
                  child: InkWell(
                    borderRadius: BorderRadius.circular(8),
                    onTap: () {
                      widget.onFlareSelected(flareItem);
                      Navigator.of(context).pop();
                    },
                    child: Container(
                      decoration: BoxDecoration(
                        borderRadius: BorderRadius.circular(8),
                        color: Colors.transparent,
                      ),
                    ),
                  ),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }
}

// Helper function to show the flare selection bottom sheet
void showFlareSelection(
  BuildContext context,
  Function(FlareItem) onFlareSelected,
) {
  showModalBottomSheet(
    context: context,
    backgroundColor: Colors.transparent,
    isScrollControlled: true,
    builder:
        (context) =>
            FlareSelectionWidget(onFlareSelected: onFlareSelected),
  );
}
