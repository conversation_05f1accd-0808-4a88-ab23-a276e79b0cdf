import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../theme/app_theme.dart';
import '../screens/channel_detail_screen.dart';
import '../providers/channels_provider.dart';
import '../models/channel_model.dart';
import '../components/index.dart';

class HomeTabWithNavigation extends StatefulWidget {
  final Widget feedWidget;

  const HomeTabWithNavigation({super.key, required this.feedWidget});

  @override
  State<HomeTabWithNavigation> createState() => _HomeTabWithNavigationState();
}

class _HomeTabWithNavigationState extends State<HomeTabWithNavigation>
    with SingleTickerProviderStateMixin {
  late TabController _tabController;

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 2, vsync: this);
    _tabController.addListener(_onTabChanged);
  }

  @override
  void dispose() {
    _tabController.removeListener(_onTabChanged);
    _tabController.dispose();
    super.dispose();
  }

  void _onTabChanged() {
    if (_tabController.indexIsChanging) {
      // Load channels when switching to channels tab
      if (_tabController.index == 1) {
        final channelsProvider = Provider.of<ChannelsProvider>(
          context,
          listen: false,
        );
        if (channelsProvider.channels.isEmpty &&
            channelsProvider.status == ChannelsStatus.initial) {
          channelsProvider.loadChannels();
        }
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return Column(
      children: [
        // Top navigation bar
        Container(
          decoration: const BoxDecoration(
            color: AppColors.gfDarkBlue,
            border: Border(
              bottom: BorderSide(color: AppColors.gfGrayBorder, width: 0.5),
            ),
          ),
          child: SafeArea(
            bottom: false,
            child: TabBar(
              controller: _tabController,
              indicatorColor: AppColors.gfGreen,
              indicatorWeight: 3,
              labelColor: AppColors.gfGreen,
              unselectedLabelColor: AppColors.gfGrayText,
              labelStyle: const TextStyle(
                fontSize: 16,
                fontWeight: FontWeight.w600,
              ),
              unselectedLabelStyle: const TextStyle(
                fontSize: 16,
                fontWeight: FontWeight.normal,
              ),
              tabs: const [
                Tab(text: 'Posts', icon: Icon(Icons.home, size: 20)),
                Tab(text: 'Channels', icon: Icon(Icons.forum, size: 20)),
              ],
            ),
          ),
        ),
        // Tab content
        Expanded(
          child: TabBarView(
            controller: _tabController,
            children: [
              // Posts tab - existing feed
              widget.feedWidget,
              // Channels tab - channels screen without scaffold
              const ChannelsScreenContent(),
            ],
          ),
        ),
      ],
    );
  }
}

// Channels screen content without scaffold
class ChannelsScreenContent extends StatefulWidget {
  const ChannelsScreenContent({super.key});

  @override
  State<ChannelsScreenContent> createState() => _ChannelsScreenContentState();
}

class _ChannelsScreenContentState extends State<ChannelsScreenContent> {
  final ScrollController _scrollController = ScrollController();

  @override
  void initState() {
    super.initState();
    _scrollController.addListener(_onScroll);

    // Load channels when screen initializes
    WidgetsBinding.instance.addPostFrameCallback((_) {
      final channelsProvider = Provider.of<ChannelsProvider>(
        context,
        listen: false,
      );
      if (channelsProvider.channels.isEmpty) {
        channelsProvider.loadChannels();
      }
    });
  }

  @override
  void dispose() {
    _scrollController.removeListener(_onScroll);
    _scrollController.dispose();
    super.dispose();
  }

  void _onScroll() {
    if (_scrollController.position.pixels >=
        _scrollController.position.maxScrollExtent - 200) {
      final channelsProvider = Provider.of<ChannelsProvider>(
        context,
        listen: false,
      );
      if (channelsProvider.hasMore && !channelsProvider.isLoading) {
        channelsProvider.loadMoreChannels();
      }
    }
  }

  Future<void> _onRefresh() async {
    final channelsProvider = Provider.of<ChannelsProvider>(
      context,
      listen: false,
    );
    await channelsProvider.refreshChannels();
  }

  void _onChannelTap(ChannelModel channel) {
    Navigator.of(context).push(
      MaterialPageRoute(
        builder: (context) => ChannelDetailScreen(channel: channel),
      ),
    );
  }

  void _onJoinChannel(ChannelModel channel) async {
    final channelsProvider = Provider.of<ChannelsProvider>(
      context,
      listen: false,
    );

    final success = await channelsProvider.joinChannel(channel.id);

    if (!mounted) return;

    if (success) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text('Joined ${channel.name}!'),
          backgroundColor: AppColors.gfGreen,
        ),
      );
    } else {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text('Failed to join ${channel.name}'),
          backgroundColor: AppColors.gfYellow,
        ),
      );
    }
  }

  void _onLeaveChannel(ChannelModel channel) async {
    final channelsProvider = Provider.of<ChannelsProvider>(
      context,
      listen: false,
    );

    final success = await channelsProvider.leaveChannel(channel.id);

    if (!mounted) return;

    if (success) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text('Left ${channel.name}'),
          backgroundColor: AppColors.gfGreen,
        ),
      );
    } else {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text('Failed to leave ${channel.name}'),
          backgroundColor: AppColors.gfYellow,
        ),
      );
    }
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      color: AppColors.gfDarkBlue,
      child: Consumer<ChannelsProvider>(
        builder: (context, channelsProvider, child) {
          if (channelsProvider.status == ChannelsStatus.loading &&
              channelsProvider.channels.isEmpty) {
            return const Center(
              child: CircularProgressIndicator(
                valueColor: AlwaysStoppedAnimation<Color>(AppColors.gfGreen),
              ),
            );
          }

          if (channelsProvider.status == ChannelsStatus.error) {
            return Center(
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  const Icon(Icons.error_outline, size: 64, color: Colors.red),
                  const SizedBox(height: 16),
                  Text(
                    'Error loading channels',
                    style: const TextStyle(
                      color: Colors.white,
                      fontSize: 18,
                      fontWeight: FontWeight.w500,
                    ),
                  ),
                  const SizedBox(height: 8),
                  Text(
                    channelsProvider.error ?? 'Unknown error',
                    style: const TextStyle(color: Colors.grey, fontSize: 14),
                    textAlign: TextAlign.center,
                  ),
                  const SizedBox(height: 24),
                  ElevatedButton(
                    onPressed: () => channelsProvider.loadChannels(),
                    style: ElevatedButton.styleFrom(
                      backgroundColor: AppColors.gfGreen,
                      foregroundColor: AppColors.gfDarkBlue,
                    ),
                    child: const Text('Retry'),
                  ),
                ],
              ),
            );
          }

          if (channelsProvider.channels.isEmpty) {
            return const Center(
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Icon(Icons.forum_outlined, size: 64, color: Colors.grey),
                  SizedBox(height: 16),
                  Text(
                    'No channels found',
                    style: TextStyle(
                      color: Colors.white,
                      fontSize: 18,
                      fontWeight: FontWeight.w500,
                    ),
                  ),
                  SizedBox(height: 8),
                  Text(
                    'Check back later for new channels',
                    style: TextStyle(color: Colors.grey, fontSize: 14),
                  ),
                ],
              ),
            );
          }

          return RefreshIndicator(
            onRefresh: _onRefresh,
            color: AppColors.gfGreen,
            backgroundColor: AppColors.gfDarkBlue,
            child: CustomScrollView(
              controller: _scrollController,
              slivers: [
                SliverPadding(
                  padding: const EdgeInsets.symmetric(
                    horizontal: 20,
                    vertical: 16,
                  ),
                  sliver: SliverGrid(
                    gridDelegate:
                        const SliverGridDelegateWithFixedCrossAxisCount(
                          crossAxisCount: 2,
                          childAspectRatio: 1.0, // Square aspect ratio
                          crossAxisSpacing: 16,
                          mainAxisSpacing: 16,
                        ),
                    delegate: SliverChildBuilderDelegate(
                      (context, index) {
                        if (index >= channelsProvider.channels.length) {
                          return const Center(
                            child: CircularProgressIndicator(
                              valueColor: AlwaysStoppedAnimation<Color>(
                                AppColors.gfGreen,
                              ),
                            ),
                          );
                        }

                        final channel = channelsProvider.channels[index];
                        return GFChannelCard(
                          channel: channel,
                          onTap: () => _onChannelTap(channel),
                          onJoin:
                              channel.isUserMember
                                  ? null
                                  : () => _onJoinChannel(channel),
                          onLeave:
                              channel.isUserMember
                                  ? () => _onLeaveChannel(channel)
                                  : null,
                          showJoinButton: !channel.isUserMember,
                          isJoined: channel.isUserMember,
                        );
                      },
                      childCount:
                          channelsProvider.channels.length +
                          (channelsProvider.hasMore &&
                                  channelsProvider.isLoading
                              ? 1
                              : 0),
                    ),
                  ),
                ),
              ],
            ),
          );
        },
      ),
    );
  }
}
