import 'package:logger/logger.dart';
import 'package:flutter/foundation.dart';

/// Centralized logging utility for GameFlex app
/// 
/// This class provides a consistent logging interface throughout the app,
/// replacing all print() statements with proper structured logging.
/// 
/// Usage:
/// ```dart
/// AppLogger.debug('Debug message');
/// AppLogger.info('Info message');
/// AppLogger.warning('Warning message');
/// AppLogger.error('Error message', error: exception, stackTrace: stackTrace);
/// ```
class AppLogger {
  static final Logger _logger = Logger(
    filter: kDebugMode ? DevelopmentFilter() : ProductionFilter(),
    printer: PrettyPrinter(
      methodCount: 2,
      errorMethodCount: 8,
      lineLength: 120,
      colors: true,
      printEmojis: true,
      dateTimeFormat: DateTimeFormat.onlyTimeAndSinceStart,
    ),
    output: ConsoleOutput(),
  );

  /// Log debug messages (lowest priority)
  /// Use for detailed debugging information
  static void debug(String message, {Object? error, StackTrace? stackTrace}) {
    _logger.d(message, error: error, stackTrace: stackTrace);
  }

  /// Log info messages
  /// Use for general information about app flow
  static void info(String message, {Object? error, StackTrace? stackTrace}) {
    _logger.i(message, error: error, stackTrace: stackTrace);
  }

  /// Log warning messages
  /// Use for potentially harmful situations
  static void warning(String message, {Object? error, StackTrace? stackTrace}) {
    _logger.w(message, error: error, stackTrace: stackTrace);
  }

  /// Log error messages (highest priority)
  /// Use for error events that might still allow the app to continue
  static void error(String message, {Object? error, StackTrace? stackTrace}) {
    _logger.e(message, error: error, stackTrace: stackTrace);
  }

  /// Log API requests and responses
  /// Specialized logging for API interactions
  static void api(String message, {Map<String, dynamic>? data}) {
    if (kDebugMode) {
      _logger.d('🌐 API: $message', error: data);
    }
  }

  /// Log authentication events
  /// Specialized logging for auth flow
  static void auth(String message, {Object? error, StackTrace? stackTrace}) {
    _logger.i('🔐 AUTH: $message', error: error, stackTrace: stackTrace);
  }

  /// Log posts-related events
  /// Specialized logging for posts operations
  static void posts(String message, {Object? error, StackTrace? stackTrace}) {
    _logger.i('📝 POSTS: $message', error: error, stackTrace: stackTrace);
  }

  /// Log media-related events
  /// Specialized logging for media operations
  static void media(String message, {Object? error, StackTrace? stackTrace}) {
    _logger.i('🎬 MEDIA: $message', error: error, stackTrace: stackTrace);
  }

  /// Log user-related events
  /// Specialized logging for user operations
  static void user(String message, {Object? error, StackTrace? stackTrace}) {
    _logger.i('👤 USER: $message', error: error, stackTrace: stackTrace);
  }

  /// Log channel-related events
  /// Specialized logging for channel operations
  static void channel(String message, {Object? error, StackTrace? stackTrace}) {
    _logger.i('📺 CHANNEL: $message', error: error, stackTrace: stackTrace);
  }

  /// Log provider state changes
  /// Specialized logging for provider operations
  static void provider(String message, {Object? error, StackTrace? stackTrace}) {
    _logger.d('🔄 PROVIDER: $message', error: error, stackTrace: stackTrace);
  }

  /// Log navigation events
  /// Specialized logging for navigation
  static void navigation(String message, {Object? error, StackTrace? stackTrace}) {
    _logger.d('🧭 NAV: $message', error: error, stackTrace: stackTrace);
  }

  /// Log image processing events
  /// Specialized logging for image operations
  static void imageProcessing(String message, {Object? error, StackTrace? stackTrace}) {
    _logger.d('🖼️ IMAGE: $message', error: error, stackTrace: stackTrace);
  }
}
