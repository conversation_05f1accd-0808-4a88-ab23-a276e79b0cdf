import 'dart:io';
import 'dart:developer' as developer;

class VideoUtils {
  /// Check if the file is a valid video file based on extension
  static bool isValidVideoFile(File file) {
    try {
      final extension = file.path.split('.').last.toLowerCase();
      const validExtensions = ['mp4', 'mov', 'avi', 'mkv', 'm4v', '3gp'];
      return validExtensions.contains(extension);
    } catch (e) {
      developer.log('VideoUtils: Error checking video file validity: $e');
      return false;
    }
  }

  /// Get file size in MB
  static Future<double> getFileSizeMB(File file) async {
    try {
      final bytes = await file.length();
      return bytes / (1024 * 1024); // Convert bytes to MB
    } catch (e) {
      developer.log('VideoUtils: Error getting file size: $e');
      return 0.0;
    }
  }

  /// Get MIME type for video file based on extension
  static String getMimeType(File file) {
    final extension = file.path.split('.').last.toLowerCase();
    switch (extension) {
      case 'mp4':
      case 'm4v':
        return 'video/mp4';
      case 'mov':
        return 'video/quicktime';
      case 'avi':
        return 'video/x-msvideo';
      case 'mkv':
        return 'video/x-matroska';
      case '3gp':
        return 'video/3gpp';
      default:
        return 'video/mp4'; // Default fallback
    }
  }

  /// Get duration of video file (placeholder - would need video_player or similar)
  static Future<Duration?> getVideoDuration(File file) async {
    try {
      // This is a placeholder implementation
      // In a real app, you'd use video_player or ffmpeg to get actual duration
      developer.log('VideoUtils: Getting duration for ${file.path}');
      return null; // Return null for now
    } catch (e) {
      developer.log('VideoUtils: Error getting video duration: $e');
      return null;
    }
  }

  /// Validate video file constraints
  static Future<String?> validateVideoFile(File file) async {
    try {
      // Check if file exists
      if (!await file.exists()) {
        return 'Video file does not exist';
      }

      // Check if it's a valid video file
      if (!isValidVideoFile(file)) {
        return 'Invalid video file format. Supported formats: MP4, MOV, AVI, MKV, M4V, 3GP';
      }

      // Check file size (30MB limit for 30-second videos)
      final sizeMB = await getFileSizeMB(file);
      if (sizeMB > 30) {
        return 'Video file is too large. Maximum size is 30MB.';
      }

      // Check minimum file size (1KB)
      if (sizeMB < 0.001) {
        return 'Video file is too small or corrupted.';
      }

      return null; // No validation errors
    } catch (e) {
      developer.log('VideoUtils: Error validating video file: $e');
      return 'Error validating video file: $e';
    }
  }
}
