import 'package:flutter/material.dart';

/// Design Configuration for UI Designers
///
/// This file contains all the styling variables that designers can easily modify
/// to change the look and feel of the GameFlex app components.
///
/// Instructions for Designers:
/// 1. Modify colors, sizes, and spacing values below
/// 2. All components will automatically use these values
/// 3. Changes here will apply app-wide
/// 4. Test changes by running the app after modifications

class DesignConfig {
  // ==================== COLORS ====================

  /// Primary brand color - used for buttons, highlights, and accents
  static const Color primaryColor = Color(0xFF28F4C3);

  /// Secondary brand color - used for secondary actions and elements
  static const Color secondaryColor = Color(0xFF00D5FF);

  /// Background colors
  static const Color darkBackground = Color(0xFF2A3642);
  static const Color cardBackground = Color(0xFF1A2530);
  static const Color overlayBackground = Color(0x992A3642); // 60% opacity

  /// Text colors
  static const Color primaryText = Color(0xFFF4F4F4);
  static const Color secondaryText = Color(0x80F4F4F4); // 50% opacity
  static const Color lightText = Color(0xFF8FAABD);

  /// Border and divider colors
  static const Color borderColor = Color(0xFF3A4A5A);
  static const Color dividerColor = Color(0xFF3A4A5A);

  /// Status colors
  static const Color successColor = Color(0xFF28F4C3);
  static const Color errorColor = Color(0xFFFF4444);
  static const Color warningColor = Color(0xFFF2DE76);

  // ==================== TYPOGRAPHY ====================

  /// Font sizes for different text elements
  static const double headingFontSize = 24.0;
  static const double titleFontSize = 18.0;
  static const double bodyFontSize = 14.0;
  static const double captionFontSize = 12.0;
  static const double smallFontSize = 10.0;

  /// Font weights
  static const FontWeight boldWeight = FontWeight.w700;
  static const FontWeight semiBoldWeight = FontWeight.w600;
  static const FontWeight mediumWeight = FontWeight.w500;
  static const FontWeight normalWeight = FontWeight.w400;

  // ==================== SPACING ====================

  /// Standard spacing units (multiply by these for consistent spacing)
  static const double spaceUnit = 8.0;

  /// Common spacing values
  static const double spaceXS = spaceUnit * 0.5; // 4px
  static const double spaceSM = spaceUnit * 1.0; // 8px
  static const double spaceMD = spaceUnit * 2.0; // 16px
  static const double spaceLG = spaceUnit * 3.0; // 24px
  static const double spaceXL = spaceUnit * 4.0; // 32px
  static const double spaceXXL = spaceUnit * 6.0; // 48px

  // ==================== BORDER RADIUS ====================

  /// Border radius values for different elements
  static const double radiusXS = 4.0;
  static const double radiusSM = 8.0;
  static const double radiusMD = 12.0;
  static const double radiusLG = 16.0;
  static const double radiusXL = 20.0;
  static const double radiusRound = 50.0; // For circular elements

  // ==================== COMPONENT SIZES ====================

  /// Button dimensions
  static const double buttonHeight = 48.0;
  static const double buttonHeightSmall = 36.0;
  static const double buttonHeightLarge = 56.0;

  /// Icon sizes
  static const double iconSizeXS = 16.0;
  static const double iconSizeSM = 20.0;
  static const double iconSizeMD = 24.0;
  static const double iconSizeLG = 32.0;
  static const double iconSizeXL = 48.0;

  /// Avatar sizes
  static const double avatarSizeSmall = 32.0;
  static const double avatarSizeMedium = 48.0;
  static const double avatarSizeLarge = 64.0;

  /// Card dimensions
  static const double cardElevation = 4.0;
  static const double cardPadding = spaceMD;

  // ==================== ANIMATION ====================

  /// Animation durations
  static const Duration animationFast = Duration(milliseconds: 150);
  static const Duration animationNormal = Duration(milliseconds: 300);
  static const Duration animationSlow = Duration(milliseconds: 500);

  /// Animation curves
  static const Curve animationCurve = Curves.easeInOut;
  static const Curve animationCurveIn = Curves.easeIn;
  static const Curve animationCurveOut = Curves.easeOut;

  // ==================== SHADOWS ====================

  /// Box shadow configurations
  static List<BoxShadow> get cardShadow => [
    BoxShadow(
      color: Colors.black.withValues(alpha: 0.1),
      blurRadius: 8.0,
      offset: const Offset(0, 2),
    ),
  ];

  static List<BoxShadow> get buttonShadow => [
    BoxShadow(
      color: primaryColor.withValues(alpha: 0.3),
      blurRadius: 8.0,
      offset: const Offset(0, 4),
    ),
  ];

  // ==================== GRADIENTS ====================

  /// Common gradient configurations
  static LinearGradient get primaryGradient => const LinearGradient(
    begin: Alignment.topLeft,
    end: Alignment.bottomRight,
    colors: [Color(0xFF28F4C3), Color(0xFF00D5FF)],
  );

  static LinearGradient get backgroundGradient => const LinearGradient(
    begin: Alignment.topCenter,
    end: Alignment.bottomCenter,
    colors: [Color(0xFF2A3642), Color(0xFF1A2530)],
  );

  // ==================== COMPONENT-SPECIFIC CONFIGS ====================

  /// Post Card Configuration
  static const double postCardImageHeight = 250.0;
  static const double postCardPadding = spaceMD;
  static const double postCardBorderRadius = radiusMD;

  /// Navigation Configuration
  static const double bottomNavHeight = 60.0;
  static const double bottomNavIconSize = iconSizeLG;

  /// Feed Configuration
  static const double feedItemSpacing = spaceMD;
  static const double feedOverlayPadding = spaceMD;

  /// Channel Configuration
  static const double channelCardHeight = 120.0;
  static const double channelCardPadding = spaceMD;

  /// Reaction Configuration
  static const double reactionChipHeight = 32.0;
  static const double reactionChipPadding = spaceSM;
  static const double reactionIconSize = iconSizeSM;

  // ==================== HELPER METHODS ====================

  /// Get text style for different text types
  static TextStyle getTextStyle({
    required double fontSize,
    FontWeight? fontWeight,
    Color? color,
    double? height,
  }) {
    return TextStyle(
      fontSize: fontSize,
      fontWeight: fontWeight ?? normalWeight,
      color: color ?? primaryText,
      height: height,
    );
  }

  /// Get heading text style
  static TextStyle get headingStyle =>
      getTextStyle(fontSize: headingFontSize, fontWeight: boldWeight);

  /// Get title text style
  static TextStyle get titleStyle =>
      getTextStyle(fontSize: titleFontSize, fontWeight: semiBoldWeight);

  /// Get body text style
  static TextStyle get bodyStyle =>
      getTextStyle(fontSize: bodyFontSize, fontWeight: normalWeight);

  /// Get caption text style
  static TextStyle get captionStyle => getTextStyle(
    fontSize: captionFontSize,
    fontWeight: normalWeight,
    color: secondaryText,
  );

  /// Get button decoration
  static BoxDecoration get buttonDecoration => BoxDecoration(
    color: primaryColor,
    borderRadius: BorderRadius.circular(radiusMD),
    boxShadow: buttonShadow,
  );

  /// Get card decoration
  static BoxDecoration get cardDecoration => BoxDecoration(
    color: cardBackground,
    borderRadius: BorderRadius.circular(radiusMD),
    boxShadow: cardShadow,
  );
}
