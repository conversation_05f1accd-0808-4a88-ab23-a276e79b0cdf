import 'package:flutter/foundation.dart';
import 'dart:developer' as developer;
import '../models/channel_model.dart';
import '../models/post_model.dart';
import '../services/aws_channels_service.dart';
import '../utils/app_logger.dart';

enum ChannelsStatus { initial, loading, loaded, refreshing, error }

class ChannelsProvider with ChangeNotifier {
  final AwsChannelsService _channelsService = AwsChannelsService.instance;

  // Channels list state
  List<ChannelModel> _channels = [];
  ChannelsStatus _status = ChannelsStatus.initial;
  String? _error;
  bool _hasMore = true;
  String? _nextOffset;
  bool _isLoadingMore = false;

  // Channel posts state
  List<PostModel> _channelPosts = [];
  String? _currentChannelId;
  bool _isLoadingPosts = false;
  bool _hasMorePosts = true;
  String? _channelPostsError;

  // Getters
  List<ChannelModel> get channels => _channels;
  ChannelsStatus get status => _status;
  String? get error => _error;
  bool get hasMore => _hasMore;
  bool get isLoading => _status == ChannelsStatus.loading;
  bool get isLoadingMore => _isLoadingMore;

  List<PostModel> get channelPosts => _channelPosts;
  String? get currentChannelId => _currentChannelId;
  bool get isLoadingPosts => _isLoadingPosts;
  bool get hasMorePosts => _hasMorePosts;

  // Additional getters for compatibility
  String? get errorMessage => _error;
  bool get isRefreshing => _status == ChannelsStatus.refreshing;
  bool get channelPostsLoading => _isLoadingPosts;
  String? get channelPostsError => _channelPostsError;

  // Private setters
  void _setStatus(ChannelsStatus status) {
    _status = status;
    notifyListeners();
  }

  void _setError(String error) {
    _error = error;
    _setStatus(ChannelsStatus.error);
  }

  /// Load channels (initial load)
  Future<void> loadChannels() async {
    if (_status == ChannelsStatus.loading) return;

    AppLogger.channel('loadChannels: Setting status to loading');
    developer.log('ChannelsProvider: Starting to load channels');
    _setStatus(ChannelsStatus.loading);
    _nextOffset = null;
    _hasMore = true;

    try {
      AppLogger.channel(
        'loadChannels: About to call AwsChannelsService.getChannels()',
      );
      developer.log(
        'ChannelsProvider: Calling AwsChannelsService.getChannels()',
      );
      final channels = await _channelsService.getChannels(
        limit: 20,
        nextOffset: null,
      );
      AppLogger.channel(
        'loadChannels: Received ${channels.length} channels from service',
      );

      developer.log('ChannelsProvider: Loaded ${channels.length} channels');
      _channels = channels;
      _hasMore = channels.length == 20; // Assume more if we got a full page
      _setStatus(ChannelsStatus.loaded);
    } catch (e, stackTrace) {
      developer.log(
        'ChannelsProvider: Error loading channels',
        error: e,
        stackTrace: stackTrace,
      );
      AppLogger.error(
        'ChannelsProvider LOAD ERROR',
        error: e,
        stackTrace: stackTrace,
      );
      _setError('Failed to load channels: $e');
    }
  }

  /// Load more channels (pagination)
  Future<void> loadMoreChannels() async {
    if (_isLoadingMore || !_hasMore) return;

    _isLoadingMore = true;
    notifyListeners();

    try {
      final moreChannels = await _channelsService.getChannels(
        limit: 20,
        nextOffset: _nextOffset,
      );

      developer.log(
        'ChannelsProvider: Loaded ${moreChannels.length} more channels',
      );
      _channels.addAll(moreChannels);
      _hasMore = moreChannels.length == 20;
      _isLoadingMore = false;
      notifyListeners();
    } catch (e, stackTrace) {
      developer.log(
        'ChannelsProvider: Error loading more channels',
        error: e,
        stackTrace: stackTrace,
      );
      _isLoadingMore = false;
      _setError('Failed to load more channels: $e');
    }
  }

  /// Refresh channels
  Future<void> refreshChannels() async {
    if (_status == ChannelsStatus.refreshing) return;

    _setStatus(ChannelsStatus.refreshing);
    _nextOffset = null;
    _hasMore = true;

    try {
      final channels = await _channelsService.getChannels(
        limit: 20,
        nextOffset: null,
      );

      developer.log(
        'ChannelsProvider: Refreshed with ${channels.length} channels',
      );
      _channels = channels;
      _hasMore = channels.length == 20;
      _setStatus(ChannelsStatus.loaded);
    } catch (e, stackTrace) {
      developer.log(
        'ChannelsProvider: Error refreshing channels',
        error: e,
        stackTrace: stackTrace,
      );
      AppLogger.error(
        'ChannelsProvider REFRESH ERROR',
        error: e,
        stackTrace: stackTrace,
      );
      _setError('Failed to refresh channels: $e');
    }
  }

  /// Create a new channel
  Future<ChannelModel?> createChannel({
    required String name,
    String? description,
    bool isPublic = true,
  }) async {
    try {
      developer.log('ChannelsProvider: Creating channel: $name');

      final channel = await _channelsService.createChannel(
        name: name,
        description: description,
        isPublic: isPublic,
      );

      // Add to the beginning of the list
      _channels.insert(0, channel);
      notifyListeners();

      developer.log(
        'ChannelsProvider: Successfully created channel: ${channel.name}',
      );
      return channel;
    } catch (e, stackTrace) {
      developer.log(
        'ChannelsProvider: Error creating channel',
        error: e,
        stackTrace: stackTrace,
      );
      AppLogger.error('ChannelsProvider CREATE ERROR', error: e);
      rethrow;
    }
  }

  /// Join a channel
  Future<bool> joinChannel(String channelId) async {
    try {
      developer.log('ChannelsProvider: Joining channel: $channelId');

      await _channelsService.joinChannel(channelId);

      // Update the channel in the list
      final channelIndex = _channels.indexWhere((c) => c.id == channelId);
      if (channelIndex != -1) {
        _channels[channelIndex] = _channels[channelIndex].copyWith(
          isUserMember: true,
          userRole: 'member',
          memberCount: _channels[channelIndex].memberCount + 1,
        );
        notifyListeners();
      }

      developer.log(
        'ChannelsProvider: Successfully joined channel: $channelId',
      );
      return true;
    } catch (e, stackTrace) {
      developer.log(
        'ChannelsProvider: Error joining channel',
        error: e,
        stackTrace: stackTrace,
      );
      return false;
    }
  }

  /// Leave a channel
  Future<bool> leaveChannel(String channelId) async {
    try {
      developer.log('ChannelsProvider: Leaving channel: $channelId');

      await _channelsService.leaveChannel(channelId);

      // Update the channel in the list
      final channelIndex = _channels.indexWhere((c) => c.id == channelId);
      if (channelIndex != -1) {
        _channels[channelIndex] = _channels[channelIndex].copyWith(
          isUserMember: false,
          userRole: null,
          memberCount: _channels[channelIndex].memberCount - 1,
        );
        notifyListeners();
      }

      developer.log('ChannelsProvider: Successfully left channel: $channelId');
      return true;
    } catch (e, stackTrace) {
      developer.log(
        'ChannelsProvider: Error leaving channel',
        error: e,
        stackTrace: stackTrace,
      );
      return false;
    }
  }

  /// Load posts for a specific channel
  Future<void> loadChannelPosts(String channelId) async {
    if (_isLoadingPosts) return;

    _isLoadingPosts = true;
    _currentChannelId = channelId;
    _hasMorePosts = true;
    _channelPostsError = null; // Clear any previous error
    notifyListeners();

    try {
      final posts = await _channelsService.getChannelPosts(
        channelId: channelId,
        limit: 20,
        offset: 0,
      );

      developer.log(
        'ChannelsProvider: Loaded ${posts.length} posts for channel: $channelId',
      );
      _channelPosts = posts;
      _hasMorePosts = posts.length == 20;
      _isLoadingPosts = false;
      _channelPostsError = null;
      notifyListeners();
    } catch (e, stackTrace) {
      developer.log(
        'ChannelsProvider: Error loading channel posts',
        error: e,
        stackTrace: stackTrace,
      );
      _isLoadingPosts = false;
      _channelPostsError = e.toString();
      notifyListeners();
    }
  }

  /// Select a channel (for compatibility with existing code)
  void selectChannel(ChannelModel channel) {
    // This method is for compatibility - just load the channel posts
    loadChannelPosts(channel.id);
  }

  /// Clear channel posts (when leaving channel detail)
  void clearChannelPosts() {
    _channelPosts = [];
    _currentChannelId = null;
    _isLoadingPosts = false;
    _hasMorePosts = true;
    _channelPostsError = null;
    notifyListeners();
  }
}
