import 'dart:async';
import 'dart:developer' as developer;
import 'package:flutter/foundation.dart';
import '../models/user_model.dart';
import '../services/aws_auth_service.dart' as aws;
import '../services/apple_signin_service.dart';
import '../services/xbox_auth_service.dart';
import '../services/newrelic_service.dart';
import '../utils/app_logger.dart';

enum AuthStatus {
  initial,
  loading,
  authenticated,
  unauthenticated,
  usernameRequired,
  error,
}

class AuthProvider extends ChangeNotifier {
  AuthStatus _status = AuthStatus.initial;
  UserModel? _user;
  String? _errorMessage;
  StreamSubscription<aws.AuthState>? _authSubscription;
  Map<String, dynamic>? _accountChoiceData;

  AuthStatus get status => _status;
  UserModel? get user => _user;
  String? get errorMessage => _errorMessage;
  Map<String, dynamic>? get accountChoiceData => _accountChoiceData;
  bool get isAuthenticated =>
      _status == AuthStatus.authenticated && _user != null;
  bool get requiresUsername => _status == AuthStatus.usernameRequired;
  bool get isLoading => _status == AuthStatus.loading;

  AuthProvider() {
    _initializeAuth();
  }

  /// Initialize authentication state
  Future<void> _initializeAuth() async {
    _setStatus(AuthStatus.loading);

    try {
      // Try to restore session from stored tokens
      final sessionRestored =
          await aws.AwsAuthService.instance.restoreSession();

      if (sessionRestored && aws.AwsAuthService.instance.isAuthenticated) {
        final awsUser = aws.AwsAuthService.instance.currentUser;
        if (awsUser != null) {
          _user = UserModel.fromAwsUser(awsUser);
          _setStatus(AuthStatus.authenticated);

          // Set user ID for New Relic session tracking on session restore
          if (_user?.id != null) {
            NewRelicService.instance.setUserId(_user!.id);
            AppLogger.auth(
              'New Relic User ID set on session restore: ${_user!.id}',
            );
          }
        } else {
          _setStatus(AuthStatus.unauthenticated);
        }
      } else {
        _setStatus(AuthStatus.unauthenticated);
      }

      // Listen to auth state changes
      _authSubscription = aws.AwsAuthService.instance.authStateChanges.listen(
        _onAuthStateChange,
        onError: (error) {
          _setError('Authentication error: $error');
        },
      );
    } catch (e) {
      _setError('Failed to initialize authentication: $e');
    }
  }

  /// Handle auth state changes
  void _onAuthStateChange(aws.AuthState authState) {
    switch (authState) {
      case aws.AuthState.signedIn:
        final awsUser = aws.AwsAuthService.instance.currentUser;
        if (awsUser != null) {
          _user = UserModel.fromAwsUser(awsUser);
          // Check if username is required before setting to authenticated
          if (_user!.username == null || _user!.username!.isEmpty) {
            _setStatus(AuthStatus.usernameRequired);
          } else {
            _setStatus(AuthStatus.authenticated);

            // Set user ID for New Relic session tracking on auth state change
            if (_user?.id != null) {
              NewRelicService.instance.setUserId(_user!.id);
              AppLogger.auth(
                'New Relic User ID set on auth state change: ${_user!.id}',
              );
            }
          }
        }
        break;
      case aws.AuthState.signedOut:
        _user = null;
        _setStatus(AuthStatus.unauthenticated);

        // Clear user ID from New Relic on sign out
        NewRelicService.instance.clearUserId();
        NewRelicService.instance.recordCustomEvent('user_signout', {
          'timestamp': DateTime.now().millisecondsSinceEpoch,
        });
        break;
    }
  }

  /// Sign up with email and password
  Future<bool> signUp({required String email, required String password}) async {
    developer.log('AuthProvider: Starting sign up process for email: $email');
    AppLogger.auth('Starting sign up process for email: $email');

    _setStatus(AuthStatus.loading);

    try {
      final response = await aws.AwsAuthService.instance.signUp(
        email: email,
        password: password,
      );

      developer.log(
        'AuthProvider: Sign up response received - Success: ${response.success}',
      );
      AppLogger.auth('Sign up response - Success: ${response.success}');
      AppLogger.auth('Message: ${response.message}');

      if (response.success && response.user != null) {
        developer.log('AuthProvider: Sign up completed successfully');
        AppLogger.auth('Sign up completed successfully');

        // Automatically sign in the user after successful signup
        developer.log(
          'AuthProvider: Attempting automatic sign in after signup',
        );
        AppLogger.auth('Attempting automatic sign in after signup');

        final signInResponse = await aws.AwsAuthService.instance.signIn(
          email: email,
          password: password,
        );

        if (signInResponse.success && signInResponse.user != null) {
          _user = UserModel.fromAwsUser(signInResponse.user!);

          // Check if username is required
          if (signInResponse.requiresUsername == true) {
            _setStatus(AuthStatus.usernameRequired);
            developer.log(
              'AuthProvider: Sign in successful but username required',
            );
            AppLogger.auth('Sign in successful but username required');
          } else {
            _setStatus(AuthStatus.authenticated);
            developer.log(
              'AuthProvider: Automatic sign in after signup successful',
            );
            AppLogger.auth('Automatic sign in after signup successful');

            // Set user ID for New Relic session tracking after signup
            if (_user?.id != null) {
              NewRelicService.instance.setUserId(_user!.id);
              AppLogger.auth(
                'New Relic User ID set after signup: ${_user!.id}',
              );
            }
          }
          return true;
        } else {
          // Signup succeeded but auto sign-in failed, user needs to manually sign in
          developer.log(
            'AuthProvider: Signup successful but auto sign-in failed: ${signInResponse.message}',
          );
          AppLogger.auth(
            'Signup successful but auto sign-in failed: ${signInResponse.message}',
          );
          _setStatus(AuthStatus.unauthenticated);
          return true;
        }
      } else {
        final errorMsg = response.message;
        developer.log('AuthProvider: $errorMsg');
        AppLogger.auth('Sign up error: $errorMsg');
        _setErrorWithoutStatusChange(errorMsg);
        return false;
      }
    } catch (e, stackTrace) {
      final errorMsg = 'Sign up failed: $e';
      developer.log(
        'AuthProvider: Error during sign up',
        error: e,
        stackTrace: stackTrace,
      );
      AppLogger.error(
        'AuthProvider: SIGN UP ERROR',
        error: e,
        stackTrace: stackTrace,
      );
      _setErrorWithoutStatusChange(errorMsg);
      return false;
    }
  }

  /// Sign in with email and password
  Future<bool> signIn({required String email, required String password}) async {
    developer.log('AuthProvider: Starting sign in process for email: $email');
    AppLogger.auth('Starting sign in process for email: $email');

    _setStatus(AuthStatus.loading);

    try {
      final response = await aws.AwsAuthService.instance.signIn(
        email: email,
        password: password,
      );

      developer.log(
        'AuthProvider: Sign in response received - Success: ${response.success}',
      );
      AppLogger.auth('Sign in response - Success: ${response.success}');
      AppLogger.auth('Message: ${response.message}');

      if (response.success && response.user != null) {
        _user = UserModel.fromAwsUser(response.user!);

        // Check if username is required
        if (response.requiresUsername == true) {
          _setStatus(AuthStatus.usernameRequired);
          developer.log(
            'AuthProvider: Sign in successful but username required',
          );
          AppLogger.auth('Sign in successful but username required');
        } else {
          _setStatus(AuthStatus.authenticated);
          developer.log('AuthProvider: Sign in completed successfully');
          AppLogger.auth('Sign in completed successfully');

          // Track successful login in New Relic
          NewRelicService.instance.recordCustomEvent('user_authentication', {
            'method': 'email_password',
            'success': true,
            'user_id': _user?.id ?? 'unknown',
          });

          // Set user ID for New Relic session tracking
          if (_user?.id != null) {
            NewRelicService.instance.setUserId(_user!.id);
          }
        }
        return true;
      } else {
        final errorMsg = response.message;
        developer.log('AuthProvider: $errorMsg');
        AppLogger.auth('Sign in error: $errorMsg');
        _setErrorWithoutStatusChange(errorMsg);
        return false;
      }
    } catch (e, stackTrace) {
      final errorMsg = 'Sign in failed: $e';
      developer.log(
        'AuthProvider: Error during sign in',
        error: e,
        stackTrace: stackTrace,
      );
      AppLogger.error(
        'AuthProvider: SIGN IN ERROR',
        error: e,
        stackTrace: stackTrace,
      );
      _setErrorWithoutStatusChange(errorMsg);
      return false;
    }
  }

  /// Sign out
  Future<void> signOut() async {
    _setStatus(AuthStatus.loading);

    try {
      await aws.AwsAuthService.instance.signOut();
      _user = null;
      _setStatus(AuthStatus.unauthenticated);

      // Clear user ID from New Relic on manual sign out
      NewRelicService.instance.clearUserId();
      NewRelicService.instance.recordCustomEvent('user_signout', {
        'method': 'manual',
        'timestamp': DateTime.now().millisecondsSinceEpoch,
      });
    } catch (e) {
      _setError('Sign out failed: $e');
    }
  }

  /// Sign in with Apple
  Future<bool> signInWithApple() async {
    _setStatus(AuthStatus.loading);

    try {
      developer.log('AuthProvider: Starting Apple Sign In');
      AppLogger.auth('Starting Apple Sign In');

      final response = await AppleSignInService.instance.signIn();

      if (response.success &&
          response.user != null &&
          response.tokens != null) {
        // Store the tokens and user data in AWS auth service
        await aws.AwsAuthService.instance.storeAppleAuthData(
          response.user!,
          response.tokens!.accessToken,
          response.tokens!.refreshToken,
          response.tokens!.idToken,
        );

        _user = response.user;

        // Check if username is required
        if (response.requiresUsername) {
          _setStatus(AuthStatus.usernameRequired);
        } else {
          _setStatus(AuthStatus.authenticated);
        }

        developer.log('AuthProvider: Apple Sign In successful');
        AppLogger.auth('Apple Sign In successful');

        // Track successful Apple login in New Relic
        NewRelicService.instance.recordCustomEvent('user_authentication', {
          'method': 'apple_signin',
          'success': true,
          'user_id': _user?.id ?? 'unknown',
        });

        // Set user ID for New Relic session tracking
        if (_user?.id != null) {
          NewRelicService.instance.setUserId(_user!.id);
        }
        return true;
      } else {
        _setError(response.message);
        return false;
      }
    } catch (e, stackTrace) {
      final errorMsg = 'Apple Sign In failed: $e';
      developer.log(
        'AuthProvider: Error during Apple Sign In',
        error: e,
        stackTrace: stackTrace,
      );
      AppLogger.error(
        'AuthProvider: APPLE SIGN IN ERROR',
        error: e,
        stackTrace: stackTrace,
      );
      _setErrorWithoutStatusChange(errorMsg);
      return false;
    }
  }

  /// Sign in with Xbox
  Future<bool> signInWithXbox() async {
    _setStatus(AuthStatus.loading);

    try {
      developer.log('AuthProvider: Starting Xbox Sign In');
      AppLogger.auth('Starting Xbox Sign In');

      final response = await XboxAuthService.instance.signInWithXbox();

      if (response.success &&
          response.user != null &&
          response.tokens != null) {
        // Store the tokens and user data in AWS auth service
        await aws.AwsAuthService.instance.storeXboxAuthData(
          response.user!,
          response.tokens!.accessToken,
          response.tokens!.refreshToken,
          response.tokens!.idToken,
        );

        _user = response.user;

        // Check if username is required
        if (response.requiresUsername) {
          _setStatus(AuthStatus.usernameRequired);
        } else {
          _setStatus(AuthStatus.authenticated);
        }

        developer.log('AuthProvider: Xbox Sign In successful');
        AppLogger.auth('Xbox Sign In successful');

        // Track successful Xbox login in New Relic
        NewRelicService.instance.recordCustomEvent('user_authentication', {
          'method': 'xbox_signin',
          'success': true,
          'user_id': _user?.id ?? 'unknown',
        });

        // Set user ID for New Relic session tracking
        if (_user?.id != null) {
          NewRelicService.instance.setUserId(_user!.id);
        }
        return true;
      } else if (response.requiresAccountChoice) {
        // Account choice required - navigate to account choice screen
        _setStatus(AuthStatus.unauthenticated);

        // Store the account choice data for navigation
        _accountChoiceData = {
          'accountOptions': response.accountOptions,
          'xboxData': response.xboxData,
        };

        developer.log('AuthProvider: Xbox account choice required');
        AppLogger.auth('Xbox account choice required');

        return true; // Return true to indicate we should navigate to account choice
      } else {
        _setError(response.message);
        return false;
      }
    } catch (e, stackTrace) {
      final errorMsg = 'Xbox Sign In failed: $e';
      developer.log(
        'AuthProvider: Error during Xbox Sign In',
        error: e,
        stackTrace: stackTrace,
      );
      AppLogger.error(
        'AuthProvider: XBOX SIGN IN ERROR',
        error: e,
        stackTrace: stackTrace,
      );
      _setErrorWithoutStatusChange(errorMsg);
      return false;
    }
  }

  /// Reset password (not implemented in AWS backend yet)
  Future<bool> resetPassword(String email) async {
    _setError('Password reset is not implemented yet');
    return false;
  }

  /// Change password for the current user (not implemented in AWS backend yet)
  Future<bool> changePassword(String newPassword) async {
    _setError('Password change is not implemented yet');
    return false;
  }

  /// Clear error message
  void clearError() {
    _errorMessage = null;
    notifyListeners();
  }

  /// Set status and notify listeners
  void _setStatus(AuthStatus status) {
    _status = status;
    if (status != AuthStatus.error) {
      _errorMessage = null;
    }
    notifyListeners();
  }

  /// Set error and notify listeners
  void _setError(String error) {
    _errorMessage = error;
    _status = AuthStatus.error;
    notifyListeners();
  }

  /// Set error without changing status (for login/signup failures)
  /// Set user from Xbox account creation
  void setUserFromXboxCreation(UserModel user) {
    _user = user;
    _setStatus(AuthStatus.usernameRequired);
    developer.log('AuthProvider: User set from Xbox account creation');
    AppLogger.auth('User set from Xbox account creation');
  }

  /// Set username for authenticated user
  Future<bool> setUsername({required String username}) async {
    developer.log('AuthProvider: Setting username: $username');
    AppLogger.auth('Setting username: $username');

    _setStatus(AuthStatus.loading);

    try {
      final response = await aws.AwsAuthService.instance.setUsername(
        username: username,
      );

      developer.log(
        'AuthProvider: Set username response received - Success: ${response.success}',
      );
      AppLogger.auth('Set username response - Success: ${response.success}');
      AppLogger.auth('Message: ${response.message}');

      if (response.success && response.user != null) {
        _user = UserModel.fromAwsUser(response.user!);
        _setStatus(AuthStatus.authenticated);

        developer.log('AuthProvider: Username set successfully');
        AppLogger.auth('Username set successfully');

        // Set user ID for New Relic session tracking after username completion
        if (_user?.id != null) {
          NewRelicService.instance.setUserId(_user!.id);
          AppLogger.auth(
            'New Relic User ID set after username completion: ${_user!.id}',
          );
        }

        return true;
      } else {
        final errorMsg = response.message;
        developer.log('AuthProvider: $errorMsg');
        AppLogger.auth('Set username error: $errorMsg');
        _setErrorWithoutStatusChange(errorMsg);
        return false;
      }
    } catch (e, stackTrace) {
      final errorMsg = 'Set username failed: $e';
      developer.log(
        'AuthProvider: Error during set username',
        error: e,
        stackTrace: stackTrace,
      );
      AppLogger.error(
        'AuthProvider: SET USERNAME ERROR',
        error: e,
        stackTrace: stackTrace,
      );
      _setErrorWithoutStatusChange(errorMsg);
      return false;
    }
  }

  void _setErrorWithoutStatusChange(String error) {
    _errorMessage = error;
    // Reset loading status back to unauthenticated if we're currently loading
    if (_status == AuthStatus.loading) {
      _status = AuthStatus.unauthenticated;
    }
    notifyListeners();
  }

  @override
  void dispose() {
    _authSubscription?.cancel();
    super.dispose();
  }
}
