import 'package:flutter/foundation.dart';
import 'dart:developer' as developer;
import '../models/post_model.dart';
import '../services/aws_posts_service.dart';
import '../services/aws_auth_service.dart';
import '../utils/app_logger.dart';

enum FollowedPostsStatus { initial, loading, loaded, refreshing, error }

class FollowedPostsProvider with ChangeNotifier {
  List<PostModel> _posts = [];
  FollowedPostsStatus _status = FollowedPostsStatus.initial;
  String? _error;
  int _currentOffset = 0;
  bool _hasMore = true;
  int _currentPostIndex = 0;
  final int _pageSize = 20;

  // Getters
  List<PostModel> get posts => _posts;
  FollowedPostsStatus get status => _status;
  String? get error => _error;
  bool get hasMore => _hasMore;
  int get currentPostIndex => _currentPostIndex;

  /// Load followed posts
  Future<void> loadPosts() async {
    if (_status == FollowedPostsStatus.loading) return;

    // Check if user is authenticated before loading posts
    if (!AwsAuthService.instance.isAuthenticated) {
      AppLogger.provider(
        'loadFollowedPosts: User not authenticated, skipping load',
      );
      _setError('User not authenticated. Please sign in to view posts.');
      return;
    }

    AppLogger.provider('loadFollowedPosts: Setting status to loading');
    developer.log('FollowedPostsProvider: Starting to load followed posts');
    _setStatus(FollowedPostsStatus.loading);
    _currentOffset = 0;
    _hasMore = true;

    try {
      AppLogger.provider(
        'loadFollowedPosts: About to call AwsPostsService.getFollowedPosts()',
      );
      developer.log(
        'FollowedPostsProvider: Calling AwsPostsService.getFollowedPosts()',
      );
      final posts = await AwsPostsService.instance.getFollowedPosts(
        limit: _pageSize,
        offset: _currentOffset,
      );

      AppLogger.provider(
        'loadFollowedPosts: Received ${posts.length} posts from service',
      );
      developer.log(
        'FollowedPostsProvider: Loaded ${posts.length} followed posts',
      );

      _posts = posts;
      _currentOffset = posts.length;
      _hasMore = posts.length == _pageSize;
      _currentPostIndex = 0; // Reset to first post
      _setStatus(FollowedPostsStatus.loaded);

      AppLogger.provider(
        'loadFollowedPosts: Successfully loaded ${_posts.length} posts',
      );
    } catch (e, stackTrace) {
      developer.log(
        'FollowedPostsProvider: Error loading followed posts',
        error: e,
        stackTrace: stackTrace,
      );
      AppLogger.error(
        'FollowedPostsProvider ERROR',
        error: e,
        stackTrace: stackTrace,
      );
      _setError('Failed to load followed posts: $e');
    }
  }

  /// Reset to top of feed immediately (for instant scroll response)
  void resetToTop() {
    AppLogger.provider(
      'resetToTop: Immediately scrolling to top of followed posts',
    );
    _currentPostIndex = 0;
    notifyListeners();
  }

  /// Refresh followed posts (pull to refresh) - optimized for better UX
  Future<void> refreshPosts() async {
    if (_status == FollowedPostsStatus.refreshing) return;

    // Check if user is authenticated before refreshing posts
    if (!AwsAuthService.instance.isAuthenticated) {
      AppLogger.provider(
        'refreshFollowedPosts: User not authenticated, skipping refresh',
      );
      _setError('User not authenticated. Please sign in to view posts.');
      return;
    }

    developer.log('FollowedPostsProvider: Starting to refresh followed posts');
    AppLogger.provider(
      'REFRESH TRIGGERED - Background refresh for latest followed content',
    );

    // Don't change status to refreshing immediately - keep current posts visible
    // Only set refreshing if we have no posts to show
    if (_posts.isEmpty) {
      _setStatus(FollowedPostsStatus.refreshing);
    }

    _currentOffset = 0;
    _hasMore = true;

    try {
      final posts = await AwsPostsService.instance.getFollowedPosts(
        limit: _pageSize,
        offset: 0,
      );

      developer.log(
        'FollowedPostsProvider: Refreshed with ${posts.length} followed posts',
      );
      _posts = posts;
      _currentOffset = posts.length;
      _hasMore = posts.length == _pageSize;
      _currentPostIndex = 0; // Reset to first post on refresh
      _setStatus(FollowedPostsStatus.loaded);

      AppLogger.provider(
        'FOLLOWED REFRESH COMPLETED - ${posts.length} posts loaded',
      );
    } catch (e, stackTrace) {
      developer.log(
        'FollowedPostsProvider: Error refreshing followed posts',
        error: e,
        stackTrace: stackTrace,
      );
      AppLogger.error(
        'FollowedPostsProvider REFRESH ERROR',
        error: e,
        stackTrace: stackTrace,
      );

      // Only show error if we have no posts to fall back to
      if (_posts.isEmpty) {
        _setError('Failed to refresh followed posts: $e');
      } else {
        // Keep existing posts and just log the error
        AppLogger.provider(
          'Followed refresh failed but keeping existing posts',
        );
      }
    }
  }

  /// Load more followed posts (pagination)
  Future<void> loadMorePosts() async {
    if (!_hasMore || _status == FollowedPostsStatus.loading) return;

    // Check if user is authenticated before loading more posts
    if (!AwsAuthService.instance.isAuthenticated) {
      AppLogger.provider(
        'loadMoreFollowedPosts: User not authenticated, skipping load',
      );
      return;
    }

    try {
      final morePosts = await AwsPostsService.instance.getFollowedPosts(
        limit: _pageSize,
        offset: _currentOffset,
      );

      if (morePosts.isNotEmpty) {
        _posts.addAll(morePosts);
        _currentOffset += morePosts.length;
        _hasMore = morePosts.length == _pageSize;
        notifyListeners();
      } else {
        _hasMore = false;
        notifyListeners();
      }
    } catch (e) {
      _setError('Failed to load more followed posts: $e');
    }
  }

  /// Set current post index for state persistence
  void setCurrentPostIndex(int index) {
    if (index >= 0 && index < _posts.length) {
      _currentPostIndex = index;
      // Don't notify listeners for index changes to avoid rebuilds
    }
  }

  /// Clear all posts and reset state
  void clearPosts() {
    _posts.clear();
    _currentOffset = 0;
    _hasMore = true;
    _currentPostIndex = 0;
    _setStatus(FollowedPostsStatus.initial);
    _error = null;
    notifyListeners();
  }

  /// Add a new post to the beginning of the list
  void addPost(PostModel post) {
    _posts.insert(0, post);
    _currentOffset++;
    notifyListeners();
  }

  /// Update an existing post
  void updatePost(PostModel updatedPost) {
    final index = _posts.indexWhere((post) => post.id == updatedPost.id);
    if (index != -1) {
      _posts[index] = updatedPost;
      notifyListeners();
    }
  }

  /// Remove a post
  void removePost(String postId) {
    _posts.removeWhere((post) => post.id == postId);
    _currentOffset = _posts.length;
    notifyListeners();
  }

  /// Private helper methods
  void _setStatus(FollowedPostsStatus status) {
    _status = status;
    if (status != FollowedPostsStatus.error) {
      _error = null;
    }
    notifyListeners();
  }

  void _setError(String error) {
    _error = error;
    _status = FollowedPostsStatus.error;
    notifyListeners();
  }
}
