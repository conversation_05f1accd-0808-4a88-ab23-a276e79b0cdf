import 'package:flutter/material.dart';
import '../models/reflex_model.dart';
import '../services/aws_reflex_service.dart';
import '../utils/app_logger.dart';

enum ReflexStatus { initial, loading, loaded, error }

class ReflexProvider extends ChangeNotifier {
  final AwsReflexService _reflexService = AwsReflexService.instance;

  // State for all reflexes (not used anymore - reflexes are post-specific)
  final List<ReflexModel> _reflexes = [];
  ReflexStatus _status = ReflexStatus.initial;
  String? _error;

  // State for post-specific reflexes
  final Map<String, List<ReflexModel>> _postReflexes = {};
  final Map<String, ReflexStatus> _postReflexStatus = {};
  final Map<String, String?> _postReflexErrors = {};

  // Getters for all reflexes (not used anymore)
  List<ReflexModel> get reflexes => _reflexes;
  ReflexStatus get status => _status;
  String? get error => _error;
  bool get isLoading => _status == ReflexStatus.loading;

  // Getters for post-specific reflexes
  List<ReflexModel> getReflexesForPost(String postId) {
    return _postReflexes[postId] ?? [];
  }

  ReflexStatus getPostReflexStatus(String postId) {
    return _postReflexStatus[postId] ?? ReflexStatus.initial;
  }

  String? getPostReflexError(String postId) {
    return _postReflexErrors[postId];
  }

  bool isPostReflexLoading(String postId) {
    return getPostReflexStatus(postId) == ReflexStatus.loading;
  }

  /// Load reflexes for a specific post
  Future<void> loadReflexesForPost(String postId) async {
    if (_postReflexStatus[postId] == ReflexStatus.loading) return;

    _postReflexStatus[postId] = ReflexStatus.loading;
    _postReflexErrors[postId] = null;
    notifyListeners();

    try {
      AppLogger.debug('ReflexProvider: Loading reflexes for post $postId');

      final reflexes = await _reflexService.getReflexesForPost(postId);
      _postReflexes[postId] = reflexes;
      _postReflexStatus[postId] = ReflexStatus.loaded;

      AppLogger.debug(
        'ReflexProvider: Loaded ${reflexes.length} reflexes for post $postId',
      );
    } catch (e, stackTrace) {
      _postReflexStatus[postId] = ReflexStatus.error;
      _postReflexErrors[postId] = e.toString();
      AppLogger.error(
        'ReflexProvider: Error loading reflexes for post $postId',
        error: e,
        stackTrace: stackTrace,
      );
    }

    notifyListeners();
  }

  /// Create a new reflex
  Future<ReflexModel?> createReflex(CreateReflexRequest request) async {
    try {
      AppLogger.debug(
        'ReflexProvider: Creating reflex for post ${request.postId}',
      );

      final reflex = await _reflexService.createReflex(request);

      if (reflex != null) {
        // Add to post-specific reflexes
        if (_postReflexes[request.postId] != null) {
          _postReflexes[request.postId]!.insert(0, reflex);
        }

        // Add to all reflexes if it's loaded
        if (_status == ReflexStatus.loaded) {
          _reflexes.insert(0, reflex);
        }

        AppLogger.debug('ReflexProvider: Created reflex ${reflex.id}');
        notifyListeners();
      }

      return reflex;
    } catch (e, stackTrace) {
      AppLogger.error(
        'ReflexProvider: Error creating reflex',
        error: e,
        stackTrace: stackTrace,
      );
      rethrow;
    }
  }

  /// Delete a reflex
  Future<bool> deleteReflex(String reflexId) async {
    try {
      AppLogger.debug('ReflexProvider: Deleting reflex $reflexId');

      final success = await _reflexService.deleteReflex(reflexId);

      if (success) {
        // Remove from all reflexes
        _reflexes.removeWhere((reflex) => reflex.id == reflexId);

        // Remove from post-specific reflexes
        for (final postReflexes in _postReflexes.values) {
          postReflexes.removeWhere((reflex) => reflex.id == reflexId);
        }

        AppLogger.debug('ReflexProvider: Deleted reflex $reflexId');
        notifyListeners();
      }

      return success;
    } catch (e, stackTrace) {
      AppLogger.error(
        'ReflexProvider: Error deleting reflex $reflexId',
        error: e,
        stackTrace: stackTrace,
      );
      rethrow;
    }
  }

  /// Update a reflex
  Future<ReflexModel?> updateReflex(
    String reflexId,
    Map<String, dynamic> updates,
  ) async {
    try {
      AppLogger.debug('ReflexProvider: Updating reflex $reflexId');

      final updatedReflex = await _reflexService.updateReflex(
        reflexId,
        updates,
      );

      if (updatedReflex != null) {
        // Update in all reflexes
        final allReflexIndex = _reflexes.indexWhere(
          (reflex) => reflex.id == reflexId,
        );
        if (allReflexIndex != -1) {
          _reflexes[allReflexIndex] = updatedReflex;
        }

        // Update in post-specific reflexes
        for (final postReflexes in _postReflexes.values) {
          final postReflexIndex = postReflexes.indexWhere(
            (reflex) => reflex.id == reflexId,
          );
          if (postReflexIndex != -1) {
            postReflexes[postReflexIndex] = updatedReflex;
          }
        }

        AppLogger.debug('ReflexProvider: Updated reflex $reflexId');
        notifyListeners();
      }

      return updatedReflex;
    } catch (e, stackTrace) {
      AppLogger.error(
        'ReflexProvider: Error updating reflex $reflexId',
        error: e,
        stackTrace: stackTrace,
      );
      rethrow;
    }
  }

  /// Clear all state
  void clear() {
    _reflexes.clear();
    _postReflexes.clear();
    _postReflexStatus.clear();
    _postReflexErrors.clear();
    _status = ReflexStatus.initial;
    _error = null;
    notifyListeners();
  }

  /// Get reflex count for a post
  int getReflexCountForPost(String postId) {
    return _postReflexes[postId]?.length ?? 0;
  }
}
