class ChannelModel {
  final String id;
  final String name;
  final String? description;
  final String ownerId;
  final bool isPublic;
  final bool isActive;
  final int memberCount;
  final int postCount; // Number of posts in the channel
  final DateTime createdAt;
  final DateTime updatedAt;

  // Owner information (from join)
  final String? ownerUsername;
  final String? ownerDisplayName;
  final String? ownerAvatarUrl;

  // Channel icon
  final String? iconUrl;

  // User membership status
  final bool isUserMember;
  final String? userRole; // 'owner', 'admin', 'moderator', 'member'

  // Convenience getters for component compatibility
  String? get imageUrl => iconUrl;

  ChannelModel({
    required this.id,
    required this.name,
    this.description,
    required this.ownerId,
    required this.isPublic,
    required this.isActive,
    required this.memberCount,
    this.postCount = 0,
    required this.createdAt,
    required this.updatedAt,
    this.ownerUsername,
    this.ownerDisplayName,
    this.ownerAvatarUrl,
    this.iconUrl,
    this.isUserMember = false,
    this.userRole,
  });

  factory ChannelModel.fromJson(Map<String, dynamic> json) {
    return ChannelModel(
      id: json['id'] as String,
      name: json['name'] as String,
      description: json['description'] as String?,
      ownerId: json['ownerId'] as String,
      isPublic: json['isPublic'] as bool? ?? true,
      isActive: json['isActive'] as bool? ?? true,
      memberCount: json['memberCount'] as int? ?? 0,
      postCount: json['postCount'] as int? ?? 0,
      createdAt: DateTime.parse(json['createdAt'] as String),
      updatedAt: DateTime.parse(json['updatedAt'] as String),
      ownerUsername: json['ownerUsername'] as String?,
      ownerDisplayName: json['ownerDisplayName'] as String?,
      ownerAvatarUrl: json['ownerAvatarUrl'] as String?,
      iconUrl: json['iconUrl'] as String?,
      isUserMember: json['isUserMember'] as bool? ?? false,
      userRole: json['userRole'] as String?,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'name': name,
      'description': description,
      'ownerId': ownerId,
      'isPublic': isPublic,
      'isActive': isActive,
      'memberCount': memberCount,
      'createdAt': createdAt.toIso8601String(),
      'updatedAt': updatedAt.toIso8601String(),
      'ownerUsername': ownerUsername,
      'ownerDisplayName': ownerDisplayName,
      'ownerAvatarUrl': ownerAvatarUrl,
      'iconUrl': iconUrl,
      'isUserMember': isUserMember,
      'userRole': userRole,
    };
  }

  ChannelModel copyWith({
    String? id,
    String? name,
    String? description,
    String? ownerId,
    bool? isPublic,
    bool? isActive,
    int? memberCount,
    DateTime? createdAt,
    DateTime? updatedAt,
    String? ownerUsername,
    String? ownerDisplayName,
    String? ownerAvatarUrl,
    String? iconUrl,
    bool? isUserMember,
    String? userRole,
  }) {
    return ChannelModel(
      id: id ?? this.id,
      name: name ?? this.name,
      description: description ?? this.description,
      ownerId: ownerId ?? this.ownerId,
      isPublic: isPublic ?? this.isPublic,
      isActive: isActive ?? this.isActive,
      memberCount: memberCount ?? this.memberCount,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
      ownerUsername: ownerUsername ?? this.ownerUsername,
      ownerDisplayName: ownerDisplayName ?? this.ownerDisplayName,
      ownerAvatarUrl: ownerAvatarUrl ?? this.ownerAvatarUrl,
      iconUrl: iconUrl ?? this.iconUrl,
      isUserMember: isUserMember ?? this.isUserMember,
      userRole: userRole ?? this.userRole,
    );
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is ChannelModel && other.id == id;
  }

  @override
  int get hashCode => id.hashCode;

  @override
  String toString() {
    return 'ChannelModel(id: $id, name: $name, memberCount: $memberCount)';
  }
}
