import 'dart:async';
import 'dart:developer' as developer;
import 'package:flutter/services.dart';
import '../utils/app_logger.dart';
import 'xbox_auth_service.dart';

/// Service for handling deep links in the app
class DeepLinkService {
  static DeepLinkService? _instance;
  static DeepLinkService get instance => _instance ??= DeepLinkService._();

  DeepLinkService._();

  static const MethodChannel _channel = MethodChannel('gameflex/deeplink');
  StreamSubscription<String>? _linkSubscription;

  /// Initialize deep link handling
  Future<void> initialize() async {
    try {
      developer.log('DeepLinkService: Initializing deep link handling');
      AppLogger.info('Initializing deep link service');

      // Set up method channel handler for deep links
      _channel.setMethodCallHandler(_handleMethodCall);

      // Check for initial link (when app is launched via deep link)
      await _checkInitialLink();

      developer.log('DeepLinkService: Deep link service initialized');
      AppLogger.info('Deep link service initialized successfully');
    } catch (e) {
      developer.log('DeepLinkService: Error initializing: $e');
      AppLogger.error('Error initializing deep link service: $e');
    }
  }

  /// Handle method calls from native platforms
  Future<dynamic> _handleMethodCall(MethodCall call) async {
    switch (call.method) {
      case 'onDeepLink':
        final String link = call.arguments as String;
        await _handleDeepLink(link);
        break;
      default:
        developer.log('DeepLinkService: Unknown method: ${call.method}');
    }
  }

  /// Check for initial deep link when app is launched
  Future<void> _checkInitialLink() async {
    try {
      final String? initialLink = await _channel.invokeMethod('getInitialLink');
      if (initialLink != null) {
        developer.log('DeepLinkService: Found initial link: $initialLink');
        await _handleDeepLink(initialLink);
      }
    } catch (e) {
      developer.log('DeepLinkService: Error checking initial link: $e');
      AppLogger.warning('Error checking initial deep link: $e');
    }
  }

  /// Handle incoming deep links
  Future<void> _handleDeepLink(String link) async {
    try {
      developer.log('DeepLinkService: Handling deep link: $link');
      AppLogger.info('Handling deep link: $link');

      final uri = Uri.parse(link);
      
      // Handle Xbox authentication completion
      if (uri.scheme == 'gameflex' && uri.host == 'xbox-auth-complete') {
        await _handleXboxAuthComplete(uri);
      } else if (uri.scheme == 'gameflex' && uri.host == 'xbox-auth-success') {
        await _handleXboxAuthSuccess(uri);
      } else {
        developer.log('DeepLinkService: Unknown deep link: $link');
        AppLogger.warning('Unknown deep link received: $link');
      }
    } catch (e) {
      developer.log('DeepLinkService: Error handling deep link: $e');
      AppLogger.error('Error handling deep link: $e');
    }
  }

  /// Handle Xbox authentication completion with temp ID
  Future<void> _handleXboxAuthComplete(Uri uri) async {
    try {
      final tempId = uri.queryParameters['tempId'];
      if (tempId == null) {
        developer.log('DeepLinkService: No tempId in Xbox auth complete link');
        AppLogger.warning('Xbox auth complete link missing tempId');
        return;
      }

      developer.log('DeepLinkService: Processing Xbox auth completion with tempId: $tempId');
      AppLogger.info('Processing Xbox authentication completion');

      // Claim the Xbox authentication
      final success = await XboxAuthService.instance.claimXboxAuth(tempId);
      
      if (success) {
        developer.log('DeepLinkService: Xbox authentication claimed successfully');
        AppLogger.info('Xbox authentication claimed successfully');
        
        // Notify listeners that Xbox auth is complete
        _notifyXboxAuthComplete(true, null);
      } else {
        developer.log('DeepLinkService: Failed to claim Xbox authentication');
        AppLogger.error('Failed to claim Xbox authentication');
        
        _notifyXboxAuthComplete(false, 'Failed to claim Xbox authentication');
      }
    } catch (e) {
      developer.log('DeepLinkService: Error processing Xbox auth complete: $e');
      AppLogger.error('Error processing Xbox auth completion: $e');
      
      _notifyXboxAuthComplete(false, e.toString());
    }
  }

  /// Handle Xbox authentication success (legacy)
  Future<void> _handleXboxAuthSuccess(Uri uri) async {
    try {
      final gamertag = uri.queryParameters['gamertag'];
      developer.log('DeepLinkService: Xbox auth success for gamertag: $gamertag');
      AppLogger.info('Xbox authentication success');
      
      // Just notify success - the account should already be linked
      _notifyXboxAuthComplete(true, null);
    } catch (e) {
      developer.log('DeepLinkService: Error processing Xbox auth success: $e');
      AppLogger.error('Error processing Xbox auth success: $e');
    }
  }

  /// Stream controller for Xbox auth completion events
  final StreamController<XboxAuthResult> _xboxAuthController = 
      StreamController<XboxAuthResult>.broadcast();

  /// Stream of Xbox authentication completion events
  Stream<XboxAuthResult> get xboxAuthCompletionStream => _xboxAuthController.stream;

  /// Notify listeners of Xbox auth completion
  void _notifyXboxAuthComplete(bool success, String? error) {
    _xboxAuthController.add(XboxAuthResult(success: success, error: error));
  }

  /// Dispose of resources
  void dispose() {
    _linkSubscription?.cancel();
    _xboxAuthController.close();
  }
}

/// Result of Xbox authentication completion
class XboxAuthResult {
  final bool success;
  final String? error;

  XboxAuthResult({required this.success, this.error});
}
