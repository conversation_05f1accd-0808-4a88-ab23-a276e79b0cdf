import 'dart:developer' as developer;
import 'package:sign_in_with_apple/sign_in_with_apple.dart';
import '../models/user_model.dart';
import '../utils/app_logger.dart';
import 'api_service.dart';

/// Service for handling Apple Sign In authentication
class AppleSignInService {
  static final AppleSignInService _instance = AppleSignInService._internal();
  static AppleSignInService get instance => _instance;
  AppleSignInService._internal();

  /// Check if Apple Sign In is available on this device
  Future<bool> isAvailable() async {
    try {
      return await SignInWithApple.isAvailable();
    } catch (e) {
      developer.log('AppleSignInService: Error checking availability: $e');
      return false;
    }
  }

  /// Sign in with Apple
  Future<AppleSignInResponse> signIn() async {
    try {
      developer.log('AppleSignInService: Starting Apple Sign In');
      AppLogger.auth('Starting Apple Sign In');

      // Check if Apple Sign In is available
      if (!await isAvailable()) {
        return AppleSignInResponse(
          success: false,
          message: 'Apple Sign In is not available on this device',
        );
      }

      // Request Apple Sign In
      final credential = await SignInWithApple.getAppleIDCredential(
        scopes: [
          AppleIDAuthorizationScopes.email,
          AppleIDAuthorizationScopes.fullName,
        ],
      );

      developer.log('AppleSignInService: Received Apple credential');
      AppLogger.auth('Received Apple credential');

      // Prepare request body for backend
      final requestBody = <String, dynamic>{
        'identityToken': credential.identityToken,
        'authorizationCode': credential.authorizationCode,
      };

      // Add user information if available (only provided on first sign in)
      if (credential.givenName != null || 
          credential.familyName != null || 
          credential.email != null) {
        requestBody['user'] = <String, dynamic>{};
        
        if (credential.givenName != null || credential.familyName != null) {
          requestBody['user']['name'] = <String, dynamic>{
            if (credential.givenName != null) 'firstName': credential.givenName,
            if (credential.familyName != null) 'lastName': credential.familyName,
          };
        }
        
        if (credential.email != null) {
          requestBody['user']['email'] = credential.email;
        }
      }

      developer.log('AppleSignInService: Sending request to backend');
      AppLogger.auth('Sending Apple Sign In request to backend');

      // Send to backend
      final response = await ApiService.instance.makeRequest(
        method: 'POST',
        path: '/auth/apple',
        body: requestBody,
      );

      final data = ApiService.instance.parseResponse(response);

      // Parse user data
      final userData = data['user'] as Map<String, dynamic>?;
      UserModel? user;
      if (userData != null) {
        user = UserModel.fromJson(userData);
      }

      // Parse tokens
      final tokensData = data['tokens'] as Map<String, dynamic>?;
      AuthTokens? tokens;
      if (tokensData != null) {
        tokens = AuthTokens(
          accessToken: tokensData['accessToken'] as String,
          refreshToken: tokensData['refreshToken'] as String,
          idToken: tokensData['idToken'] as String,
        );
      }

      developer.log('AppleSignInService: Apple Sign In successful');
      AppLogger.auth('Apple Sign In successful');

      return AppleSignInResponse(
        success: true,
        message: data['message'] as String? ?? 'Apple Sign In successful',
        user: user,
        tokens: tokens,
        requiresUsername: data['requiresUsername'] as bool? ?? false,
      );

    } catch (e, stackTrace) {
      developer.log('AppleSignInService: Apple Sign In failed: $e');
      AppLogger.error(
        'Apple Sign In failed',
        error: e,
        stackTrace: stackTrace,
      );

      String errorMessage;
      if (e is SignInWithAppleAuthorizationException) {
        switch (e.code) {
          case AuthorizationErrorCode.canceled:
            errorMessage = 'Apple Sign In was cancelled';
            break;
          case AuthorizationErrorCode.failed:
            errorMessage = 'Apple Sign In failed';
            break;
          case AuthorizationErrorCode.invalidResponse:
            errorMessage = 'Invalid response from Apple';
            break;
          case AuthorizationErrorCode.notHandled:
            errorMessage = 'Apple Sign In not handled';
            break;
          case AuthorizationErrorCode.unknown:
          default:
            errorMessage = 'Unknown Apple Sign In error';
            break;
        }
      } else {
        errorMessage = e.toString();
      }

      return AppleSignInResponse(
        success: false,
        message: errorMessage,
      );
    }
  }
}

/// Response from Apple Sign In operation
class AppleSignInResponse {
  final bool success;
  final String message;
  final UserModel? user;
  final AuthTokens? tokens;
  final bool requiresUsername;

  AppleSignInResponse({
    required this.success,
    required this.message,
    this.user,
    this.tokens,
    this.requiresUsername = false,
  });
}

/// Authentication tokens
class AuthTokens {
  final String accessToken;
  final String refreshToken;
  final String idToken;

  AuthTokens({
    required this.accessToken,
    required this.refreshToken,
    required this.idToken,
  });
}
