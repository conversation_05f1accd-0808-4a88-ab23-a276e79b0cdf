// lib/services/flare_service.dart

import '../models/flare_model.dart';

class FlareService {
  static final FlareService _instance = FlareService._internal();
  factory FlareService() => _instance;
  FlareService._internal();

  // Static data based on the assets we copied from the old app
  static const Map<FlareCategory, List<String>> _flareData = {
    FlareCategory.arrows: [
      'Arrow 1.png',
      'Arrow 2.png',
      'Arrow 3.png',
      'Arrow 4.png',
      'Vector-2.png',
      'icons8-arrow-50.png',
    ],
    FlareCategory.bubbles: [
      'Angry Bubble 1.png',
      'Angry Bubble 2.png',
      'Angry Question Mark.png',
      'Shout 1.png',
      'Shout 2.png',
      'Shout 3.png',
      'Shout 4.png',
      'Shout 5.png',
      'Shout 6.png',
      'Speech Bubble 1.png',
      'Speech Bubble 10.png',
      'Speech Bubble 11.png',
      'Speech Bubble 13.png',
      'Speech Bubble 14.png',
      'Speech Bubble 15.png',
      'Speech Bubble 4.png',
      'Speech Bubble 5.png',
      'Speech Bubble 6.png',
      'Speech Bubble 7.png',
      'Speech Bubble 9.png',
      'Square Cloud 1.png',
      'Square Cloud 2.png',
      'Square Cloud 3.png',
      'Thought Cloud 3.png',
      'What was that text.png',
      'What were you thinking text.png',
      'Whisper Bubble 1.png',
    ],
    FlareCategory.flexFun: [
      'Ass Kicked.png',
      'Barrel Stuffed.png',
      'Bullet Hole 1.png',
      'Bullet Sponge.png',
      'Camper Text.png',
      'Camper.png',
      'Cheater.png',
      'Crosshairs.png',
      'Cry Baby.png',
      'Dead Inside.png',
      'Face Roll.png',
      'Fail Army.png',
      'Farming NPCs.png',
      'Feeding the Enemy.png',
      'Filthy Casual.png',
      'Finger Guns.png',
      'Fireball.png',
      'Five Stars.png',
      'Flex Arm.png',
      'Flex Man 1.png',
      'Flex Man 2.png',
      'Flex Smoothfly - Shouting.png',
      'Flex Smoothfly - Smiling.png',
      'Flexi Hand on mouth.png',
      'Flexi Hearts.png',
      'Flexi Laugh and Point.png',
      'Flexi Shocked.png',
      'Flexin Smoothfly.png',
      'Flexio Drinking.png',
      'Flexio Grumpy.png',
      'Flexio Running.png',
      'Flexio Smashing.png',
      'Flexio Stunned.png',
      'Fragged.png',
      'Friendly Fire.png',
      'Good Game.png',
      'Grinding.png',
      'Group 6.png',
      'Group-2.png',
      'Group-3.png',
      'Group.png',
      'HeFlex Would You look at that.png',
      'Headshot 2.png',
      'Headshot.png',
      'High Five.png',
      'Mind Blown.png',
      'Nerd Girl.png',
      'One Shot Kill.png',
      'Pirate Full Body.png',
      'Point and Laugh.png',
      'Rekt.png',
      'Sarcastic Soldier.png',
      'Screen Peeker.png',
      'Smurf.png',
      'Spanked.png',
      'Thumbs Down.png',
      'Vector-3.png',
      'Vector.png',
      'What were you thinking.png',
      'flexi What was that.png',
      'icons8-alien-50.png',
      'icons8-apple-arcade-50.png',
      'icons8-arcade-cabinet-50.png',
      'icons8-asian-character-50.png',
      'icons8-assassins-creed-50.png',
      'icons8-atari-2600-50.png',
      'icons8-bendy-50.png',
      'icons8-best-terraria-50.png',
      'icons8-body-armor-50.png',
      'icons8-crash-bandicoot-50.png',
      'icons8-danganronpa-50.png',
      'icons8-death-star-50.png',
      'icons8-eevee-50.png',
      'icons8-futurama-zapp-brannigan-50.png',
      'icons8-game-controller-50.png',
      'icons8-grand-theft-auto-v-50.png',
      'icons8-hyper-potion-50.png',
      'icons8-joystick-50.png',
      'icons8-mana-50.png',
      'icons8-mega-ball-50.png',
      'icons8-meowth-50.png',
      'icons8-millennium-rod-50.png',
      'icons8-minecraft-main-character-50.png',
      'icons8-minecraft-pickaxe-50.png',
      'icons8-minecraft-pug-50.png',
      'icons8-minecraft-skeleton-50.png',
      'icons8-minecraft-sword-50-2.png',
      'icons8-minecraft-sword-50.png',
      'icons8-minecraft-zombie-50.png',
      'icons8-naruto-50-2.png',
      'icons8-naruto-50.png',
      'icons8-nintendo-gamecube-50.png',
      'icons8-pacman-50-2.png',
      'icons8-pacman-50-3.png',
      'icons8-pacman-50.png',
      'icons8-pikachu-pokemon-50.png',
      'icons8-play-button-circled-50.png',
      'icons8-playstation-50.png',
      'icons8-pokeball-50-2.png',
      'icons8-pokeball-50.png',
      'icons8-pokecoin-50.png',
      'icons8-pubg-50-2.png',
      'icons8-pubg-50.png',
      'icons8-pubg-helmet-50.png',
      'icons8-qq-50.png',
      'icons8-rainbow-six-siege-50.png',
      'icons8-rockstar-games-50.png',
      'icons8-satellites-50.png',
      'icons8-skyrim-50.png',
      'icons8-sonic-the-hedgehog-50.png',
      'icons8-spyro-50.png',
      'icons8-stormtrooper-50.png',
      'icons8-super-mario-50-2.png',
      'icons8-super-mario-50.png',
      'icons8-visual-game-boy-50-2.png',
      'icons8-visual-game-boy-50-3.png',
      'icons8-visual-game-boy-50.png',
      'icons8-xbox-50.png',
      'mushblue.png',
      'mushgreen.png',
      'mushred.png',
      'mushyellow.png',
      'spaz kid.png',
      'tetris 1.png',
      'tetris 2.png',
      'tetris 3.png',
      'tetris 4.png',
      'tetris 5.png',
    ],
    FlareCategory.memes: [
      'Cutout 1.png',
      'Cutout 2.png',
      'Cutout 5.png',
      'Cutout 6.png',
      'Cutout-10.png',
      'Cutout-11.png',
      'Cutout-12.png',
      'Cutout-13.png',
      'Cutout-14.png',
      'Cutout-15.png',
      'Cutout-16.png',
      'Cutout-17.png',
      'Cutout-18.png',
      'Cutout-3.png',
      'Cutout-4.png',
      'Cutout-7.png',
      'Cutout-8.png',
      'Future US Army.png',
      'Hey i wanna.png',
      'I don_t care.png',
      'I sure hope.png',
      'I was to busy.png',
      'It is Wednesday.png',
      'Okay.png',
      'That was legitness.png',
      'Think.png',
      'Thumbs up.png',
    ],
    FlareCategory.smack: [
      '80s_Vibe.png',
      'Aesthetic.png',
      'Are_You_Ok.png',
      'Ass Kicked text.png',
      'Back_To_The_Past.png',
      'Barrel Stuffed Text.png',
      'Bullet Sponge Text.png',
      'Cheater Text.png',
      'Cherry_Bomb.png',
      'Classic.png',
      'Cool!.png',
      'Cry Baby Text.png',
      'Eye Roll text.png',
      'Face Palm text.png',
      'Face Roll Text.png',
      'Fail Army text copy.png',
      'Fail Army text.png',
      'Fail Text.png',
      'Fail.png',
      'Far_Out.png',
      'Farming NPCs text.png',
      'Feeding the Enemy Text.png',
      'Filthy Casual Text.png',
      'Flexed 1 Text.png',
      'Flexed 2 Text.png',
      'Game Over.png',
      'Game_Over.png',
      'God Mode Text.png',
      'Good Game Text.png',
      'Great Job, Idiot text.png',
      'Griefer Text.png',
      'Grinding Text.png',
      'Groovy.png',
      'Hahahaha Text.png',
      'Headshot 2 text.png',
      'Healthbar.png',
      'High Five Text.png',
      'Layer 5.png',
      'Let_s_Go.png',
      'Lightning 1.png',
      'Lit Text.png',
      'Logo.png',
      'Nerfed Text.png',
      'Oh no it_s retarded text.png',
      'Oh yeah text.png',
      'Old_School.png',
      'One Shot Kill Text.png',
      'Quick Scope Text.png',
      'Rage Quit text.png',
      'Red_is_Sus.png',
      'Rekt Text.png',
      'Retro.png',
      'Roasted Text.png',
      'SH_ Text.png',
      'Smurf Text.png',
      'Spanked Text.png',
      'Spoiler Text.png',
      'Spray and Pray text.png',
      'Teabagged 2 Text.png',
      'That_s Cap.png',
      'That_s_Sus.png',
      'Toxic Teammate Text.png',
      'Trollin_ Text.png',
      'Waaaaaaah text.png',
      'Would you look at that text.png',
      'You_Sus_Bro.png',
    ],
    FlareCategory.smellies: [
      'Layer 10.png',
      'Layer 11.png',
      'Layer 12.png',
      'Layer 13.png',
      'Layer 14.png',
      'Layer 15.png',
      'Layer 16.png',
      'Layer 17.png',
      'Layer 18.png',
      'Layer 19.png',
      'Layer 20.png',
      'Layer 21.png',
      'Layer 22.png',
      'Layer 23.png',
      'Layer 24.png',
      'Layer 25.png',
      'Layer 26.png',
      'Layer 27.png',
      'Layer 28.png',
      'Layer 29.png',
      'Layer 30.png',
      'Layer 31.png',
      'Layer 32.png',
      'Layer 33.png',
      'Layer 6.png',
      'Layer 7.png',
      'Layer 8.png',
      'Layer 9.png',
      'Smileys.png',
    ],
  };

  List<FlareCategory> getCategories() {
    return FlareCategory.values;
  }

  List<FlareItem> getFlareForCategory(FlareCategory category) {
    final fileNames = _flareData[category] ?? [];
    return fileNames.map((fileName) {
      return FlareItem(
        name: fileName.replaceAll('.png', ''),
        fileName: fileName,
        category: category,
      );
    }).toList();
  }

  List<FlareItem> getAllFlare() {
    final List<FlareItem> allItems = [];
    for (final category in FlareCategory.values) {
      allItems.addAll(getFlareForCategory(category));
    }
    return allItems;
  }

  FlareItem? findFlareByFileName(String fileName) {
    for (final category in FlareCategory.values) {
      final items = getFlareForCategory(category);
      for (final item in items) {
        if (item.fileName == fileName) {
          return item;
        }
      }
    }
    return null;
  }
}
