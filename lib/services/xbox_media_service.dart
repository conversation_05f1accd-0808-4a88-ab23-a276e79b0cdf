import 'dart:async';
import 'dart:developer' as developer;
import 'dart:io';
import 'package:http/http.dart' as http;
import 'package:path_provider/path_provider.dart';
import 'package:path/path.dart' as path;
import '../models/xbox_media_model.dart';
import '../utils/app_logger.dart';
import 'api_service.dart';

/// Service for fetching Xbox screenshots and game clips
class XboxMediaService {
  static final XboxMediaService _instance = XboxMediaService._internal();
  static XboxMediaService get instance => _instance;
  XboxMediaService._internal();

  /// Get Xbox screenshots for the authenticated user
  Future<XboxMediaResponse?> getScreenshots({
    int numItems = 25,
    String? contToken,
  }) async {
    try {
      developer.log('XboxMediaService: Fetching Xbox screenshots');
      AppLogger.info('Fetching Xbox screenshots');

      final queryParams = <String, String>{'numItems': numItems.toString()};

      if (contToken != null && contToken.isNotEmpty) {
        queryParams['contToken'] = contToken;
      }

      final response = await ApiService.instance.makeAuthenticatedRequest(
        method: 'GET',
        path: '/xbox/screenshots',
        queryParams: queryParams,
      );

      if (response.statusCode == 200) {
        final data = ApiService.instance.parseResponse(response);
        developer.log(
          'XboxMediaService: Successfully fetched ${data['screenshots']?.length ?? 0} screenshots',
        );
        AppLogger.info('Successfully fetched Xbox screenshots');

        final screenshots =
            (data['screenshots'] as List<dynamic>?)
                ?.map(
                  (item) =>
                      XboxMediaItem.fromJson(item as Map<String, dynamic>),
                )
                .toList() ??
            [];

        final pagination = data['pagination'] as Map<String, dynamic>?;

        return XboxMediaResponse(
          items: screenshots,
          hasMore: pagination?['hasMore'] as bool? ?? false,
          contToken: pagination?['contToken'] as String?,
        );
      } else if (response.statusCode == 404) {
        developer.log('XboxMediaService: No Xbox account linked');
        AppLogger.info('No Xbox account linked for screenshots');
        return null;
      } else {
        final data = ApiService.instance.parseResponse(response);
        developer.log(
          'XboxMediaService: Failed to fetch screenshots: ${data['error']}',
        );
        AppLogger.error(
          'Failed to fetch Xbox screenshots',
          error: data['error'],
        );
        return null;
      }
    } catch (e) {
      developer.log('XboxMediaService: Error fetching screenshots: $e');
      AppLogger.error('Error fetching Xbox screenshots', error: e);
      return null;
    }
  }

  /// Get Xbox game clips for the authenticated user
  Future<XboxMediaResponse?> getGameClips({
    int numItems = 25,
    String? contToken,
  }) async {
    try {
      developer.log('XboxMediaService: Fetching Xbox game clips');
      AppLogger.info('Fetching Xbox game clips');

      final queryParams = <String, String>{'numItems': numItems.toString()};

      if (contToken != null && contToken.isNotEmpty) {
        queryParams['contToken'] = contToken;
      }

      final response = await ApiService.instance.makeAuthenticatedRequest(
        method: 'GET',
        path: '/xbox/gameclips',
        queryParams: queryParams,
      );

      if (response.statusCode == 200) {
        final data = ApiService.instance.parseResponse(response);
        developer.log(
          'XboxMediaService: Successfully fetched ${data['gameClips']?.length ?? 0} game clips',
        );
        AppLogger.info('Successfully fetched Xbox game clips');

        final gameClips =
            (data['gameClips'] as List<dynamic>?)
                ?.map(
                  (item) =>
                      XboxMediaItem.fromJson(item as Map<String, dynamic>),
                )
                .toList() ??
            [];

        final pagination = data['pagination'] as Map<String, dynamic>?;

        return XboxMediaResponse(
          items: gameClips,
          hasMore: pagination?['hasMore'] as bool? ?? false,
          contToken: pagination?['contToken'] as String?,
        );
      } else if (response.statusCode == 404) {
        developer.log('XboxMediaService: No Xbox account linked');
        AppLogger.info('No Xbox account linked for game clips');
        return null;
      } else {
        final data = ApiService.instance.parseResponse(response);
        developer.log(
          'XboxMediaService: Failed to fetch game clips: ${data['error']}',
        );
        AppLogger.error(
          'Failed to fetch Xbox game clips',
          error: data['error'],
        );
        return null;
      }
    } catch (e) {
      developer.log('XboxMediaService: Error fetching game clips: $e');
      AppLogger.error('Error fetching Xbox game clips', error: e);
      return null;
    }
  }

  /// Get download URL for Xbox media item
  Future<String?> getMediaDownloadUrl(String mediaId) async {
    try {
      developer.log(
        'XboxMediaService: Getting download URL for media: $mediaId',
      );
      AppLogger.info('Getting Xbox media download URL');

      final response = await ApiService.instance.makeAuthenticatedRequest(
        method: 'GET',
        path: '/xbox/media/$mediaId/download',
      );

      if (response.statusCode == 200) {
        final data = ApiService.instance.parseResponse(response);
        final downloadUrl = data['downloadUrl'] as String?;

        developer.log('XboxMediaService: Successfully got download URL');
        AppLogger.info('Successfully got Xbox media download URL');

        return downloadUrl;
      } else {
        final data = ApiService.instance.parseResponse(response);
        developer.log(
          'XboxMediaService: Failed to get download URL: ${data['error']}',
        );
        AppLogger.error(
          'Failed to get Xbox media download URL',
          error: data['error'],
        );
        return null;
      }
    } catch (e) {
      developer.log('XboxMediaService: Error getting download URL: $e');
      AppLogger.error('Error getting Xbox media download URL', error: e);
      return null;
    }
  }

  /// Download Xbox media item to local storage
  Future<String?> downloadMedia(XboxMediaItem mediaItem) async {
    try {
      developer.log(
        'XboxMediaService: Downloading Xbox media: ${mediaItem.id}',
      );
      AppLogger.info('Downloading Xbox media item');

      // Get the download URL first
      final downloadUrl = await getMediaDownloadUrl(mediaItem.id);
      if (downloadUrl == null) {
        developer.log('XboxMediaService: Failed to get download URL');
        AppLogger.error('Failed to get download URL for Xbox media');
        return null;
      }

      // For now, return the download URL
      // In a full implementation, you would download the file and save it locally
      developer.log(
        'XboxMediaService: Media download URL obtained: $downloadUrl',
      );
      AppLogger.info('Xbox media download URL obtained');

      return downloadUrl;
    } catch (e) {
      developer.log('XboxMediaService: Error downloading media: $e');
      AppLogger.error('Error downloading Xbox media', error: e);
      return null;
    }
  }

  /// Get combined media (screenshots and game clips) for browsing
  Future<List<XboxMediaItem>> getAllMedia({
    int screenshotLimit = 15,
    int gameClipLimit = 10,
  }) async {
    try {
      developer.log('XboxMediaService: Fetching all Xbox media');
      AppLogger.info('Fetching all Xbox media');

      final List<XboxMediaItem> allMedia = [];

      // Fetch screenshots
      final screenshotsResponse = await getScreenshots(
        numItems: screenshotLimit,
      );
      if (screenshotsResponse != null) {
        allMedia.addAll(screenshotsResponse.items);
      }

      // Fetch game clips
      final gameClipsResponse = await getGameClips(numItems: gameClipLimit);
      if (gameClipsResponse != null) {
        allMedia.addAll(gameClipsResponse.items);
      }

      // Sort by date taken (most recent first)
      allMedia.sort((a, b) => b.dateTaken.compareTo(a.dateTaken));

      developer.log(
        'XboxMediaService: Successfully fetched ${allMedia.length} total media items',
      );
      AppLogger.info('Successfully fetched all Xbox media');

      return allMedia;
    } catch (e) {
      developer.log('XboxMediaService: Error fetching all media: $e');
      AppLogger.error('Error fetching all Xbox media', error: e);
      return [];
    }
  }

  /// Filter media by game title
  List<XboxMediaItem> filterByGame(
    List<XboxMediaItem> media,
    String gameTitle,
  ) {
    return media
        .where(
          (item) =>
              item.gameTitle.toLowerCase().contains(gameTitle.toLowerCase()),
        )
        .toList();
  }

  /// Filter media by type
  List<XboxMediaItem> filterByType(
    List<XboxMediaItem> media,
    XboxMediaType type,
  ) {
    return media.where((item) => item.type == type).toList();
  }

  /// Get unique game titles from media list
  List<String> getUniqueGameTitles(List<XboxMediaItem> media) {
    final Set<String> gameTitles = {};
    for (final item in media) {
      if (item.gameTitle.isNotEmpty && item.gameTitle != 'Unknown Game') {
        gameTitles.add(item.gameTitle);
      }
    }
    return gameTitles.toList()..sort();
  }

  /// Download Xbox media item directly to device
  Future<File?> downloadMediaItem(
    XboxMediaItem mediaItem, {
    Function(double)? onProgress,
  }) async {
    try {
      developer.log(
        'XboxMediaService: Downloading ${mediaItem.type}: ${mediaItem.title}',
      );
      AppLogger.info('Downloading Xbox ${mediaItem.type}: ${mediaItem.title}');

      if (mediaItem.downloadUrl.isEmpty) {
        throw Exception('Download URL is empty');
      }

      // Get app documents directory
      final directory = await getApplicationDocumentsDirectory();
      final xboxDir = Directory(path.join(directory.path, 'xbox_downloads'));

      // Create directory if it doesn't exist
      if (!await xboxDir.exists()) {
        await xboxDir.create(recursive: true);
      }

      // Generate filename
      final extension =
          mediaItem.type == XboxMediaType.screenshot ? 'png' : 'mp4';
      final timestamp = DateTime.now().millisecondsSinceEpoch;
      final filename =
          '${mediaItem.type.name}_${mediaItem.id}_$timestamp.$extension';
      final filePath = path.join(xboxDir.path, filename);

      // Download the file
      final response = await http.get(Uri.parse(mediaItem.downloadUrl));

      if (response.statusCode == 200) {
        final file = File(filePath);
        await file.writeAsBytes(response.bodyBytes);

        developer.log(
          'XboxMediaService: Successfully downloaded ${mediaItem.type} to: $filePath',
        );
        AppLogger.info('Successfully downloaded Xbox ${mediaItem.type}');

        return file;
      } else {
        throw Exception('Failed to download: HTTP ${response.statusCode}');
      }
    } catch (e) {
      developer.log('XboxMediaService: Error downloading media item: $e');
      AppLogger.error('Error downloading Xbox media item: $e');
      rethrow;
    }
  }

  /// Download multiple Xbox media items
  Future<List<File>> downloadMultipleItems(
    List<XboxMediaItem> mediaItems, {
    Function(int completed, int total)? onProgress,
  }) async {
    final downloadedFiles = <File>[];

    try {
      developer.log(
        'XboxMediaService: Downloading ${mediaItems.length} Xbox media items',
      );
      AppLogger.info('Downloading ${mediaItems.length} Xbox media items');

      for (int i = 0; i < mediaItems.length; i++) {
        try {
          final file = await downloadMediaItem(mediaItems[i]);
          if (file != null) {
            downloadedFiles.add(file);
          }

          // Report progress
          onProgress?.call(i + 1, mediaItems.length);
        } catch (e) {
          developer.log(
            'XboxMediaService: Failed to download item ${mediaItems[i].id}: $e',
          );
          AppLogger.error('Failed to download Xbox media item: $e');
          // Continue with other downloads
        }
      }

      developer.log(
        'XboxMediaService: Successfully downloaded ${downloadedFiles.length}/${mediaItems.length} items',
      );
      AppLogger.info(
        'Downloaded ${downloadedFiles.length}/${mediaItems.length} Xbox media items',
      );

      return downloadedFiles;
    } catch (e) {
      developer.log('XboxMediaService: Error downloading multiple items: $e');
      AppLogger.error('Error downloading multiple Xbox media items: $e');
      rethrow;
    }
  }

  /// Get local Xbox downloads directory
  Future<Directory> getDownloadsDirectory() async {
    final directory = await getApplicationDocumentsDirectory();
    final xboxDir = Directory(path.join(directory.path, 'xbox_downloads'));

    if (!await xboxDir.exists()) {
      await xboxDir.create(recursive: true);
    }

    return xboxDir;
  }

  /// Check if a media item is already downloaded
  Future<bool> isMediaDownloaded(XboxMediaItem mediaItem) async {
    try {
      final xboxDir = await getDownloadsDirectory();
      final extension =
          mediaItem.type == XboxMediaType.screenshot ? 'png' : 'mp4';

      // Check for files with the media item ID in the filename
      final files = await xboxDir.list().toList();
      return files.any(
        (file) =>
            file is File &&
            file.path.contains(mediaItem.id) &&
            file.path.endsWith('.$extension'),
      );
    } catch (e) {
      developer.log('XboxMediaService: Error checking download status: $e');
      return false;
    }
  }

  /// Get the local file path for a downloaded media item
  Future<File?> getDownloadedMediaFile(XboxMediaItem mediaItem) async {
    try {
      final xboxDir = await getDownloadsDirectory();
      final extension =
          mediaItem.type == XboxMediaType.screenshot ? 'png' : 'mp4';

      // Find the file with the media item ID
      final files = await xboxDir.list().toList();
      final matchingFile = files.firstWhere(
        (file) =>
            file is File &&
            file.path.contains(mediaItem.id) &&
            file.path.endsWith('.$extension'),
        orElse: () => throw StateError('File not found'),
      );

      return matchingFile as File;
    } catch (e) {
      developer.log('XboxMediaService: Error getting downloaded file: $e');
      return null;
    }
  }

  /// Clear downloaded Xbox media files
  Future<void> clearDownloads() async {
    try {
      final xboxDir = await getDownloadsDirectory();

      if (await xboxDir.exists()) {
        await xboxDir.delete(recursive: true);
        developer.log('XboxMediaService: Cleared Xbox downloads directory');
        AppLogger.info('Cleared Xbox downloads directory');
      }
    } catch (e) {
      developer.log('XboxMediaService: Error clearing downloads: $e');
      AppLogger.error('Error clearing Xbox downloads: $e');
      rethrow;
    }
  }
}
