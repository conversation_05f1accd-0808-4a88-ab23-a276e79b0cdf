import 'dart:async';
import 'package:flutter/material.dart';
import 'package:cached_network_image/cached_network_image.dart';
import 'package:video_player/video_player.dart';
import '../models/post_model.dart';
import '../utils/app_logger.dart';

/// Service for preloading media content in feeds
class MediaPreloader {
  static final MediaPreloader _instance = MediaPreloader._internal();
  static MediaPreloader get instance => _instance;
  MediaPreloader._internal();

  final Map<String, bool> _preloadedImages = {};
  final Map<String, VideoPlayerController> _preloadedVideos = {};
  final Set<String> _currentlyPreloading = {};

  static const int maxPreloadDistance = 3; // Preload next 3 posts
  static const int maxCachedVideos =
      5; // Keep max 5 video controllers in memory

  /// Preload media for upcoming posts in the feed
  Future<void> preloadUpcomingMedia({
    required List<PostModel> posts,
    required int currentIndex,
    required BuildContext context,
  }) async {
    if (posts.isEmpty || currentIndex >= posts.length) return;

    // Calculate range of posts to preload
    final startIndex = currentIndex + 1;
    final endIndex = (currentIndex + maxPreloadDistance + 1).clamp(
      0,
      posts.length,
    );

    AppLogger.debug(
      'MediaPreloader: Preloading posts $startIndex to ${endIndex - 1}',
    );

    // Preload media for upcoming posts in order
    for (int i = startIndex; i < endIndex; i++) {
      final post = posts[i];
      if (post.hasMedia) {
        // Don't await to avoid blocking the UI
        _preloadPostMedia(post, context);
      }
    }

    // Clean up old preloaded content
    _cleanupOldContent(posts, currentIndex);
  }

  /// Preload media for a specific post
  Future<void> _preloadPostMedia(PostModel post, BuildContext context) async {
    try {
      final mediaUrl = await post.getMediaUrl();
      if (mediaUrl == null || mediaUrl.isEmpty) return;

      // Skip if already preloading or preloaded
      if (_currentlyPreloading.contains(mediaUrl)) return;

      _currentlyPreloading.add(mediaUrl);

      if (post.isImage && context.mounted) {
        await _preloadImage(mediaUrl, context);
      } else if (post.isVideo) {
        await _preloadVideo(mediaUrl);
      }

      _currentlyPreloading.remove(mediaUrl);
    } catch (e) {
      AppLogger.error(
        'MediaPreloader: Error preloading media for post ${post.id}',
        error: e,
      );
    }
  }

  /// Preload an image using cached network image
  Future<void> _preloadImage(String imageUrl, BuildContext context) async {
    if (_preloadedImages.containsKey(imageUrl)) return;

    try {
      AppLogger.debug('MediaPreloader: Preloading image $imageUrl');

      // Use CachedNetworkImage's precaching
      await precacheImage(CachedNetworkImageProvider(imageUrl), context);

      _preloadedImages[imageUrl] = true;
      AppLogger.debug('MediaPreloader: Successfully preloaded image $imageUrl');
    } catch (e) {
      AppLogger.error(
        'MediaPreloader: Failed to preload image $imageUrl',
        error: e,
      );
    }
  }

  /// Preload a video by initializing its controller
  Future<void> _preloadVideo(String videoUrl) async {
    if (_preloadedVideos.containsKey(videoUrl)) return;

    try {
      AppLogger.debug('MediaPreloader: Preloading video $videoUrl');

      final controller = VideoPlayerController.networkUrl(
        Uri.parse(videoUrl),
        videoPlayerOptions: VideoPlayerOptions(
          mixWithOthers: true,
          allowBackgroundPlayback: false,
        ),
        httpHeaders: {'Accept-Ranges': 'bytes', 'Cache-Control': 'no-cache'},
      );

      await controller.initialize();

      // Don't auto-play, just initialize for faster loading later
      await controller.pause();

      _preloadedVideos[videoUrl] = controller;
      AppLogger.debug('MediaPreloader: Successfully preloaded video $videoUrl');

      // Clean up excess video controllers
      _cleanupExcessVideos();
    } catch (e) {
      AppLogger.error(
        'MediaPreloader: Failed to preload video $videoUrl',
        error: e,
      );
    }
  }

  /// Get a preloaded video controller if available
  VideoPlayerController? getPreloadedVideoController(String videoUrl) {
    return _preloadedVideos[videoUrl];
  }

  /// Check if an image has been preloaded
  bool isImagePreloaded(String imageUrl) {
    return _preloadedImages.containsKey(imageUrl);
  }

  /// Clean up old preloaded content that's no longer needed
  void _cleanupOldContent(List<PostModel> posts, int currentIndex) {
    final keepRange = currentIndex - 2; // Keep 2 posts behind current

    // Clean up images
    final imagesToRemove = <String>[];
    for (final imageUrl in _preloadedImages.keys) {
      bool shouldKeep = false;

      for (
        int i = keepRange;
        i < posts.length && i < currentIndex + maxPreloadDistance + 1;
        i++
      ) {
        if (i >= 0 && posts[i].hasMedia) {
          posts[i].getMediaUrl().then((url) {
            if (url == imageUrl) shouldKeep = true;
          });
        }
      }

      if (!shouldKeep) {
        imagesToRemove.add(imageUrl);
      }
    }

    for (final imageUrl in imagesToRemove) {
      _preloadedImages.remove(imageUrl);
      AppLogger.debug('MediaPreloader: Cleaned up preloaded image $imageUrl');
    }

    // Clean up videos
    final videosToRemove = <String>[];
    for (final videoUrl in _preloadedVideos.keys) {
      bool shouldKeep = false;

      for (
        int i = keepRange;
        i < posts.length && i < currentIndex + maxPreloadDistance + 1;
        i++
      ) {
        if (i >= 0 && posts[i].hasMedia) {
          posts[i].getMediaUrl().then((url) {
            if (url == videoUrl) shouldKeep = true;
          });
        }
      }

      if (!shouldKeep) {
        videosToRemove.add(videoUrl);
      }
    }

    for (final videoUrl in videosToRemove) {
      final controller = _preloadedVideos.remove(videoUrl);
      controller?.dispose();
      AppLogger.debug('MediaPreloader: Cleaned up preloaded video $videoUrl');
    }
  }

  /// Clean up excess video controllers to prevent memory issues
  void _cleanupExcessVideos() {
    if (_preloadedVideos.length <= maxCachedVideos) return;

    // Remove oldest video controllers (simple FIFO approach)
    final urlsToRemove = _preloadedVideos.keys.take(
      _preloadedVideos.length - maxCachedVideos,
    );

    for (final url in urlsToRemove) {
      final controller = _preloadedVideos.remove(url);
      controller?.dispose();
      AppLogger.debug(
        'MediaPreloader: Cleaned up excess video controller for $url',
      );
    }
  }

  /// Clear all preloaded content
  void clearAll() {
    _preloadedImages.clear();

    for (final controller in _preloadedVideos.values) {
      controller.dispose();
    }
    _preloadedVideos.clear();
    _currentlyPreloading.clear();

    AppLogger.debug('MediaPreloader: Cleared all preloaded content');
  }

  /// Dispose of the preloader
  void dispose() {
    clearAll();
  }
}
