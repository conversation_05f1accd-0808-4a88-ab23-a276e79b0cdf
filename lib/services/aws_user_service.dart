import 'dart:developer' as developer;
import '../utils/app_logger.dart';
import 'api_service.dart';

/// AWS User Service for handling user profile operations
class AwsUserService {
  static final AwsUserService _instance = AwsUserService._internal();
  static AwsUserService get instance => _instance;
  AwsUserService._internal();

  /// Get user profile by user ID
  Future<Map<String, dynamic>?> getUserProfile(String userId) async {
    try {
      developer.log('AwsUserService: Getting user profile for ID: $userId');
      AppLogger.user('Getting user profile for ID: $userId');

      final response = await ApiService.instance.makeAuthenticatedRequest(
        method: 'GET',
        path: '/users/$userId',
      );

      final data = ApiService.instance.parseResponse(response);

      developer.log('AwsUserService: Successfully retrieved user profile');
      AppLogger.user('Successfully retrieved user profile');

      return data;
    } catch (e) {
      developer.log('AwsUserService: Error getting user profile: $e');
      AppLogger.error('Error getting user profile', error: e);
      return null;
    }
  }

  /// Get user posts by user ID
  Future<List<Map<String, dynamic>>> getUserPosts(
    String userId, {
    int limit = 20,
    int offset = 0,
  }) async {
    try {
      developer.log('AwsUserService: Getting user posts for ID: $userId');
      AppLogger.user('Getting user posts for ID: $userId');

      final response = await ApiService.instance.makeRequest(
        method: 'GET',
        path: '/users/$userId/posts?limit=$limit&offset=$offset',
      );

      final data = ApiService.instance.parseResponse(response);
      final posts = data['posts'] as List<dynamic>? ?? [];

      developer.log(
        'AwsUserService: Successfully retrieved ${posts.length} user posts',
      );
      AppLogger.user('Successfully retrieved ${posts.length} user posts');

      return posts.cast<Map<String, dynamic>>();
    } catch (e) {
      developer.log('AwsUserService: Error getting user posts: $e');
      AppLogger.error('Error getting user posts', error: e);
      return [];
    }
  }

  /// Update current user's profile
  Future<bool> updateUserProfile({
    String? displayName,
    String? username,
    String? bio,
    String? avatarUrl,
    String? firstName,
    String? lastName,
    String? country,
    String? timezone,
    String? language,
  }) async {
    try {
      developer.log('AwsUserService: Updating user profile');
      AppLogger.user('Updating user profile');

      final body = <String, dynamic>{};

      // User table fields
      if (displayName != null) body['display_name'] = displayName;
      if (username != null) body['username'] = username;
      if (bio != null) body['bio'] = bio;
      if (avatarUrl != null) body['avatar_url'] = avatarUrl;

      // User profile table fields
      if (firstName != null) body['first_name'] = firstName;
      if (lastName != null) body['last_name'] = lastName;
      if (country != null) body['country'] = country;
      if (timezone != null) body['timezone'] = timezone;
      if (language != null) body['language'] = language;

      final response = await ApiService.instance.makeAuthenticatedRequest(
        method: 'PUT',
        path: '/users/profile',
        body: body,
      );

      ApiService.instance.parseResponse(response);

      developer.log('AwsUserService: Successfully updated user profile');
      AppLogger.user('Successfully updated user profile');

      return true;
    } catch (e) {
      developer.log('AwsUserService: Error updating user profile: $e');
      AppLogger.error('Error updating user profile', error: e);
      return false;
    }
  }

  /// Get user statistics
  Future<Map<String, int>> getUserStats(String userId) async {
    try {
      developer.log('AwsUserService: Getting user stats for ID: $userId');
      AppLogger.user('Getting user stats for ID: $userId');

      final response = await ApiService.instance.makeAuthenticatedRequest(
        method: 'GET',
        path: '/users/$userId',
      );

      final data = ApiService.instance.parseResponse(response);
      final stats = data['stats'] as Map<String, dynamic>? ?? {};

      final userStats = {
        'posts': stats['posts'] as int? ?? 0,
        'followers': stats['followers'] as int? ?? 0,
        'following': stats['following'] as int? ?? 0,
        'likes': stats['likes'] as int? ?? 0,
      };

      developer.log('AwsUserService: Successfully retrieved user stats');
      AppLogger.user('Successfully retrieved user stats: $userStats');

      return userStats;
    } catch (e) {
      developer.log('AwsUserService: Error getting user stats: $e');
      AppLogger.error('Error getting user stats', error: e);
      return {'posts': 0, 'followers': 0, 'following': 0, 'likes': 0};
    }
  }

  /// Get current user's profile (authenticated user)
  Future<Map<String, dynamic>?> getCurrentUserProfile() async {
    try {
      developer.log('AwsUserService: Getting current user profile');
      AppLogger.user('Getting current user profile');

      final response = await ApiService.instance.makeAuthenticatedRequest(
        method: 'GET',
        path: '/users/profile',
      );

      final data = ApiService.instance.parseResponse(response);

      // Combine user and profile data
      final user = data['user'] as Map<String, dynamic>? ?? {};
      final profile = data['profile'] as Map<String, dynamic>? ?? {};
      final stats = data['stats'] as Map<String, dynamic>? ?? {};
      final linkedAccounts = data['linkedAccounts'] as List<dynamic>? ?? [];

      final combinedInfo = <String, dynamic>{
        ...user,
        ...profile,
        'stats': stats,
        'linkedAccounts': linkedAccounts,
      };

      developer.log(
        'AwsUserService: Successfully retrieved current user profile',
      );
      AppLogger.user('Successfully retrieved current user profile');

      return combinedInfo;
    } catch (e) {
      developer.log('AwsUserService: Error getting current user profile: $e');
      AppLogger.error('Error getting current user profile', error: e);
      return null;
    }
  }

  /// Get basic user info (user + profile combined)
  Future<Map<String, dynamic>?> getUserInfo(String userId) async {
    try {
      developer.log('AwsUserService: Getting user info for ID: $userId');
      AppLogger.user('Getting user info for ID: $userId');

      final response = await ApiService.instance.makeAuthenticatedRequest(
        method: 'GET',
        path: '/users/$userId',
      );

      final data = ApiService.instance.parseResponse(response);

      // Combine user and profile data
      final user = data['user'] as Map<String, dynamic>? ?? {};
      final profile = data['profile'] as Map<String, dynamic>? ?? {};
      final stats = data['stats'] as Map<String, dynamic>? ?? {};
      final linkedAccounts = data['linkedAccounts'] as List<dynamic>? ?? [];

      final combinedInfo = <String, dynamic>{
        ...user,
        ...profile,
        'stats': stats,
        'linkedAccounts': linkedAccounts,
      };

      developer.log('AwsUserService: Successfully retrieved user info');

      AppLogger.user('AwsUserService: Successfully retrieved user info');

      return combinedInfo;
    } catch (e) {
      developer.log('AwsUserService: Error getting user info: $e');
      AppLogger.error('Error getting user info', error: e);
      return null;
    }
  }

  /// Get current user's liked posts
  Future<List<Map<String, dynamic>>> getCurrentUserLikedPosts({
    int limit = 20,
    int offset = 0,
  }) async {
    try {
      developer.log('AwsUserService: Getting current user liked posts');
      AppLogger.user('Getting current user liked posts');

      final response = await ApiService.instance.makeAuthenticatedRequest(
        method: 'GET',
        path: '/users/profile/liked-posts?limit=$limit&offset=$offset',
      );

      final data = ApiService.instance.parseResponse(response);
      final posts = data['posts'] as List<dynamic>? ?? [];

      developer.log(
        'AwsUserService: Successfully retrieved ${posts.length} liked posts',
      );
      AppLogger.user('Successfully retrieved ${posts.length} liked posts');

      return posts.cast<Map<String, dynamic>>();
    } catch (e) {
      developer.log('AwsUserService: Error getting liked posts: $e');
      AppLogger.error('Error getting liked posts', error: e);
      return [];
    }
  }

  /// Follow a user
  Future<bool> followUser(String userId) async {
    try {
      developer.log('AwsUserService: Following user: $userId');
      AppLogger.user('Following user: $userId');

      final response = await ApiService.instance.makeAuthenticatedRequest(
        method: 'POST',
        path: '/users/$userId/follow',
      );

      ApiService.instance.parseResponse(response);

      developer.log('AwsUserService: Successfully followed user: $userId');
      AppLogger.user('Successfully followed user: $userId');

      return true;
    } catch (e) {
      developer.log('AwsUserService: Error following user: $e');
      AppLogger.error('Error following user', error: e);
      return false;
    }
  }

  /// Unfollow a user
  Future<bool> unfollowUser(String userId) async {
    try {
      developer.log('AwsUserService: Unfollowing user: $userId');
      AppLogger.user('Unfollowing user: $userId');

      final response = await ApiService.instance.makeAuthenticatedRequest(
        method: 'DELETE',
        path: '/users/$userId/follow',
      );

      ApiService.instance.parseResponse(response);

      developer.log('AwsUserService: Successfully unfollowed user: $userId');
      AppLogger.user('Successfully unfollowed user: $userId');

      return true;
    } catch (e) {
      developer.log('AwsUserService: Error unfollowing user: $e');
      AppLogger.error('Error unfollowing user', error: e);
      return false;
    }
  }

  /// Check if current user is following another user
  Future<bool> isFollowing(String userId) async {
    try {
      developer.log('AwsUserService: Checking if following user: $userId');
      AppLogger.user('Checking if following user: $userId');

      // Get current user's profile to check following status
      final userInfo = await getUserInfo(userId);
      if (userInfo != null) {
        // The backend should include follow status in user info
        // For now, we'll implement a simple check
        return userInfo['isFollowing'] as bool? ?? false;
      }

      return false;
    } catch (e) {
      developer.log('AwsUserService: Error checking follow status: $e');
      AppLogger.error('Error checking follow status', error: e);
      return false;
    }
  }
}
