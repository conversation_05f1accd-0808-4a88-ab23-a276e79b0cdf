/* GameFlex Mockup - Component Styles */

/* Buttons */
.gf-button {
    width: 100%;
    padding: 16px 24px;
    border: none;
    border-radius: 8px;
    font-size: 16px;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 8px;
    text-decoration: none;
}

.gf-button.primary {
    background: var(--gf-green);
    color: #000;
}

.gf-button.primary:hover {
    background: #20e0b1;
    transform: translateY(-1px);
}

.gf-button.secondary {
    background: transparent;
    color: var(--gf-off-white);
    border: 1px solid var(--gf-gray-border);
}

.gf-button.secondary:hover {
    background: var(--gf-dark-background-40);
    border-color: var(--gf-green);
}

.gf-button.danger {
    background: #e74c3c;
    color: white;
}

.gf-button.danger:hover {
    background: #c0392b;
}

.gf-button:disabled {
    opacity: 0.5;
    cursor: not-allowed;
    transform: none !important;
}

/* Input Fields */
.gf-input {
    width: 100%;
    padding: 16px;
    background: var(--gf-card-background);
    border: 1px solid var(--gf-gray-border);
    border-radius: 8px;
    color: var(--gf-off-white);
    font-size: 16px;
    transition: all 0.3s ease;
}

.gf-input::placeholder {
    color: var(--gf-gray-text);
}

.gf-input:focus {
    outline: none;
    border-color: var(--gf-green);
    box-shadow: 0 0 0 2px rgba(40, 244, 195, 0.2);
}

.input-group {
    margin-bottom: 16px;
}

/* Icons */
.app-icon {
    width: 120px;
    height: 120px;
    object-fit: contain;
}

.app-text-logo {
    width: 200px;
    height: auto;
    object-fit: contain;
}

.icon-container {
    width: 80px;
    height: 80px;
    background: var(--gf-dark-background);
    border-radius: 20px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 40px;
}

/* Validation Indicator */
.validation-indicator {
    display: flex;
    align-items: center;
    gap: 8px;
    margin-top: 8px;
    font-size: 14px;
}

.validation-indicator.available {
    color: var(--gf-green);
}

.validation-indicator.unavailable {
    color: #e74c3c;
}

.validation-indicator.checking {
    color: var(--gf-gray-text);
}

.check-icon {
    font-size: 16px;
}

/* Divider */
.divider {
    display: flex;
    align-items: center;
    margin: 24px 0;
    color: var(--gf-gray-text);
}

.divider::before,
.divider::after {
    content: '';
    flex: 1;
    height: 1px;
    background: var(--gf-gray-border);
}

.divider span {
    padding: 0 16px;
    font-size: 14px;
}

/* Auth Toggle */
.auth-toggle {
    text-align: center;
    margin-top: 24px;
    color: var(--gf-gray-text);
}

.toggle-link {
    color: var(--gf-green);
    text-decoration: none;
    font-weight: 600;
}

.toggle-link:hover {
    text-decoration: underline;
}

/* Navigation Tabs */
.feed-tabs {
    display: flex;
    background: var(--gf-dark-background);
    border-radius: 12px;
    padding: 4px;
    margin: 16px;
}

.tab-button {
    flex: 1;
    padding: 12px 16px;
    background: transparent;
    border: none;
    color: var(--gf-gray-text);
    font-size: 14px;
    font-weight: 600;
    border-radius: 8px;
    cursor: pointer;
    transition: all 0.3s ease;
}

.tab-button.active {
    background: var(--gf-green);
    color: #000;
}

.tab-button:hover:not(.active) {
    color: var(--gf-off-white);
    background: var(--gf-dark-background-40);
}

/* Post Card */
.post-card {
    background: var(--gf-card-background);
    border-radius: 16px;
    margin: 16px;
    overflow: hidden;
    border: 1px solid var(--gf-gray-border);
}

.post-header {
    display: flex;
    align-items: center;
    padding: 16px;
    gap: 12px;
}

.user-avatar {
    width: 40px;
    height: 40px;
    background: linear-gradient(135deg, var(--gf-light-teal), var(--gf-teal));
    border-radius: 50%;
    border: 2px solid var(--gf-green);
}

.user-info {
    display: flex;
    flex-direction: column;
    gap: 2px;
}

.username {
    font-weight: 600;
    color: var(--gf-off-white);
    font-size: 14px;
}

.timestamp {
    color: var(--gf-gray-text);
    font-size: 12px;
}

.post-media {
    aspect-ratio: 16/9;
    background: var(--gf-dark-background);
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 48px;
}

.media-placeholder {
    opacity: 0.5;
}

.post-actions {
    display: flex;
    padding: 16px;
    gap: 24px;
}

.action-btn {
    background: none;
    border: none;
    color: var(--gf-gray-text);
    font-size: 14px;
    cursor: pointer;
    display: flex;
    align-items: center;
    gap: 4px;
    transition: color 0.3s ease;
}

.action-btn:hover {
    color: var(--gf-green);
}

/* Bottom Navigation */
.bottom-nav {
    position: absolute;
    bottom: 0;
    left: 0;
    right: 0;
    height: 50px;
    display: flex;
    background: var(--gf-dark-blue);
    border-top: 0.5px solid var(--gf-gray-border);
    padding: 4px 0;
    padding-bottom: env(safe-area-inset-bottom);
    z-index: 20;
}

.nav-btn {
    flex: 1;
    background: none;
    border: none;
    color: var(--gf-gray-text);
    padding: 8px 16px;
    cursor: pointer;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    justify-content: center;
}

.nav-btn.active {
    color: var(--gf-green);
}

.nav-btn:hover:not(.active) {
    color: var(--gf-off-white);
}

.nav-btn .nav-icon {
    width: 28px;
    height: 28px;
}

/* Middle button (upload) is larger */
.nav-btn:nth-child(2) .nav-icon {
    width: 32px;
    height: 32px;
}

/* Top Navigation */
.top-nav {
    background: var(--gf-dark-background-100);
    padding: 16px 24px;
    border-bottom: 1px solid var(--gf-gray-border);
}

.app-title {
    font-size: 24px;
    font-weight: bold;
    color: var(--gf-off-white);
    text-align: center;
}

/* Screen Titles */
.screen-title {
    font-size: 28px;
    font-weight: bold;
    color: var(--gf-off-white);
    text-align: center;
    margin-bottom: 16px;
}

.screen-subtitle {
    color: var(--gf-gray-text);
    text-align: center;
    line-height: 1.4;
    margin-bottom: 48px;
}