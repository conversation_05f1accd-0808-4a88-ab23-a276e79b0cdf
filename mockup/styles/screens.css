/* GameFlex Mockup - Screen-Specific Styles */

/* Splash Screen - Exact Flutter Implementation */
.splash-screen {
    background: #212121;
    /* Matches Android launch_background.xml #FF212121 */
    display: flex;
    align-items: center;
    justify-content: center;
}

.splash-content {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    width: 100%;
    height: 100%;
}

/* GameFlex Logo Container */
.splash-logo {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 24px;
    /* Matches Flutter SizedBox(height: 24) */
}

/* GameFlex Icon - Teal Circle */
.splash-icon {
    width: 180px;
    /* Matches Flutter SizedBox width: 180 */
    height: 180px;
    /* Matches Flutter SizedBox height: 180 */
    object-fit: contain;
}

/* GameFlex Text Logo */
.splash-text-logo {
    width: 80%;
    /* Matches Flutter 80% of screen width */
    max-width: 300px;
    /* Reasonable max width for larger screens */
    height: auto;
    object-fit: contain;
}

/* Login Screen */
.login-screen {
    background: linear-gradient(to bottom,
            var(--gf-light-teal) 0%,
            var(--gf-teal) 50%,
            var(--gf-dark-blue) 100%);
    padding: 24px;
    display: flex;
    flex-direction: column;
}

.login-content {
    width: 100%;
    height: 100%;
    display: flex;
    flex-direction: column;
}

/* Logo Section - Top Half (flex: 5) */
.logo-section {
    flex: 5;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    gap: 24px;
}

.app-icon-login {
    width: 180px;
    height: 180px;
    object-fit: contain;
}

.app-text-logo-login {
    width: 80%;
    max-width: 300px;
    height: auto;
    object-fit: contain;
}

/* Buttons Section - Bottom Half (flex: 8) */
.buttons-section {
    flex: 8;
    display: flex;
    flex-direction: column;
    gap: 20px;
    padding-top: 20px;
}

/* Login Buttons - Exact Flutter Implementation */
.login-button {
    width: 80%;
    height: 55px;
    margin: 0 auto;
    border: none;
    border-radius: 8px;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 12px;
    font-size: 16px;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.3s ease;
    padding: 12px 16px;
}

.login-button:hover {
    transform: translateY(-1px);
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
}

.button-icon {
    width: 24px;
    height: 24px;
    flex-shrink: 0;
}

/* Email Button - Black background, white text */
.email-button {
    background-color: #000000;
    color: #ffffff;
}

/* Apple Button - White background, black text */
.apple-button {
    background-color: #ffffff;
    color: #000000;
}

/* Xbox Button - Xbox green gradient */
.xbox-button {
    background: linear-gradient(135deg, #107C10 0%, #0E6B0E 100%);
    color: #ffffff;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
}

/* Google Button - White background, black text */
.google-button {
    background-color: #ffffff;
    color: #000000;
}

/* Log in text section */
.login-text-section {
    display: flex;
    justify-content: center;
    margin-top: 24px;
}

.login-text-button {
    background: none;
    border: none;
    color: var(--gf-green);
    font-size: 16px;
    font-weight: 500;
    cursor: pointer;
    padding: 8px 16px;
    border-radius: 4px;
    transition: all 0.3s ease;
}

.login-text-button:hover {
    background: rgba(40, 244, 195, 0.1);
    transform: translateY(-1px);
}

/* Username Selection Screen */
.username-screen {
    background: var(--gf-dark-background-100);
    padding: 24px;
    display: flex;
    align-items: center;
    justify-content: center;
}

.username-content {
    width: 100%;
    max-width: 320px;
    display: flex;
    flex-direction: column;
    align-items: center;
}

.header-icon {
    margin-bottom: 32px;
}

.person-icon {
    color: var(--gf-green);
}

/* Home Screen - Full Screen Feed Layout */
.home-screen {
    background: #000;
    position: relative;
    overflow: hidden;
}

/* Feed Tabs Container */
.feed-tabs-container {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 50px;
    background: var(--gf-dark-blue);
    border-bottom: 0.5px solid var(--gf-gray-border);
    display: flex;
    align-items: center;
    z-index: 15;
    padding-top: env(safe-area-inset-top);
}

.feed-tab {
    flex: 1;
    height: 100%;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    transition: all 0.3s ease;
    position: relative;
}

.feed-tab.active {
    position: relative;
    background: linear-gradient(180deg,
            rgba(40, 244, 195, 0.05) 0%,
            rgba(40, 244, 195, 0.02) 50%,
            transparent 100%);
}

.feed-tab.active::after {
    content: '';
    position: absolute;
    bottom: 0;
    left: 0;
    right: 0;
    height: 3px;
    background: var(--gf-green);
    border-radius: 1.5px 1.5px 0 0;
    box-shadow: 0 0 8px rgba(40, 244, 195, 0.4);
}

.feed-tab.active .tab-text {
    color: var(--gf-green);
    font-weight: 600;
}

.tab-text {
    color: var(--gf-gray-text);
    font-size: 16px;
    font-weight: normal;
    transition: all 0.3s ease;
}

.feed-tab:hover:not(.active) .tab-text {
    color: var(--gf-off-white);
}

.tab-separator {
    width: 1px;
    height: 20px;
    background: var(--gf-gray-border);
}

/* Full Screen Media Container */
.feed-media-container {
    position: absolute;
    top: 50px;
    /* Account for feed tabs */
    left: 0;
    width: 100%;
    height: calc(100% - 50px);
    /* Subtract tabs height */
    z-index: 1;
}

.feed-media-image {
    width: 100%;
    height: 100%;
    object-fit: cover;
    object-position: center;
}

/* Top Overlay - User Info */
.feed-top-overlay {
    position: absolute;
    top: 70px;
    /* Below the feed tabs (50px + 20px margin) */
    left: 16px;
    right: 80px;
    /* Leave space for right nav */
    z-index: 10;
    display: flex;
    flex-direction: column;
    gap: 8px;
}

.user-info-section {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
}

.user-info-overlay {
    display: flex;
    align-items: center;
    gap: 12px;
}

.post-content-overlay {
    margin-top: 8px;
}

.user-avatar-overlay {
    width: 40px;
    height: 40px;
    background: linear-gradient(135deg, var(--gf-light-teal), var(--gf-teal));
    border: 2px solid var(--gf-green);
    border-radius: 50%;
}

.user-details {
    display: flex;
    flex-direction: column;
    gap: 2px;
}

.username-overlay {
    color: white;
    font-size: 16px;
    font-weight: 600;
    text-shadow: 0 1px 3px rgba(0, 0, 0, 0.7);
}

.timestamp-overlay {
    color: rgba(255, 255, 255, 0.8);
    font-size: 12px;
    text-shadow: 0 1px 3px rgba(0, 0, 0, 0.7);
}

.more-options-btn {
    background: rgba(0, 0, 0, 0.4);
    border: none;
    color: white;
    width: 32px;
    height: 32px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 18px;
    cursor: pointer;
    transition: background 0.3s ease;
}

.more-options-btn:hover {
    background: rgba(0, 0, 0, 0.6);
}

/* Bottom Overlay - Reactions and Actions */
.feed-bottom-overlay {
    position: absolute;
    bottom: 80px;
    /* Above bottom nav */
    left: 16px;
    right: 16px;
    z-index: 10;
    display: flex;
    flex-direction: column;
    gap: 12px;
}

/* Reaction Bar */
.reaction-bar {
    display: flex;
    align-items: center;
    gap: 8px;
    flex-wrap: wrap;
}

.reaction-chip {
    background: rgba(0, 0, 0, 0.6);
    border: 1px solid rgba(255, 255, 255, 0.2);
    border-radius: 16px;
    padding: 6px 10px;
    display: flex;
    align-items: center;
    gap: 4px;
    cursor: pointer;
    transition: all 0.3s ease;
}

.reaction-chip:hover {
    border-color: var(--gf-green);
    background: rgba(0, 0, 0, 0.8);
}

.reaction-emoji {
    font-size: 16px;
}

.reaction-count {
    color: white;
    font-size: 12px;
    font-weight: 600;
}

.add-reaction-btn {
    background: rgba(0, 0, 0, 0.4);
    border: 1px solid rgba(255, 255, 255, 0.2);
    border-radius: 12px;
    padding: 4px 8px;
    display: flex;
    align-items: center;
    gap: 4px;
    cursor: pointer;
    transition: all 0.3s ease;
}

.add-reaction-btn:hover {
    border-color: var(--gf-green);
    background: rgba(0, 0, 0, 0.6);
}

.reaction-icon {
    width: 16px;
    height: 16px;
    color: white;
}

.reaction-text {
    color: white;
    font-size: 12px;
    font-weight: 500;
}

/* Action Buttons Row */
.action-buttons-row {
    display: flex;
    justify-content: flex-end;
    align-items: center;
}

.right-actions {
    display: flex;
    gap: 16px;
    align-items: center;
}

.post-description {
    color: white;
    font-size: 14px;
    line-height: 1.4;
    text-shadow: 0 1px 3px rgba(0, 0, 0, 0.7);
    max-lines: 2;
    overflow: hidden;
    text-overflow: ellipsis;
}

.action-button {
    background: none;
    border: none;
    color: white;
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 4px;
    cursor: pointer;
    transition: all 0.3s ease;
    padding: 4px;
}

.action-button:hover {
    transform: scale(1.1);
}

.action-button.shield-active {
    color: var(--gf-green);
}

.action-icon {
    width: 24px;
    height: 24px;
    filter: drop-shadow(0 1px 3px rgba(0, 0, 0, 0.7));
}

.action-count {
    font-size: 12px;
    font-weight: 600;
    text-shadow: 0 1px 3px rgba(0, 0, 0, 0.7);
}

/* Right Side Navigation */
.feed-right-nav {
    position: absolute;
    right: 16px;
    top: 50%;
    transform: translateY(-50%);
    z-index: 10;
}

.nav-tab-btn {
    background: rgba(0, 0, 0, 0.4);
    border: 1px solid rgba(255, 255, 255, 0.2);
    border-radius: 50%;
    width: 48px;
    height: 48px;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    transition: all 0.3s ease;
}

.nav-tab-btn:hover {
    background: rgba(0, 0, 0, 0.6);
    border-color: var(--gf-green);
}

.nav-tab-btn .nav-icon {
    width: 24px;
    height: 24px;
    color: white;
}

/* Upload Screen - Exact Flutter Implementation */
.upload-screen {
    background: var(--gf-dark-blue);
    display: flex;
    flex-direction: column;
    padding: 0;
}

.upload-content {
    flex: 1;
    padding: 24px;
    display: flex;
    flex-direction: column;
    gap: 32px;
    overflow-y: auto;
}

/* Upload Sections */
.upload-section {
    display: flex;
    flex-direction: column;
    gap: 8px;
}

.upload-section-title {
    color: var(--gf-off-white);
    font-size: 20px;
    font-weight: bold;
    margin: 0;
    line-height: 1.2;
}

.upload-section-subtitle {
    color: var(--gf-gray-text);
    font-size: 14px;
    line-height: 1.4;
    margin: 0 0 20px 0;
}

.upload-section-description {
    color: var(--gf-gray-text);
    font-size: 14px;
    line-height: 1.4;
    margin: 0;
}

/* Platform Cards Row */
.platform-cards-row {
    display: flex;
    justify-content: space-evenly;
    gap: 16px;
    margin-top: 20px;
}

.platform-card {
    width: 80px;
    height: 80px;
    background: var(--gf-card-background);
    border: 1px solid var(--gf-gray-border);
    border-radius: 12px;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    gap: 4px;
    cursor: pointer;
    transition: all 0.3s ease;
}

.platform-card:hover {
    border-color: var(--gf-green);
    transform: translateY(-2px);
}

.platform-icon {
    width: 32px;
    height: 32px;
    color: var(--gf-gray-text);
}

.platform-name {
    color: var(--gf-gray-text);
    font-size: 10px;
    font-weight: 500;
    text-align: center;
}

/* Phone Library Card */
.phone-library-card {
    background: var(--gf-card-background);
    border: 1px solid var(--gf-gray-border);
    border-radius: 12px;
    padding: 16px;
    display: flex;
    align-items: center;
    gap: 16px;
    cursor: pointer;
    transition: all 0.3s ease;
}

.phone-library-card:hover {
    border-color: var(--gf-green);
    transform: translateY(-1px);
}

.library-icon-container {
    width: 40px;
    height: 40px;
    background: var(--gf-dark-background-40);
    border-radius: 8px;
    display: flex;
    align-items: center;
    justify-content: center;
}

.library-icon {
    width: 24px;
    height: 24px;
    color: var(--gf-off-white);
}

.library-content {
    flex: 1;
    display: flex;
    flex-direction: column;
}

.library-title {
    color: var(--gf-off-white);
    font-size: 16px;
    font-weight: 600;
}

.library-arrow {
    width: 16px;
    height: 16px;
    color: var(--gf-gray-text);
}

/* App Logo at Bottom */
.upload-logo-container {
    display: flex;
    justify-content: center;
    padding: 20px 0;
}

.upload-app-logo {
    width: 60px;
    height: 60px;
    opacity: 0.7;
    transition: all 0.3s ease;
    cursor: pointer;
}

.upload-app-logo:hover {
    opacity: 1;
    transform: scale(1.05);
}

/* Profile Screen - Exact Flutter Implementation */
.profile-screen {
    background: var(--gf-dark-blue);
    display: flex;
    flex-direction: column;
    padding: 0;
}

.profile-content {
    flex: 1;
    padding: 24px;
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 24px;
    overflow-y: auto;
}

/* Profile Avatar - Large 120px */
.profile-avatar-container {
    width: 120px;
    height: 120px;
    background: linear-gradient(135deg, var(--gf-light-teal), var(--gf-teal));
    border: 3px solid var(--gf-green);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-top: 20px;
}

.profile-avatar-icon {
    width: 60px;
    height: 60px;
    color: var(--gf-dark-blue);
}

/* User Display Name */
.profile-display-name {
    font-size: 28px;
    font-weight: bold;
    color: var(--gf-off-white);
    text-align: center;
    margin: 0;
    line-height: 1.2;
}

/* User Email */
.profile-email {
    color: var(--gf-gray-text);
    font-size: 16px;
    text-align: center;
    margin: 0;
    margin-top: 8px;
}

/* Linked Accounts Section */
.linked-accounts-section {
    margin-top: 16px;
    display: flex;
    flex-direction: column;
    gap: 8px;
}

.linked-account-item {
    display: flex;
    align-items: center;
    gap: 12px;
    padding: 12px 16px;
    background: var(--gf-card-background);
    border: 1px solid var(--gf-gray-border);
    border-radius: 12px;
    transition: all 0.3s ease;
}

.linked-account-item:hover {
    border-color: var(--gf-green);
}

/* Xbox Icon Container - Matches Flutter styling */
.linked-account-icon-container {
    width: 28px;
    height: 28px;
    background: #107C10;
    /* Xbox green background */
    border-radius: 6px;
    display: flex;
    align-items: center;
    justify-content: center;
    flex-shrink: 0;
}

.xbox-icon {
    width: 20px;
    height: 20px;
    color: white;
    /* White Xbox logo on green background */
}

.linked-account-info {
    display: flex;
    flex-direction: column;
    gap: 4px;
    flex: 1;
}

.linked-account-header {
    display: flex;
    align-items: center;
    gap: 8px;
}

.linked-account-platform {
    color: var(--gf-off-white);
    font-size: 14px;
    font-weight: 600;
    line-height: 1;
}

/* "Linked" Badge - Matches Flutter styling */
.linked-badge {
    background: rgba(40, 244, 195, 0.2);
    /* 20% opacity GameFlex green */
    color: var(--gf-green);
    font-size: 10px;
    font-weight: 500;
    padding: 2px 6px;
    border-radius: 4px;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.linked-account-username {
    color: rgba(255, 255, 255, 0.8);
    /* 80% opacity white */
    font-size: 13px;
    line-height: 1;
}

/* Profile Stats Container */
.profile-stats-container {
    width: 100%;
    background: var(--gf-dark-background-40);
    border: 1px solid rgba(40, 244, 195, 0.3);
    border-radius: 16px;
    padding: 20px;
    margin-top: 32px;
}

.stats-row {
    display: flex;
    justify-content: space-around;
    align-items: center;
}

.stats-row:first-child {
    margin-bottom: 20px;
}

.stat-item {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 4px;
}

.stat-number {
    color: var(--gf-off-white);
    font-size: 20px;
    font-weight: bold;
    line-height: 1;
}

.stat-label {
    color: var(--gf-gray-text);
    font-size: 12px;
    font-weight: 500;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

/* Account Information Section */
.account-info-section {
    width: 100%;
    background: var(--gf-dark-background-40);
    border: 1px solid var(--gf-gray-border);
    border-radius: 16px;
    padding: 20px;
    margin-top: 32px;
}

.account-info-title {
    color: var(--gf-off-white);
    font-size: 18px;
    font-weight: bold;
    margin: 0 0 16px 0;
    line-height: 1.2;
}

.info-rows {
    display: flex;
    flex-direction: column;
    gap: 12px;
}

.info-row {
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.info-label {
    color: var(--gf-gray-text);
    font-size: 14px;
    font-weight: 500;
}

.info-value {
    color: var(--gf-off-white);
    font-size: 14px;
    font-weight: 600;
    text-align: right;
}

/* Xbox Media Browser Screen - Exact Flutter Implementation */
.xbox-screen {
    background: var(--gf-dark-background-100);
    display: flex;
    flex-direction: column;
}

/* App Bar - Matches Flutter AppBar */
.xbox-app-bar {
    background: var(--gf-dark-background-100);
    border-bottom: 1px solid var(--gf-gray-border);
}

.app-bar-content {
    height: 56px;
    /* Standard AppBar height */
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 0 16px;
}

.back-button,
.refresh-button {
    background: none;
    border: none;
    color: var(--gf-off-white);
    cursor: pointer;
    padding: 8px;
    border-radius: 50%;
    transition: all 0.3s ease;
}

.back-button:hover,
.refresh-button:hover {
    background: rgba(255, 255, 255, 0.1);
}

.back-icon,
.refresh-icon {
    width: 24px;
    height: 24px;
}

.app-bar-title {
    color: var(--gf-off-white);
    font-size: 20px;
    font-weight: 500;
    margin: 0;
    flex: 1;
    text-align: center;
}

/* Tab Bar - Matches Flutter TabBar */
.xbox-tab-bar {
    display: flex;
    height: 48px;
    /* Standard TabBar height */
}

.xbox-tab {
    flex: 1;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    transition: all 0.3s ease;
    position: relative;
    background: none;
    border: none;
}

.xbox-tab .tab-text {
    color: var(--gf-gray-text);
    font-size: 14px;
    font-weight: 500;
    transition: color 0.3s ease;
}

.xbox-tab.active .tab-text {
    color: var(--gf-green);
    font-weight: 600;
}

.xbox-tab.active::after {
    content: '';
    position: absolute;
    bottom: 0;
    left: 0;
    right: 0;
    height: 2px;
    background: var(--gf-green);
}

.xbox-tab:hover:not(.active) .tab-text {
    color: var(--gf-off-white);
}

/* Content Area */
.xbox-content {
    flex: 1;
    padding: 16px;
    overflow-y: auto;
}

/* Media Grid - 2 columns like Flutter */
.xbox-media-grid {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: 12px;
    /* Matches Flutter crossAxisSpacing/mainAxisSpacing */
}

/* Media Item - Matches Flutter XboxMediaItemWidget */
.xbox-media-item {
    background: var(--gf-card-background);
    border: 1px solid var(--gf-gray-border);
    border-radius: 12px;
    overflow: hidden;
    cursor: pointer;
    transition: all 0.3s ease;
    aspect-ratio: 1;
    /* Square aspect ratio */
}

.xbox-media-item:hover {
    border-color: var(--gf-green);
    transform: translateY(-2px);
}

.xbox-media-item.selected {
    border-color: var(--gf-green);
    border-width: 3px;
}

/* Media Thumbnail Container */
.media-thumbnail {
    position: relative;
    width: 100%;
    height: 70%;
    /* 70% for thumbnail, 30% for details */
    overflow: hidden;
}

.thumbnail-image {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.thumbnail-placeholder {
    width: 100%;
    height: 100%;
    background: var(--gf-dark-background-40);
    display: flex;
    align-items: center;
    justify-content: center;
}

.placeholder-icon {
    width: 48px;
    height: 48px;
    color: var(--gf-gray-text);
}

/* Media Type Indicator - Top Left Corner */
.media-type-indicator {
    position: absolute;
    top: 8px;
    left: 8px;
    background: rgba(0, 0, 0, 0.7);
    border-radius: 4px;
    padding: 2px 6px;
    display: flex;
    align-items: center;
    gap: 4px;
}

.media-type-indicator.video {
    padding: 2px 6px;
}

.type-icon {
    width: 12px;
    height: 12px;
    color: white;
}

.duration {
    color: white;
    font-size: 10px;
    font-weight: 500;
}

/* Download Status - Top Right Corner */
.download-status {
    position: absolute;
    top: 8px;
    right: 8px;
    width: 20px;
    height: 20px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
}

.download-status.downloaded {
    background: var(--gf-green);
}

.download-icon {
    width: 12px;
    height: 12px;
    color: white;
}

/* Media Details - Bottom Section */
.media-details {
    padding: 12px;
    height: 30%;
    display: flex;
    flex-direction: column;
    justify-content: center;
    gap: 4px;
}

.media-title {
    color: var(--gf-off-white);
    font-size: 14px;
    font-weight: 600;
    line-height: 1.2;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
}

.media-game {
    color: var(--gf-gray-text);
    font-size: 12px;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
}

/* Download Action Bar */
.xbox-action-bar {
    padding: 16px;
    background: var(--gf-dark-background-100);
    border-top: 1px solid var(--gf-gray-border);
}

.download-button {
    width: 100%;
    background: var(--gf-green);
    color: var(--gf-dark-blue);
    border: none;
    border-radius: 8px;
    padding: 12px 24px;
    font-size: 16px;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
}

.download-button:hover {
    background: var(--gf-light-teal);
    transform: translateY(-1px);
}

/* Channels Screen - Exact Flutter Implementation */
.channels-screen {
    background: var(--gf-dark-blue);
    display: flex;
    flex-direction: column;
    padding: 0;
}

.channels-content {
    flex: 1;
    padding: 20px 20px 16px 20px;
    /* Matches Flutter SliverPadding */
    overflow-y: auto;
    margin-top: 50px;
    /* Account for feed tabs */
}

.channels-grid {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: 16px;
    /* Matches Flutter crossAxisSpacing and mainAxisSpacing */
}

/* Channel Card - Matches Flutter ChannelCard */
.channel-card {
    aspect-ratio: 1;
    /* Square aspect ratio like Flutter */
    border-radius: 20px;
    /* Matches Flutter BorderRadius.circular(20) */
    position: relative;
    overflow: hidden;
    cursor: pointer;
    transition: all 0.3s ease;
}

.channel-card:hover {
    transform: scale(1.02);
}

/* Channel Background - Gradient Layer */
.channel-background {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    z-index: 1;
}

/* Channel Content - Overlaid on background */
.channel-content {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: 16px;
    z-index: 2;
}

/* Channel Icon Container */
.channel-icon {
    width: 48px;
    height: 48px;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-bottom: 12px;
}

.channel-icon-svg {
    width: 32px;
    height: 32px;
    color: white;
    filter: drop-shadow(0 2px 4px rgba(0, 0, 0, 0.3));
}

/* Channel Info */
.channel-info {
    text-align: center;
    display: flex;
    flex-direction: column;
    gap: 4px;
}

.channel-name {
    color: white;
    font-size: 16px;
    font-weight: 600;
    text-shadow: 0 1px 3px rgba(0, 0, 0, 0.5);
    line-height: 1.2;
}

.channel-members {
    color: rgba(255, 255, 255, 0.8);
    font-size: 12px;
    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.5);
}

/* Join Button - Top Right Corner */
.join-button {
    position: absolute;
    top: 8px;
    right: 8px;
    background: rgba(0, 0, 0, 0.5);
    /* 50% opacity black */
    color: var(--gf-green);
    font-size: 12px;
    font-weight: 600;
    padding: 6px 12px;
    border-radius: 16px;
    z-index: 3;
    transition: all 0.3s ease;
}

.join-button:hover {
    background: rgba(0, 0, 0, 0.7);
    transform: scale(1.05);
}

/* Channel-specific gradient backgrounds */
.channel-card.minecraft .channel-background {
    background: linear-gradient(135deg,
            rgba(76, 175, 80, 0.9) 0%,
            rgba(46, 125, 50, 0.7) 100%);
}

.channel-card.fortnite .channel-background {
    background: linear-gradient(135deg,
            rgba(156, 39, 176, 0.9) 0%,
            rgba(106, 27, 154, 0.7) 100%);
}

.channel-card.valorant .channel-background {
    background: linear-gradient(135deg,
            rgba(244, 67, 54, 0.9) 0%,
            rgba(198, 40, 40, 0.7) 100%);
}

.channel-card.apex .channel-background {
    background: linear-gradient(135deg,
            rgba(255, 152, 0, 0.9) 0%,
            rgba(245, 124, 0, 0.7) 100%);
}

.channel-card.cod .channel-background {
    background: linear-gradient(135deg,
            rgba(96, 125, 139, 0.9) 0%,
            rgba(69, 90, 100, 0.7) 100%);
}

.channel-card.general .channel-background {
    background: linear-gradient(135deg,
            rgba(33, 150, 243, 0.9) 0%,
            rgba(25, 118, 210, 0.7) 100%);
}

/* Post Composition Screen - Exact Flutter Implementation */
.compose-screen {
    background: var(--gf-dark-blue);
    display: flex;
    flex-direction: column;
}

/* App Bar - Matches Flutter AppBar */
.compose-app-bar {
    background: var(--gf-dark-blue);
    border-bottom: 1px solid var(--gf-gray-border);
}

.compose-app-bar .app-bar-content {
    height: 56px;
    /* Standard AppBar height */
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 0 16px;
}

.compose-app-bar .back-button {
    background: none;
    border: none;
    color: var(--gf-off-white);
    cursor: pointer;
    padding: 8px;
    border-radius: 50%;
    transition: all 0.3s ease;
}

.compose-app-bar .back-button:hover {
    background: rgba(255, 255, 255, 0.1);
}

.compose-app-bar .back-icon {
    width: 24px;
    height: 24px;
}

.compose-app-bar .app-bar-title {
    color: var(--gf-green);
    font-size: 20px;
    font-weight: bold;
    margin: 0;
    flex: 1;
    text-align: center;
}

.post-button {
    background: none;
    border: none;
    color: var(--gf-green);
    font-size: 16px;
    font-weight: bold;
    cursor: pointer;
    padding: 8px 12px;
    border-radius: 6px;
    transition: all 0.3s ease;
}

.post-button:hover {
    background: rgba(40, 244, 195, 0.1);
}

.post-button:disabled {
    color: var(--gf-gray-text);
    cursor: not-allowed;
}

/* Content Area */
.compose-content {
    flex: 1;
    padding: 16px;
    overflow-y: auto;
    display: flex;
    flex-direction: column;
    gap: 16px;
}

/* Caption Section - At Top */
.caption-section {
    display: flex;
    flex-direction: column;
    gap: 12px;
}

.caption-label {
    color: var(--gf-off-white);
    font-size: 18px;
    font-weight: bold;
}

.caption-input-container {
    position: relative;
    background: var(--gf-card-background);
    border: 1px solid var(--gf-gray-border);
    border-radius: 12px;
    padding: 16px;
    min-height: 120px;
    /* Matches Flutter constraints */
}

.caption-input {
    width: 100%;
    background: none;
    border: none;
    color: var(--gf-off-white);
    font-size: 16px;
    resize: none;
    outline: none;
    min-height: 80px;
    font-family: inherit;
}

.caption-input::placeholder {
    color: var(--gf-gray-text);
}

.character-count {
    position: absolute;
    bottom: 8px;
    right: 12px;
    color: var(--gf-gray-text);
    font-size: 12px;
}

.clear-button {
    position: absolute;
    top: 12px;
    right: 12px;
    background: none;
    border: none;
    color: var(--gf-gray-text);
    cursor: pointer;
    padding: 4px;
    border-radius: 50%;
    transition: all 0.3s ease;
}

.clear-button:hover {
    background: rgba(255, 255, 255, 0.1);
}

.clear-icon {
    width: 18px;
    height: 18px;
}

/* Media Preview Section */
.media-preview-section {
    flex: 1;
    display: flex;
    flex-direction: column;
}

.media-preview-container {
    flex: 1;
    border: 1px solid var(--gf-gray-border);
    border-radius: 12px;
    overflow: hidden;
    background: var(--gf-card-background);
    min-height: 200px;
}

.preview-media {
    width: 100%;
    height: 100%;
    object-fit: contain;
    background: var(--gf-dark-background);
}

/* Channel Selection Section */
.channel-selection-section {
    margin-top: auto;
    /* Push to bottom */
}

.channel-selection-widget {
    background: var(--gf-card-background);
    border: 1px solid var(--gf-gray-border);
    border-radius: 12px;
    padding: 16px;
}

.channel-selection-header {
    display: flex;
    align-items: center;
    gap: 12px;
    cursor: pointer;
    transition: all 0.3s ease;
}

.channel-selection-header:hover {
    background: rgba(255, 255, 255, 0.05);
    border-radius: 8px;
    margin: -8px;
    padding: 8px;
}

.channel-icon {
    width: 24px;
    height: 24px;
    color: var(--gf-green);
    flex-shrink: 0;
}

.channel-selection-text {
    flex: 1;
    display: flex;
    flex-direction: column;
    gap: 2px;
}

.channel-selection-title {
    color: var(--gf-off-white);
    font-size: 16px;
    font-weight: 600;
    line-height: 1.2;
}

.channel-selection-subtitle {
    color: var(--gf-gray-text);
    font-size: 14px;
    line-height: 1.2;
}

.change-channel-button {
    background: none;
    border: none;
    color: var(--gf-gray-text);
    cursor: pointer;
    padding: 4px;
    border-radius: 50%;
    transition: all 0.3s ease;
}

.change-channel-button:hover {
    background: rgba(255, 255, 255, 0.1);
    color: var(--gf-off-white);
}

.change-icon {
    width: 20px;
    height: 20px;
}

.caption-input::placeholder {
    color: var(--gf-gray-text);
}

.caption-input:focus {
    outline: none;
    border-color: var(--gf-green);
    box-shadow: 0 0 0 2px rgba(40, 244, 195, 0.2);
}

.channel-selection {
    margin: 16px;
    background: var(--gf-card-background);
    border: 1px solid var(--gf-gray-border);
    border-radius: 12px;
    padding: 16px;
}

.selection-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 12px;
}

.selection-label {
    color: var(--gf-off-white);
    font-size: 14px;
    font-weight: 600;
}

.selection-toggle {
    background: none;
    border: none;
    color: var(--gf-green);
    font-size: 16px;
    cursor: pointer;
}

.selected-channel {
    display: flex;
    align-items: center;
    gap: 12px;
}

.post-options {
    padding: 16px;
    display: flex;
    flex-direction: column;
    gap: 16px;
}

.option-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.option-label {
    color: var(--gf-off-white);
    font-size: 16px;
}

.toggle-switch {
    width: 44px;
    height: 24px;
    background: var(--gf-gray-border);
    border-radius: 12px;
    position: relative;
    cursor: pointer;
    transition: all 0.3s ease;
}

.toggle-switch::after {
    content: '';
    position: absolute;
    top: 2px;
    left: 2px;
    width: 20px;
    height: 20px;
    background: white;
    border-radius: 50%;
    transition: all 0.3s ease;
}

.toggle-switch.active {
    background: var(--gf-green);
}

.toggle-switch.active::after {
    transform: translateX(20px);
}

/* Responsive adjustments for screens */
@media (max-width: 480px) {
    .platform-grid {
        grid-template-columns: repeat(2, 1fr);
    }

    .media-grid {
        grid-template-columns: 1fr;
    }

    .channels-grid {
        grid-template-columns: 1fr;
    }
}