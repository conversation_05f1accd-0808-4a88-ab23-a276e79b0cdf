/* GameFlex Mockup - Main Styles */

/* CSS Variables for GameFlex Colors */
:root {
    /* Main brand colors */
    --gf-green: #28F4C3;
    --gf-dark-background: rgba(42, 54, 66, 0.6);
    --gf-dark-background-100: #2A3642;
    --gf-dark-background-40: rgba(42, 54, 66, 0.4);

    /* Text colors */
    --gf-gray-text: rgba(244, 244, 244, 0.5);
    --gf-off-white: #F4F4F4;
    --gf-text-light: #8FAABD;

    /* Additional colors */
    --gf-yellow: #F2DE76;
    --gf-blue: #00D5FF;

    /* Gradient colors */
    --gf-light-teal: #8BE4CF;
    --gf-teal: #2BA5A5;
    --gf-dark-blue: #293642;

    /* Card background */
    --gf-card-background: #1A2530;

    /* Border colors */
    --gf-gray-border: #3A4A5A;

    /* Phone dimensions */
    --phone-width: 375px;
    --phone-height: 812px;
    --phone-border-radius: 40px;
}

/* Reset and Base Styles */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Roboto', -apple-system, BlinkMacSystemFont, 'Segoe UI', sans-serif;
    background: linear-gradient(135deg, var(--gf-dark-blue) 0%, var(--gf-dark-background-100) 100%);
    color: var(--gf-off-white);
    min-height: 100vh;
    overflow-x: auto;
}

/* Mockup Container */
.mockup-container {
    min-height: 100vh;
    display: flex;
    flex-direction: column;
}

/* Navigation */
.mockup-nav {
    background: var(--gf-dark-background-100);
    padding: 1rem 2rem;
    display: flex;
    justify-content: space-between;
    align-items: center;
    border-bottom: 1px solid var(--gf-gray-border);
    position: sticky;
    top: 0;
    z-index: 100;
}

.nav-brand {
    display: flex;
    align-items: center;
    gap: 1rem;
}

.brand-logo {
    height: 32px;
    width: auto;
}

.nav-title {
    font-size: 1.25rem;
    font-weight: 600;
    color: var(--gf-off-white);
}

.nav-links {
    display: flex;
    gap: 2rem;
}

.nav-link {
    color: var(--gf-text-light);
    text-decoration: none;
    padding: 0.5rem 1rem;
    border-radius: 8px;
    transition: all 0.3s ease;
}

.nav-link:hover {
    color: var(--gf-green);
    background: var(--gf-dark-background-40);
}

/* Main Content */
.mockup-main {
    flex: 1;
    padding: 2rem;
}

/* Overview Section */
.overview-section {
    text-align: center;
    margin-bottom: 4rem;
    padding: 2rem;
    background: var(--gf-card-background);
    border-radius: 20px;
    border: 1px solid var(--gf-gray-border);
}

.main-title {
    font-size: 3rem;
    font-weight: bold;
    color: var(--gf-green);
    margin-bottom: 1rem;
    background: linear-gradient(135deg, var(--gf-green), var(--gf-light-teal));
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.main-description {
    font-size: 1.25rem;
    color: var(--gf-text-light);
    margin-bottom: 2rem;
    max-width: 600px;
    margin-left: auto;
    margin-right: auto;
    line-height: 1.6;
}

.overview-stats {
    display: flex;
    justify-content: center;
    gap: 3rem;
    margin-top: 2rem;
}

.stat-item {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 0.5rem;
}

.stat-number {
    font-size: 2rem;
    font-weight: bold;
    color: var(--gf-green);
}

.stat-label {
    font-size: 0.875rem;
    color: var(--gf-gray-text);
    text-transform: uppercase;
    letter-spacing: 0.05em;
}

/* Screen Grid */
.screen-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
    gap: 3rem;
    max-width: 1400px;
    margin: 0 auto;
}

/* Screen Section */
.screen-section {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 1.5rem;
}

.section-title {
    font-size: 1.5rem;
    font-weight: 600;
    color: var(--gf-green);
    text-align: center;
}

/* Phone Mockup */
.phone-mockup {
    position: relative;
    width: var(--phone-width);
    height: var(--phone-height);
    background: #000;
    border-radius: var(--phone-border-radius);
    padding: 8px;
    box-shadow:
        0 0 0 2px #333,
        0 20px 40px rgba(0, 0, 0, 0.3),
        0 10px 20px rgba(0, 0, 0, 0.2);
    cursor: pointer;
    transition: all 0.3s ease;
}

.phone-mockup:hover {
    transform: translateY(-2px);
    box-shadow:
        0 0 0 2px #333,
        0 25px 50px rgba(0, 0, 0, 0.4),
        0 15px 30px rgba(0, 0, 0, 0.3);
}

.phone-mockup::before {
    content: '';
    position: absolute;
    top: 20px;
    left: 50%;
    transform: translateX(-50%);
    width: 60px;
    height: 4px;
    background: #333;
    border-radius: 2px;
    z-index: 10;
}

.phone-mockup::after {
    content: '';
    position: absolute;
    bottom: 8px;
    left: 50%;
    transform: translateX(-50%);
    width: 134px;
    height: 4px;
    background: #333;
    border-radius: 2px;
    z-index: 10;
}

/* Screen Base */
.screen {
    width: 100%;
    height: 100%;
    border-radius: calc(var(--phone-border-radius) - 8px);
    overflow: hidden;
    position: relative;
    display: flex;
    flex-direction: column;
}

/* Loading Spinner */
.loading-spinner {
    width: 40px;
    height: 40px;
    border: 3px solid var(--gf-dark-background);
    border-top: 3px solid var(--gf-green);
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    0% {
        transform: rotate(0deg);
    }

    100% {
        transform: rotate(360deg);
    }
}

/* Responsive Design */
@media (max-width: 768px) {
    .mockup-nav {
        flex-direction: column;
        gap: 1rem;
        padding: 1rem;
    }

    .nav-links {
        flex-wrap: wrap;
        justify-content: center;
        gap: 1rem;
    }

    .screen-grid {
        grid-template-columns: 1fr;
        gap: 2rem;
    }

    .mockup-main {
        padding: 1rem;
    }

    .phone-mockup {
        transform: scale(0.8);
        transform-origin: top center;
    }
}

@media (max-width: 480px) {
    .phone-mockup {
        transform: scale(0.7);
    }

    .nav-title {
        font-size: 1rem;
    }

    .section-title {
        font-size: 1.25rem;
    }

    .main-title {
        font-size: 2rem;
    }

    .main-description {
        font-size: 1rem;
    }

    .overview-stats {
        gap: 1.5rem;
    }

    .stat-number {
        font-size: 1.5rem;
    }
}

/* Fullscreen Functionality */
.fullscreen-indicator {
    position: absolute;
    top: 12px;
    right: 12px;
    width: 24px;
    height: 24px;
    background: rgba(0, 0, 0, 0.7);
    color: white;
    border-radius: 4px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 12px;
    z-index: 20;
    opacity: 0;
    transition: opacity 0.3s ease;
    pointer-events: none;
}

.phone-mockup:hover .fullscreen-indicator {
    opacity: 1;
}

.fullscreen-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100vw;
    height: 100vh;
    background: rgba(0, 0, 0, 0.9);
    z-index: 1000;
    cursor: pointer;
}

.phone-mockup.fullscreen {
    position: fixed;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%) scale(1.2);
    z-index: 1001;
    cursor: default;
    box-shadow:
        0 0 0 2px #333,
        0 40px 80px rgba(0, 0, 0, 0.6),
        0 20px 40px rgba(0, 0, 0, 0.4);
}

.fullscreen-close {
    position: absolute;
    top: -40px;
    right: -40px;
    width: 32px;
    height: 32px;
    background: var(--gf-green);
    color: #000;
    border: none;
    border-radius: 50%;
    font-size: 16px;
    font-weight: bold;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 1002;
    transition: all 0.3s ease;
}

.fullscreen-close:hover {
    background: #20e0b1;
    transform: scale(1.1);
}

.fullscreen-active {
    overflow: hidden;
}

/* Responsive fullscreen */
@media (max-width: 768px) {
    .phone-mockup.fullscreen {
        transform: translate(-50%, -50%) scale(1);
        width: 90vw;
        height: auto;
        max-height: 90vh;
    }
}

@media (max-width: 480px) {
    .phone-mockup.fullscreen {
        transform: translate(-50%, -50%) scale(0.9);
        width: 95vw;
    }

    .fullscreen-close {
        top: -30px;
        right: -30px;
        width: 28px;
        height: 28px;
        font-size: 14px;
    }
}