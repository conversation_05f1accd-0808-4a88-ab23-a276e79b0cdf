<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>GameFlex - Screen Mockups</title>
    <link rel="stylesheet" href="styles/main.css">
    <link rel="stylesheet" href="styles/components.css">
    <link rel="stylesheet" href="styles/screens.css">
    <link rel="icon" href="assets/icons/icon_teal.svg" type="image/svg+xml">
</head>

<body>
    <div class="mockup-container">
        <!-- Navigation -->
        <nav class="mockup-nav">
            <div class="nav-brand">
                <img src="assets/logos/gameflex_text.svg" alt="GameFlex" class="brand-logo">
                <span class="nav-title">Screen Mockups</span>
            </div>
            <div class="nav-links">
                <a href="#splash" class="nav-link">Splash</a>
                <a href="#login" class="nav-link">Login</a>
                <a href="#username" class="nav-link">Username</a>
                <a href="#home" class="nav-link">Home</a>
                <a href="#upload" class="nav-link">Upload</a>
                <a href="#profile" class="nav-link">Profile</a>
                <a href="#channels" class="nav-link">Channels</a>
                <a href="#xbox" class="nav-link">Xbox</a>
                <a href="#compose" class="nav-link">Compose</a>
            </div>
        </nav>

        <!-- Main Content -->
        <main class="mockup-main">
            <!-- Overview Section -->
            <section class="overview-section">
                <h1 class="main-title">GameFlex App Mockup</h1>
                <p class="main-description">
                    Complete HTML/CSS mockup of all GameFlex mobile app screens.
                    Perfect for importing into Figma, Sketch, or other design tools.
                </p>
                <div class="overview-stats">
                    <div class="stat-item">
                        <span class="stat-number">8</span>
                        <span class="stat-label">Screens</span>
                    </div>
                    <div class="stat-item">
                        <span class="stat-number">100%</span>
                        <span class="stat-label">Accurate</span>
                    </div>
                    <div class="stat-item">
                        <span class="stat-number">Mobile</span>
                        <span class="stat-label">Optimized</span>
                    </div>
                </div>
            </section>

            <!-- Screen Grid -->
            <div class="screen-grid">
                <!-- Splash Screen -->
                <section id="splash" class="screen-section">
                    <h2 class="section-title">Splash Screen</h2>
                    <div class="phone-mockup" onclick="toggleFullscreen(this)">
                        <div class="screen splash-screen">
                            <div class="splash-content">
                                <!-- GameFlex Logo -->
                                <div class="splash-logo">
                                    <img src="assets/icons/icon_teal.svg" alt="GameFlex Icon" class="splash-icon">
                                    <img src="assets/logos/gameflex_text.svg" alt="GameFlex" class="splash-text-logo">
                                </div>
                            </div>
                        </div>
                    </div>
                </section>

                <!-- Login Screen -->
                <section id="login" class="screen-section">
                    <h2 class="section-title">Login Screen</h2>
                    <div class="phone-mockup" onclick="toggleFullscreen(this)">
                        <div class="screen login-screen">
                            <div class="login-content">
                                <!-- Logo Section (Top Half) -->
                                <div class="logo-section">
                                    <img src="assets/icons/icon_teal.svg" alt="GameFlex" class="app-icon-login">
                                    <img src="assets/logos/gameflex_text.svg" alt="GameFlex"
                                        class="app-text-logo-login">
                                </div>

                                <!-- Buttons Section (Bottom Half) -->
                                <div class="buttons-section">
                                    <!-- Email Sign Up Button -->
                                    <button class="login-button email-button">
                                        <svg class="button-icon" viewBox="0 0 24 24" fill="currentColor">
                                            <path
                                                d="M20 4H4c-1.1 0-1.99.9-1.99 2L2 18c0 1.1.89 2 2 2h16c1.1 0 2-.9 2-2V6c0-1.1-.9-2-2-2zm0 4l-8 5-8-5V6l8 5 8-5v2z" />
                                        </svg>
                                        <span>Sign up with email</span>
                                    </button>

                                    <!-- Apple Sign In Button -->
                                    <button class="login-button apple-button">
                                        <svg class="button-icon" viewBox="0 0 24 24" fill="currentColor">
                                            <path
                                                d="M18.71 19.5c-.83 1.24-1.71 2.45-3.05 2.47-1.34.03-1.77-.79-3.29-.79-1.53 0-2 .77-3.27.82-1.31.05-2.3-1.32-3.14-2.53C4.25 17 2.94 12.45 4.7 9.39c.87-1.52 2.43-2.48 4.12-2.51 1.28-.02 2.5.87 3.29.87.78 0 2.26-1.07 3.81-.91.65.03 2.47.26 3.64 1.98-.09.06-2.17 1.28-2.15 3.81.03 3.02 2.65 4.03 2.68 4.04-.03.07-.42 1.44-1.38 2.83M13 3.5c.73-.83 1.94-1.46 2.94-1.5.13 1.17-.34 2.35-1.04 3.19-.69.85-1.83 1.51-2.95 1.42-.15-1.15.41-2.35 1.05-3.11z" />
                                        </svg>
                                        <span>Log in with Apple ID</span>
                                    </button>

                                    <!-- Xbox Sign In Button -->
                                    <button class="login-button xbox-button">
                                        <svg class="button-icon" viewBox="0 0 24 24" fill="currentColor">
                                            <!-- Material Design sports_esports icon -->
                                            <path
                                                d="M21.58,16.09l-1.09-7.66C20.21,6.46,18.52,5,16.53,5H7.47C5.48,5,3.79,6.46,3.51,8.43l-1.09,7.66 C2.2,17.63,3.39,19,4.94,19c0.68,0,1.32-0.27,1.8-0.75L9,16h6l2.25,2.25c0.48,0.48,1.13,0.75,1.8,0.75 C20.61,19,21.8,17.63,21.58,16.09z M11,11H9v2H8v-2H6v-1h2V8h1v2h2V11z M15,10c-0.55,0-1-0.45-1-1c0-0.55,0.45-1,1-1 s1,0.45,1,1C16,9.55,15.55,10,15,10z M17,13c-0.55,0-1-0.45-1-1c0-0.55,0.45-1,1-1s1,0.45,1,1C18,12.55,17.55,13,17,13z" />
                                        </svg>
                                        <span>Log in with Xbox</span>
                                    </button>

                                    <!-- Google Sign In Button -->
                                    <button class="login-button google-button">
                                        <svg class="button-icon" viewBox="0 0 24 24" fill="currentColor">
                                            <!-- Material Design g_mobiledata icon -->
                                            <path
                                                d="M12,2C6.48,2,2,6.48,2,12s4.48,10,10,10s10-4.48,10-10S17.52,2,12,2z M14.5,18H9v-1.5h3.75c-0.41-0.68-0.75-1.43-0.75-2.25 c0-2.5,2.25-4.5,5-4.5V8.25c-3.86,0-7,3.14-7,7s3.14,7,7,7V20.5C16.25,19.25,15.25,18.75,14.5,18z" />
                                            <text x="12" y="15" text-anchor="middle" font-size="10" font-weight="bold"
                                                fill="currentColor">G</text>
                                        </svg>
                                        <span>Log in with Google</span>
                                    </button>

                                    <!-- Log in text button -->
                                    <div class="login-text-section">
                                        <button class="login-text-button">Log in</button>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </section>

                <!-- Username Selection Screen -->
                <section id="username" class="screen-section">
                    <h2 class="section-title">Username Selection</h2>
                    <div class="phone-mockup" onclick="toggleFullscreen(this)">
                        <div class="screen username-screen">
                            <div class="username-content">
                                <div class="header-icon">
                                    <div class="icon-container">
                                        <span class="person-icon">👤</span>
                                    </div>
                                </div>

                                <h1 class="screen-title">Choose Your Username</h1>
                                <p class="screen-subtitle">Pick a unique username that others will see.<br>It must be
                                    4-32 characters long.</p>

                                <div class="input-group">
                                    <input type="text" placeholder="Enter your username" class="gf-input">
                                    <div class="validation-indicator available">
                                        <span class="check-icon">✓</span>
                                        <span>Username is available</span>
                                    </div>
                                </div>

                                <button class="gf-button primary">Continue</button>
                            </div>
                        </div>
                    </div>
                </section>

                <!-- Home Screen -->
                <section id="home" class="screen-section">
                    <h2 class="section-title">Home Screen</h2>
                    <div class="phone-mockup" onclick="toggleFullscreen(this)">
                        <div class="screen home-screen">
                            <!-- Feed Tabs - Top Navigation -->
                            <div class="feed-tabs-container">
                                <div class="feed-tab">
                                    <span class="tab-text">Followed</span>
                                </div>
                                <div class="tab-separator"></div>
                                <div class="feed-tab active">
                                    <span class="tab-text">Prime</span>
                                </div>
                                <div class="tab-separator"></div>
                                <div class="feed-tab">
                                    <span class="tab-text">Channels</span>
                                </div>
                            </div>

                            <!-- Full Screen Post Media -->
                            <div class="feed-media-container">
                                <img src="assets/cod_screenshot.jpg" alt="Call of Duty Screenshot"
                                    class="feed-media-image">
                            </div>

                            <!-- Top Overlay - User Info -->
                            <div class="feed-top-overlay">
                                <div class="user-info-section">
                                    <div class="user-info-overlay">
                                        <div class="user-avatar-overlay"></div>
                                        <div class="user-details">
                                            <span class="username-overlay">@codmaster</span>
                                            <span class="timestamp-overlay">2h ago</span>
                                        </div>
                                    </div>
                                    <button class="more-options-btn">⋮</button>
                                </div>

                                <!-- Post Content -->
                                <div class="post-content-overlay">
                                    <span class="post-description">Epic clutch moment! 🎯</span>
                                </div>
                            </div>

                            <!-- Bottom Overlay - Reactions and Actions -->
                            <div class="feed-bottom-overlay">
                                <!-- Reaction Bar -->
                                <div class="reaction-bar">
                                    <div class="reaction-chip">
                                        <span class="reaction-emoji">🔥</span>
                                        <span class="reaction-count">12</span>
                                    </div>
                                    <div class="reaction-chip">
                                        <span class="reaction-emoji">💯</span>
                                        <span class="reaction-count">8</span>
                                    </div>
                                    <button class="add-reaction-btn">
                                        <svg class="reaction-icon" viewBox="0 0 24 24" fill="currentColor">
                                            <path
                                                d="M18 12.998h-5v5a1 1 0 0 1-2 0v-5H6a1 1 0 0 1 0-2h5v-5a1 1 0 0 1 2 0v5h5a1 1 0 0 1 0 2z" />
                                        </svg>
                                        <span class="reaction-text">React</span>
                                    </button>
                                </div>

                                <!-- Action Buttons Row -->
                                <div class="action-buttons-row">
                                    <div class="right-actions">
                                        <button class="action-button">
                                            <svg class="action-icon" viewBox="0 0 24 24" fill="currentColor">
                                                <path
                                                    d="M20 2H4c-1.1 0-2 .9-2 2v12c0 1.1.9 2 2 2h4l4 4 4-4h4c1.1 0 2-.9 2-2V4c0-1.1-.9-2-2-2zm-2 12H6v-2h12v2zm0-3H6V9h12v2zm0-3H6V6h12v2z" />
                                            </svg>
                                            <span class="action-count">23</span>
                                        </button>
                                        <button class="action-button shield-active">
                                            <svg class="action-icon" viewBox="0 0 24 24" fill="currentColor">
                                                <path
                                                    d="M12,1L3,5V11C3,16.55 6.84,21.74 12,23C17.16,21.74 21,16.55 21,11V5L12,1M12,7C13.4,7 14.8,8.6 14.8,10V11.5C14.8,12.4 14.4,13.2 13.7,13.7V16.25C13.7,16.66 13.36,17 12.95,17H11.05C10.64,17 10.3,16.66 10.3,16.25V13.7C9.6,13.2 9.2,12.4 9.2,11.5V10C9.2,8.6 10.6,7 12,7Z" />
                                            </svg>
                                            <span class="action-count">156</span>
                                        </button>
                                        <button class="action-button">
                                            <svg class="action-icon" viewBox="0 0 24 24" fill="currentColor">
                                                <path
                                                    d="M9,5A4,4 0 0,1 13,9A4,4 0 0,1 9,13A4,4 0 0,1 5,9A4,4 0 0,1 9,5M9,15C11.67,15 17,16.34 17,19V21H1V19C1,16.34 6.33,15 9,15M16.76,5.36C18.78,7.56 18.78,10.61 16.76,12.63L15.08,10.94C15.92,9.76 15.92,8.23 15.08,7.05L16.76,5.36M20.07,2C24,6.05 23.97,12.11 20.07,16.07L18.44,14.44C21.21,11.19 21.21,6.65 18.44,3.63L20.07,2Z" />
                                            </svg>
                                            <span class="action-count">42</span>
                                        </button>
                                    </div>
                                </div>
                            </div>

                            <!-- Right Side Navigation -->
                            <div class="feed-right-nav">
                                <button class="nav-tab-btn" title="Reflexes">
                                    <svg class="nav-icon" viewBox="0 0 24 24" fill="currentColor">
                                        <path
                                            d="M12 2l3.09 6.26L22 9.27l-5 4.87 1.18 6.88L12 17.77l-6.18 3.25L7 14.14 2 9.27l6.91-1.01L12 2z" />
                                    </svg>
                                </button>
                            </div>

                            <!-- Bottom Navigation -->
                            <div class="bottom-nav">
                                <button class="nav-btn active">
                                    <svg class="nav-icon" viewBox="0 0 24 24" fill="currentColor">
                                        <path d="M10 20v-6h4v6h5v-8h3L12 3 2 12h3v8z" />
                                    </svg>
                                </button>
                                <button class="nav-btn">
                                    <svg class="nav-icon" viewBox="0 0 24 24" fill="currentColor">
                                        <path d="M19 13h-6v6h-2v-6H5v-2h6V5h2v6h6v2z" />
                                    </svg>
                                </button>
                                <button class="nav-btn">
                                    <svg class="nav-icon" viewBox="0 0 24 24" fill="currentColor">
                                        <path
                                            d="M12 12c2.21 0 4-1.79 4-4s-1.79-4-4-4-4 1.79-4 4 1.79 4 4 4zm0 2c-2.67 0-8 1.34-8 4v2h16v-2c0-2.66-5.33-4-8-4z" />
                                    </svg>
                                </button>
                            </div>
                        </div>
                    </div>
                </section>

                <!-- Upload Screen -->
                <section id="upload" class="screen-section">
                    <h2 class="section-title">Upload Screen</h2>
                    <div class="phone-mockup" onclick="toggleFullscreen(this)">
                        <div class="screen upload-screen">
                            <!-- Main Content -->
                            <div class="upload-content">
                                <!-- Link Gaming Accounts Section -->
                                <div class="upload-section">
                                    <h3 class="upload-section-title">Link Gaming Accounts</h3>
                                    <p class="upload-section-subtitle">Connect your gaming platforms to access your
                                        screenshots and clips.</p>

                                    <div class="platform-cards-row">
                                        <div class="platform-card">
                                            <svg class="platform-icon" viewBox="0 0 24 24" fill="currentColor">
                                                <path
                                                    d="M7.8 2h8.4C19.4 2 22 4.6 22 7.8v8.4a5.8 5.8 0 0 1-5.8 5.8H7.8C4.6 22 2 19.4 2 16.2V7.8A5.8 5.8 0 0 1 7.8 2m-.2 2A3.6 3.6 0 0 0 4 7.6v8.8C4 18.39 5.61 20 7.6 20h8.8a3.6 3.6 0 0 0 3.6-3.6V7.6C20 5.61 18.39 4 16.4 4H7.6m9.65 1.5a1.25 1.25 0 0 1 1.25 1.25A1.25 1.25 0 0 1 17.25 8 1.25 1.25 0 0 1 16 6.75a1.25 1.25 0 0 1 1.25-1.25M12 7a5 5 0 0 1 5 5 5 5 0 0 1-5 5 5 5 0 0 1-5-5 5 5 0 0 1 5-5m0 2a3 3 0 0 0-3 3 3 3 0 0 0 3 3 3 3 0 0 0 3-3 3 3 0 0 0-3-3z" />
                                            </svg>
                                            <span class="platform-name">Xbox</span>
                                        </div>
                                        <div class="platform-card">
                                            <svg class="platform-icon" viewBox="0 0 24 24" fill="currentColor">
                                                <path
                                                    d="M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm-2 15l-5-5 1.41-1.41L10 14.17l7.59-7.59L19 8l-9 9z" />
                                            </svg>
                                            <span class="platform-name">PlayStation</span>
                                        </div>
                                        <div class="platform-card">
                                            <svg class="platform-icon" viewBox="0 0 24 24" fill="currentColor">
                                                <path
                                                    d="M20,18C20.5,18 21,17.5 21,17V7C21,6.5 20.5,6 20,6H4C3.5,6 3,6.5 3,7V17C3,17.5 3.5,18 4,18H20M5,8H19V16H5V8Z" />
                                            </svg>
                                            <span class="platform-name">Steam</span>
                                        </div>
                                    </div>
                                </div>

                                <!-- Linked Libraries Section -->
                                <div class="upload-section">
                                    <h3 class="upload-section-title">Linked Libraries</h3>
                                    <p class="upload-section-description">Access all your linked game libraries and
                                        screenshots.</p>
                                </div>

                                <!-- Phone Library Section -->
                                <div class="upload-section">
                                    <div class="phone-library-card">
                                        <div class="library-icon-container">
                                            <svg class="library-icon" viewBox="0 0 24 24" fill="currentColor">
                                                <path
                                                    d="M9,3V4H4V6H5V19A2,2 0 0,0 7,21H17A2,2 0 0,0 19,19V6H20V4H15V3H9M7,6H17V19H7V6M9,8V17H11V8H9M13,8V17H15V8H13Z" />
                                            </svg>
                                        </div>
                                        <div class="library-content">
                                            <span class="library-title">Phone Library</span>
                                        </div>
                                        <svg class="library-arrow" viewBox="0 0 24 24" fill="currentColor">
                                            <path d="M8.59,16.58L13.17,12L8.59,7.41L10,6L16,12L10,18L8.59,16.58Z" />
                                        </svg>
                                    </div>
                                </div>
                            </div>

                            <!-- App Logo at Bottom -->
                            <div class="upload-logo-container">
                                <img src="assets/icons/icon_teal.svg" alt="GameFlex Logo" class="upload-app-logo">
                            </div>
                        </div>
                    </div>
                </section>

                <!-- Profile Screen -->
                <section id="profile" class="screen-section">
                    <h2 class="section-title">Profile Screen</h2>
                    <div class="phone-mockup" onclick="toggleFullscreen(this)">
                        <div class="screen profile-screen">
                            <div class="profile-content">
                                <!-- Profile Avatar -->
                                <div class="profile-avatar-container">
                                    <svg class="profile-avatar-icon" viewBox="0 0 24 24" fill="currentColor">
                                        <path
                                            d="M12 12c2.21 0 4-1.79 4-4s-1.79-4-4-4-4 1.79-4 4 1.79 4 4 4zm0 2c-2.67 0-8 1.34-8 4v2h16v-2c0-2.66-5.33-4-8-4z" />
                                    </svg>
                                </div>

                                <!-- User Display Name -->
                                <h1 class="profile-display-name">GameFlex User</h1>

                                <!-- User Email -->
                                <p class="profile-email"><EMAIL></p>

                                <!-- Linked Accounts Section -->
                                <div class="linked-accounts-section">
                                    <div class="linked-account-item">
                                        <div class="linked-account-icon-container">
                                            <svg class="xbox-icon" viewBox="0 0 372.36823 372.57281"
                                                fill="currentColor">
                                                <g transform="translate(-1.5706619,12.357467)">
                                                    <path
                                                        d="M186.28613,-12.357467 C83.478516,-12.357467 0,71.121094 0,173.92871 C0,276.73633 83.478516,360.21484 186.28613,360.21484 C289.09375,360.21484 372.57227,276.73633 372.57227,173.92871 C372.57227,71.121094 289.09375,-12.357467 186.28613,-12.357467 Z M186.28613,27.642578 C259.85156,27.642578 320.57227,88.363281 320.57227,161.92871 C320.57227,235.49414 259.85156,296.21484 186.28613,296.21484 C112.7207,296.21484 52,235.49414 52,161.92871 C52,88.363281 112.7207,27.642578 186.28613,27.642578 Z" />
                                                    <path
                                                        d="M186.28613,67.642578 C134.7207,67.642578 92,110.36328 92,161.92871 C92,213.49414 134.7207,256.21484 186.28613,256.21484 C237.85156,256.21484 280.57227,213.49414 280.57227,161.92871 C280.57227,110.36328 237.85156,67.642578 186.28613,67.642578 Z M186.28613,107.64258 C215.85156,107.64258 240.57227,132.36328 240.57227,161.92871 C240.57227,191.49414 215.85156,216.21484 186.28613,216.21484 C156.7207,216.21484 132,191.49414 132,161.92871 C132,132.36328 156.7207,107.64258 186.28613,107.64258 Z" />
                                                </g>
                                            </svg>
                                        </div>
                                        <div class="linked-account-info">
                                            <div class="linked-account-header">
                                                <span class="linked-account-platform">Xbox Live</span>
                                                <span class="linked-badge">Linked</span>
                                            </div>
                                            <span class="linked-account-username">GamerTag123</span>
                                        </div>
                                    </div>
                                </div>

                                <!-- Profile Stats Container -->
                                <div class="profile-stats-container">
                                    <div class="stats-row">
                                        <div class="stat-item">
                                            <span class="stat-number">0</span>
                                            <span class="stat-label">Posts</span>
                                        </div>
                                        <div class="stat-item">
                                            <span class="stat-number">0</span>
                                            <span class="stat-label">Followers</span>
                                        </div>
                                        <div class="stat-item">
                                            <span class="stat-number">0</span>
                                            <span class="stat-label">Following</span>
                                        </div>
                                    </div>
                                    <div class="stats-row">
                                        <div class="stat-item">
                                            <span class="stat-number">0</span>
                                            <span class="stat-label">Likes</span>
                                        </div>
                                        <div class="stat-item">
                                            <span class="stat-number">0</span>
                                            <span class="stat-label">Comments</span>
                                        </div>
                                        <div class="stat-item">
                                            <span class="stat-number">0</span>
                                            <span class="stat-label">Shares</span>
                                        </div>
                                    </div>
                                </div>

                                <!-- Account Information Section -->
                                <div class="account-info-section">
                                    <h3 class="account-info-title">Account Information</h3>
                                    <div class="info-rows">
                                        <div class="info-row">
                                            <span class="info-label">User ID</span>
                                            <span class="info-value">usr_123456789</span>
                                        </div>
                                        <div class="info-row">
                                            <span class="info-label">Email Verified</span>
                                            <span class="info-value">Yes</span>
                                        </div>
                                        <div class="info-row">
                                            <span class="info-label">Member Since</span>
                                            <span class="info-value">Jan 15, 2024</span>
                                        </div>
                                        <div class="info-row">
                                            <span class="info-label">Last Updated</span>
                                            <span class="info-value">Aug 14, 2025</span>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </section>

                <!-- Channels Screen -->
                <section id="channels" class="screen-section">
                    <h2 class="section-title">Channels Screen</h2>
                    <div class="phone-mockup" onclick="toggleFullscreen(this)">
                        <div class="screen channels-screen">
                            <!-- Feed Tabs - Top Navigation -->
                            <div class="feed-tabs-container">
                                <div class="feed-tab">
                                    <span class="tab-text">Followed</span>
                                </div>
                                <div class="tab-separator"></div>
                                <div class="feed-tab">
                                    <span class="tab-text">Prime</span>
                                </div>
                                <div class="tab-separator"></div>
                                <div class="feed-tab active">
                                    <span class="tab-text">Channels</span>
                                </div>
                            </div>

                            <!-- Channels Grid Content -->
                            <div class="channels-content">
                                <div class="channels-grid">
                                    <!-- Minecraft Channel -->
                                    <div class="channel-card minecraft">
                                        <div class="channel-background"></div>
                                        <div class="channel-content">
                                            <div class="channel-icon">
                                                <svg class="channel-icon-svg" viewBox="0 0 24 24" fill="currentColor">
                                                    <path d="M12 2L2 7l10 5 10-5-10-5zM2 17l10 5 10-5M2 12l10 5 10-5" />
                                                </svg>
                                            </div>
                                            <div class="channel-info">
                                                <div class="channel-name">Minecraft</div>
                                                <div class="channel-members">1.2k members</div>
                                            </div>
                                        </div>
                                    </div>

                                    <!-- Fortnite Channel -->
                                    <div class="channel-card fortnite">
                                        <div class="channel-background"></div>
                                        <div class="channel-content">
                                            <div class="channel-icon">
                                                <svg class="channel-icon-svg" viewBox="0 0 24 24" fill="currentColor">
                                                    <path
                                                        d="M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm-2 15l-5-5 1.41-1.41L10 14.17l7.59-7.59L19 8l-9 9z" />
                                                </svg>
                                            </div>
                                            <div class="channel-info">
                                                <div class="channel-name">Fortnite</div>
                                                <div class="channel-members">856 members</div>
                                            </div>
                                        </div>
                                        <div class="join-button">Join</div>
                                    </div>

                                    <!-- Valorant Channel -->
                                    <div class="channel-card valorant">
                                        <div class="channel-background"></div>
                                        <div class="channel-content">
                                            <div class="channel-icon">
                                                <svg class="channel-icon-svg" viewBox="0 0 24 24" fill="currentColor">
                                                    <path
                                                        d="M12 2l3.09 6.26L22 9.27l-5 4.87 1.18 6.88L12 17.77l-6.18 3.25L7 14.14 2 9.27l6.91-1.01L12 2z" />
                                                </svg>
                                            </div>
                                            <div class="channel-info">
                                                <div class="channel-name">Valorant</div>
                                                <div class="channel-members">743 members</div>
                                            </div>
                                        </div>
                                    </div>

                                    <!-- Apex Legends Channel -->
                                    <div class="channel-card apex">
                                        <div class="channel-background"></div>
                                        <div class="channel-content">
                                            <div class="channel-icon">
                                                <svg class="channel-icon-svg" viewBox="0 0 24 24" fill="currentColor">
                                                    <path
                                                        d="M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm-2 15l-5-5 1.41-1.41L10 14.17l7.59-7.59L19 8l-9 9z" />
                                                </svg>
                                            </div>
                                            <div class="channel-info">
                                                <div class="channel-name">Apex Legends</div>
                                                <div class="channel-members">621 members</div>
                                            </div>
                                        </div>
                                        <div class="join-button">Join</div>
                                    </div>

                                    <!-- Call of Duty Channel -->
                                    <div class="channel-card cod">
                                        <div class="channel-background"></div>
                                        <div class="channel-content">
                                            <div class="channel-icon">
                                                <svg class="channel-icon-svg" viewBox="0 0 24 24" fill="currentColor">
                                                    <path
                                                        d="M7.8 2h8.4C19.4 2 22 4.6 22 7.8v8.4a5.8 5.8 0 0 1-5.8 5.8H7.8C4.6 22 2 19.4 2 16.2V7.8A5.8 5.8 0 0 1 7.8 2z" />
                                                </svg>
                                            </div>
                                            <div class="channel-info">
                                                <div class="channel-name">Call of Duty</div>
                                                <div class="channel-members">934 members</div>
                                            </div>
                                        </div>
                                    </div>

                                    <!-- General Gaming Channel -->
                                    <div class="channel-card general">
                                        <div class="channel-background"></div>
                                        <div class="channel-content">
                                            <div class="channel-icon">
                                                <svg class="channel-icon-svg" viewBox="0 0 24 24" fill="currentColor">
                                                    <path
                                                        d="M7.8 2h8.4C19.4 2 22 4.6 22 7.8v8.4a5.8 5.8 0 0 1-5.8 5.8H7.8C4.6 22 2 19.4 2 16.2V7.8A5.8 5.8 0 0 1 7.8 2m-.2 2A3.6 3.6 0 0 0 4 7.6v8.8C4 18.39 5.61 20 7.6 20h8.8a3.6 3.6 0 0 0 3.6-3.6V7.6C20 5.61 18.39 4 16.4 4H7.6m9.65 1.5a1.25 1.25 0 0 1 1.25 1.25A1.25 1.25 0 0 1 17.25 8 1.25 1.25 0 0 1 16 6.75a1.25 1.25 0 0 1 1.25-1.25M12 7a5 5 0 0 1 5 5 5 5 0 0 1-5 5 5 5 0 0 1-5-5 5 5 0 0 1 5-5m0 2a3 3 0 0 0-3 3 3 3 0 0 0 3 3 3 3 0 0 0 3-3 3 3 0 0 0-3-3z" />
                                                </svg>
                                            </div>
                                            <div class="channel-info">
                                                <div class="channel-name">General Gaming</div>
                                                <div class="channel-members">2.1k members</div>
                                            </div>
                                        </div>
                                        <div class="join-button">Join</div>
                                    </div>
                                </div>
                            </div>

                            <!-- Bottom Navigation -->
                            <div class="bottom-nav">
                                <button class="nav-btn">
                                    <svg class="nav-icon" viewBox="0 0 24 24" fill="currentColor">
                                        <path d="M10 20v-6h4v6h5v-8h3L12 3 2 12h3v8z" />
                                    </svg>
                                </button>
                                <button class="nav-btn">
                                    <svg class="nav-icon" viewBox="0 0 24 24" fill="currentColor">
                                        <path d="M19 13h-6v6h-2v-6H5v-2h6V5h2v6h6v2z" />
                                    </svg>
                                </button>
                                <button class="nav-btn active">
                                    <svg class="nav-icon" viewBox="0 0 24 24" fill="currentColor">
                                        <path
                                            d="M12 12c2.21 0 4-1.79 4-4s-1.79-4-4-4-4 1.79-4 4 1.79 4 4 4zm0 2c-2.67 0-8 1.34-8 4v2h16v-2c0-2.66-5.33-4-8-4z" />
                                    </svg>
                                </button>
                            </div>
                        </div>
                    </div>
                </section>

                <!-- Xbox Media Browser Screen -->
                <section id="xbox" class="screen-section">
                    <h2 class="section-title">Xbox Media Browser</h2>
                    <div class="phone-mockup" onclick="toggleFullscreen(this)">
                        <div class="screen xbox-screen">
                            <!-- App Bar -->
                            <div class="xbox-app-bar">
                                <div class="app-bar-content">
                                    <button class="back-button">
                                        <svg class="back-icon" viewBox="0 0 24 24" fill="currentColor">
                                            <path d="M20 11H7.83l5.59-5.59L12 4l-8 8 8 8 1.41-1.41L7.83 13H20v-2z" />
                                        </svg>
                                    </button>
                                    <h1 class="app-bar-title">Xbox Media</h1>
                                    <button class="refresh-button">
                                        <svg class="refresh-icon" viewBox="0 0 24 24" fill="currentColor">
                                            <path
                                                d="M17.65 6.35C16.2 4.9 14.21 4 12 4c-4.42 0-7.99 3.58-7.99 8s3.57 8 7.99 8c3.73 0 6.84-2.55 7.73-6h-2.08c-.82 2.33-3.04 4-5.65 4-3.31 0-6-2.69-6-6s2.69-6 6-6c1.66 0 3.14.69 4.22 1.78L13 11h7V4l-2.35 2.35z" />
                                        </svg>
                                    </button>
                                </div>

                                <!-- Tab Bar -->
                                <div class="xbox-tab-bar">
                                    <div class="xbox-tab active">
                                        <span class="tab-text">Screenshots</span>
                                    </div>
                                    <div class="xbox-tab">
                                        <span class="tab-text">Game Clips</span>
                                    </div>
                                </div>
                            </div>

                            <!-- Media Grid Content -->
                            <div class="xbox-content">
                                <div class="xbox-media-grid">
                                    <!-- Screenshot Item 1 -->
                                    <div class="xbox-media-item">
                                        <div class="media-thumbnail">
                                            <img src="assets/cod_screenshot.jpg" alt="COD Screenshot"
                                                class="thumbnail-image">
                                            <div class="media-type-indicator">
                                                <svg class="type-icon" viewBox="0 0 24 24" fill="currentColor">
                                                    <path
                                                        d="M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm3.5 6L12 10.5 8.5 8 12 5.5 15.5 8zM12 13.5l3.5 2.5-3.5 2.5L8.5 16l3.5-2.5z" />
                                                </svg>
                                            </div>
                                            <div class="download-status downloaded">
                                                <svg class="download-icon" viewBox="0 0 24 24" fill="currentColor">
                                                    <path d="M9 16.2L4.8 12l-1.4 1.4L9 19 21 7l-1.4-1.4L9 16.2z" />
                                                </svg>
                                            </div>
                                        </div>
                                        <div class="media-details">
                                            <div class="media-title">Epic Victory</div>
                                            <div class="media-game">Call of Duty</div>
                                        </div>
                                    </div>

                                    <!-- Game Clip Item 2 - Selected -->
                                    <div class="xbox-media-item selected">
                                        <div class="media-thumbnail">
                                            <div class="thumbnail-placeholder">
                                                <svg class="placeholder-icon" viewBox="0 0 24 24" fill="currentColor">
                                                    <path
                                                        d="M17 10.5V7c0-.55-.45-1-1-1H4c-.55 0-1 .45-1 1v10c0 .55.45 1 1 1h12c.55 0 1-.45 1-1v-3.5l4 4v-11l-4 4z" />
                                                </svg>
                                            </div>
                                            <div class="media-type-indicator video">
                                                <svg class="type-icon" viewBox="0 0 24 24" fill="currentColor">
                                                    <path
                                                        d="M17 10.5V7c0-.55-.45-1-1-1H4c-.55 0-1 .45-1 1v10c0 .55.45 1 1 1h12c.55 0 1-.45 1-1v-3.5l4 4v-11l-4 4z" />
                                                </svg>
                                                <span class="duration">0:30</span>
                                            </div>
                                        </div>
                                        <div class="media-details">
                                            <div class="media-title">Best Play</div>
                                            <div class="media-game">Halo Infinite</div>
                                        </div>
                                    </div>

                                    <!-- Screenshot Item 3 -->
                                    <div class="xbox-media-item">
                                        <div class="media-thumbnail">
                                            <div class="thumbnail-placeholder">
                                                <svg class="placeholder-icon" viewBox="0 0 24 24" fill="currentColor">
                                                    <path
                                                        d="M21 19V5c0-1.1-.9-2-2-2H5c-1.1 0-2 .9-2 2v14c0 1.1.9 2 2 2h14c1.1 0 2-.9 2-2zM8.5 13.5l2.5 3.01L14.5 12l4.5 6H5l3.5-4.5z" />
                                                </svg>
                                            </div>
                                            <div class="media-type-indicator">
                                                <svg class="type-icon" viewBox="0 0 24 24" fill="currentColor">
                                                    <path
                                                        d="M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm3.5 6L12 10.5 8.5 8 12 5.5 15.5 8zM12 13.5l3.5 2.5-3.5 2.5L8.5 16l3.5-2.5z" />
                                                </svg>
                                            </div>
                                        </div>
                                        <div class="media-details">
                                            <div class="media-title">Achievement</div>
                                            <div class="media-game">Forza Horizon</div>
                                        </div>
                                    </div>

                                    <!-- Game Clip Item 4 -->
                                    <div class="xbox-media-item">
                                        <div class="media-thumbnail">
                                            <div class="thumbnail-placeholder">
                                                <svg class="placeholder-icon" viewBox="0 0 24 24" fill="currentColor">
                                                    <path
                                                        d="M17 10.5V7c0-.55-.45-1-1-1H4c-.55 0-1 .45-1 1v10c0 .55.45 1 1 1h12c.55 0 1-.45 1-1v-3.5l4 4v-11l-4 4z" />
                                                </svg>
                                            </div>
                                            <div class="media-type-indicator video">
                                                <svg class="type-icon" viewBox="0 0 24 24" fill="currentColor">
                                                    <path
                                                        d="M17 10.5V7c0-.55-.45-1-1-1H4c-.55 0-1 .45-1 1v10c0 .55.45 1 1 1h12c.55 0 1-.45 1-1v-3.5l4 4v-11l-4 4z" />
                                                </svg>
                                                <span class="duration">1:15</span>
                                            </div>
                                        </div>
                                        <div class="media-details">
                                            <div class="media-title">Clutch Moment</div>
                                            <div class="media-game">Valorant</div>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <!-- Download Action Bar -->
                            <div class="xbox-action-bar">
                                <button class="download-button">
                                    Download (1)
                                </button>
                            </div>
                        </div>
                    </div>
                </section>

                <!-- Post Composition Screen -->
                <section id="compose" class="screen-section">
                    <h2 class="section-title">Post Composition</h2>
                    <div class="phone-mockup" onclick="toggleFullscreen(this)">
                        <div class="screen compose-screen">
                            <!-- App Bar -->
                            <div class="compose-app-bar">
                                <div class="app-bar-content">
                                    <button class="back-button">
                                        <svg class="back-icon" viewBox="0 0 24 24" fill="currentColor">
                                            <path d="M20 11H7.83l5.59-5.59L12 4l-8 8 8 8 1.41-1.41L7.83 13H20v-2z" />
                                        </svg>
                                    </button>
                                    <h1 class="app-bar-title">Create Post</h1>
                                    <button class="post-button">
                                        Post
                                    </button>
                                </div>
                            </div>

                            <!-- Content Area -->
                            <div class="compose-content">
                                <!-- Caption Section - At Top -->
                                <div class="caption-section">
                                    <div class="caption-label">Add a caption</div>
                                    <div class="caption-input-container">
                                        <textarea class="caption-input" placeholder="Add a caption..."
                                            maxlength="500">Check out this epic gaming moment! 🔥</textarea>
                                        <div class="character-count">
                                            <span class="current-count">42</span>/<span class="max-count">500</span>
                                        </div>
                                        <button class="clear-button">
                                            <svg class="clear-icon" viewBox="0 0 24 24" fill="currentColor">
                                                <path
                                                    d="M19 6.41L17.59 5 12 10.59 6.41 5 5 6.41 10.59 12 5 17.59 6.41 19 12 13.41 17.59 19 19 17.59 13.41 12z" />
                                            </svg>
                                        </button>
                                    </div>
                                </div>

                                <!-- Media Preview -->
                                <div class="media-preview-section">
                                    <div class="media-preview-container">
                                        <img src="assets/cod_screenshot.jpg" alt="Media Preview" class="preview-media">
                                    </div>
                                </div>

                                <!-- Channel Selection -->
                                <div class="channel-selection-section">
                                    <div class="channel-selection-widget">
                                        <div class="channel-selection-header">
                                            <svg class="channel-icon" viewBox="0 0 24 24" fill="currentColor">
                                                <path
                                                    d="M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm-2 15l-5-5 1.41-1.41L10 14.17l7.59-7.59L19 8l-9 9z" />
                                            </svg>
                                            <div class="channel-selection-text">
                                                <div class="channel-selection-title">Gaming Channel</div>
                                                <div class="channel-selection-subtitle">1.2k members</div>
                                            </div>
                                            <button class="change-channel-button">
                                                <svg class="change-icon" viewBox="0 0 24 24" fill="currentColor">
                                                    <path d="M7.41 8.59L12 13.17l4.59-4.58L18 10l-6 6-6-6 1.41-1.41z" />
                                                </svg>
                                            </button>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </section>
            </div>
        </main>
    </div>

    <script src="scripts/main.js"></script>
</body>

</html>