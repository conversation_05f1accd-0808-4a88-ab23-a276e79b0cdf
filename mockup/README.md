# GameFlex App Mockup

A comprehensive HTML/CSS mockup of all GameFlex mobile app screens, designed for easy import into Figma and other design tools.

## 🎯 Purpose

This mockup was created because the Flutter web version wasn't compatible with Figma's component import feature. The static HTML/CSS version provides pixel-perfect representations of all app screens that can be easily imported into design tools.

## 📱 Included Screens

### Authentication Flow
- **Splash Screen** - Loading state with spinner
- **Login/Signup Screen** - Email/password authentication with social login options
- **Username Selection** - Username creation with real-time validation

### Main App Flow
- **Home Screen** - Main feed with posts, tabs (Followed/Prime/Channels), and navigation
- **Upload Screen** - Gaming platform integration and media library access
- **Profile Screen** - User profile with linked accounts and settings
- **Channels Screen** - Gaming channel discovery with member counts
- **Xbox Media Browser** - Xbox screenshot and game clip selection
- **Post Composition** - New post creation with media preview and channel selection

## 🎨 Design Features

### Authentic GameFlex Styling
- **Color Palette**: Exact GameFlex brand colors including teal gradient (#8BE4CF → #2BA5A5 → #293642)
- **Typography**: Roboto font family matching the Flutter app
- **Components**: Pixel-perfect buttons, inputs, cards, and navigation elements
- **Icons**: Emoji-based icons for cross-platform consistency

### Interactive Elements
- Clickable navigation between screens
- Tab switching functionality
- Form validation simulation
- Media selection states
- Hover effects and animations

### Mobile-First Design
- iPhone-style phone mockup frames
- Responsive design for different screen sizes
- Touch-friendly interaction areas
- Proper mobile spacing and typography

## 🚀 Usage

### Local Development
```bash
# Start the mockup server
./serve-mockup.sh        # macOS/Linux
# or
./serve-mockup.ps1       # Windows

# Open in browser
http://localhost:8081
```

### Figma Import
1. **Open the mockup**: Navigate to `http://localhost:8081`
2. **Use Figma's import feature**: 
   - In Figma, go to File → Import
   - Choose "Import from URL" or use the Figma browser extension
   - Import individual screens or the entire page
3. **Extract components**: Use Figma's auto-layout and component extraction features

### Design Tool Compatibility
- ✅ **Figma** - Full compatibility with import features
- ✅ **Sketch** - Can import via web screenshots or HTML
- ✅ **Adobe XD** - Compatible with web import plugins
- ✅ **InVision** - Can capture screens for prototyping

## 📁 File Structure

```
mockup/
├── index.html              # Main HTML file with all screens
├── styles/
│   ├── main.css           # Base styles and layout
│   ├── components.css     # Reusable component styles
│   └── screens.css        # Screen-specific styles
├── scripts/
│   └── main.js           # Interactive functionality
├── assets/
│   ├── icons/            # App icons (SVG)
│   └── logos/            # Brand logos (SVG)
└── README.md             # This file
```

## 🎯 Key Features for Designers

### Accurate Representations
- **Exact Colors**: All GameFlex brand colors with CSS variables
- **Proper Spacing**: Consistent padding, margins, and gaps
- **Real Content**: Authentic usernames, post counts, and gaming references
- **State Variations**: Active/inactive states, selected items, validation states

### Component Library Ready
- **Modular CSS**: Components are easily extractable
- **Consistent Naming**: BEM-style CSS class naming
- **Reusable Elements**: Buttons, cards, inputs designed for reuse
- **Theme Variables**: Easy color and spacing customization

### Production-Ready Assets
- **SVG Icons**: Scalable vector graphics from the actual app
- **Optimized Images**: Compressed and web-optimized assets
- **Clean Code**: Well-structured HTML and CSS for easy modification

## 🔧 Customization

### Colors
Edit CSS variables in `styles/main.css`:
```css
:root {
    --gf-green: #28F4C3;
    --gf-teal: #2BA5A5;
    --gf-dark-blue: #293642;
    /* ... other colors */
}
```

### Content
Modify text content directly in `index.html` or add new screens by following the existing structure.

### Interactions
Extend functionality in `scripts/main.js` to add more interactive features.

## 📋 Notes for Figma Import

1. **Screen Resolution**: Mockups are designed at 375x812px (iPhone X dimensions)
2. **Component Extraction**: Each screen section can be extracted as a separate component
3. **Auto-Layout**: The CSS flexbox/grid layouts translate well to Figma's auto-layout
4. **Color Styles**: Import the CSS color variables as Figma color styles
5. **Text Styles**: Font sizes and weights are optimized for Figma text style creation

## 🎮 About GameFlex

GameFlex is a social gaming platform that allows users to share gaming content, connect with other gamers, and discover gaming communities. The app features integration with Xbox, PlayStation, and Steam platforms for seamless content sharing.

---

**Perfect for**: UI/UX designers, product managers, developers, and anyone needing accurate GameFlex app representations for design work, presentations, or documentation.
