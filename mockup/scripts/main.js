// GameFlex Mockup - Interactive Features

document.addEventListener('DOMContentLoaded', function () {
    // Smooth scrolling for navigation links
    const navLinks = document.querySelectorAll('.nav-link');
    navLinks.forEach(link => {
        link.addEventListener('click', function (e) {
            e.preventDefault();
            const targetId = this.getAttribute('href').substring(1);
            const targetElement = document.getElementById(targetId);

            if (targetElement) {
                targetElement.scrollIntoView({
                    behavior: 'smooth',
                    block: 'start'
                });

                // Update active nav link
                navLinks.forEach(l => l.classList.remove('active'));
                this.classList.add('active');
            }
        });
    });

    // Add fullscreen functionality to all phone mockups
    setupFullscreenFunctionality();

    // Tab switching functionality
    setupTabSwitching();

    // Interactive elements
    setupInteractiveElements();

    // Screen navigation simulation
    setupScreenNavigation();
});

function setupFullscreenFunctionality() {
    // Add click handlers to all phone mockups that don't already have onclick
    const phoneMockups = document.querySelectorAll('.phone-mockup');
    phoneMockups.forEach(mockup => {
        if (!mockup.hasAttribute('onclick')) {
            mockup.setAttribute('onclick', 'toggleFullscreen(this)');
        }

        // Add fullscreen indicator
        const indicator = document.createElement('div');
        indicator.className = 'fullscreen-indicator';
        indicator.innerHTML = '⛶';
        indicator.title = 'Click to view fullscreen';
        mockup.appendChild(indicator);
    });
}

// Global function for fullscreen toggle
function toggleFullscreen(element) {
    const mockup = element.closest('.phone-mockup');
    const isFullscreen = mockup.classList.contains('fullscreen');

    if (isFullscreen) {
        // Exit fullscreen
        mockup.classList.remove('fullscreen');
        document.body.classList.remove('fullscreen-active');

        // Remove overlay
        const overlay = document.querySelector('.fullscreen-overlay');
        if (overlay) {
            overlay.remove();
        }
    } else {
        // Enter fullscreen
        mockup.classList.add('fullscreen');
        document.body.classList.add('fullscreen-active');

        // Create overlay
        const overlay = document.createElement('div');
        overlay.className = 'fullscreen-overlay';
        overlay.onclick = () => toggleFullscreen(mockup);
        document.body.appendChild(overlay);

        // Add close button
        const closeBtn = document.createElement('button');
        closeBtn.className = 'fullscreen-close';
        closeBtn.innerHTML = '✕';
        closeBtn.onclick = (e) => {
            e.stopPropagation();
            toggleFullscreen(mockup);
        };
        mockup.appendChild(closeBtn);
    }
}

function setupTabSwitching() {
    // Feed tabs
    const feedTabs = document.querySelectorAll('.tab-button');
    feedTabs.forEach(tab => {
        tab.addEventListener('click', function () {
            feedTabs.forEach(t => t.classList.remove('active'));
            this.classList.add('active');
        });
    });

    // Xbox tabs
    const xboxTabs = document.querySelectorAll('.xbox-tab');
    xboxTabs.forEach(tab => {
        tab.addEventListener('click', function () {
            xboxTabs.forEach(t => t.classList.remove('active'));
            this.classList.add('active');

            // Update content based on tab
            const mediaItems = document.querySelectorAll('.media-item');
            if (this.textContent === 'Game Clips') {
                mediaItems.forEach(item => {
                    item.textContent = '🎬';
                });
            } else {
                mediaItems.forEach(item => {
                    item.textContent = '📸';
                });
            }
        });
    });
}

function setupInteractiveElements() {
    // Media item selection
    const mediaItems = document.querySelectorAll('.media-item');
    mediaItems.forEach(item => {
        item.addEventListener('click', function () {
            this.classList.toggle('selected');
            updateDownloadButton();
        });
    });

    // Platform cards hover effects
    const platformCards = document.querySelectorAll('.platform-card');
    platformCards.forEach(card => {
        card.addEventListener('click', function () {
            showToast('Platform integration coming soon!');
        });
    });

    // Library cards
    const libraryCards = document.querySelectorAll('.library-card');
    libraryCards.forEach(card => {
        card.addEventListener('click', function () {
            const libraryName = card.querySelector('.library-name').textContent;
            showToast(`Opening ${libraryName}...`);
        });
    });

    // Action buttons
    const actionBtns = document.querySelectorAll('.action-btn');
    actionBtns.forEach(btn => {
        btn.addEventListener('click', function () {
            const action = this.textContent.includes('❤️') ? 'like' :
                this.textContent.includes('💬') ? 'comment' : 'share';

            if (action === 'like') {
                this.classList.toggle('liked');
                const count = parseInt(this.textContent.match(/\d+/)[0]);
                const newCount = this.classList.contains('liked') ? count + 1 : count - 1;
                this.textContent = `❤️ ${newCount}`;
            }
        });
    });

    // Bottom navigation
    const navBtns = document.querySelectorAll('.nav-btn');
    navBtns.forEach(btn => {
        btn.addEventListener('click', function () {
            navBtns.forEach(b => b.classList.remove('active'));
            this.classList.add('active');
        });
    });
}

function setupScreenNavigation() {
    // Auth toggle
    const toggleLinks = document.querySelectorAll('.toggle-link');
    toggleLinks.forEach(link => {
        link.addEventListener('click', function (e) {
            e.preventDefault();
            const loginScreen = document.querySelector('.login-screen');
            const formSection = loginScreen.querySelector('.form-section');

            // Toggle between login and signup
            const isLogin = this.textContent === 'Sign Up';
            if (isLogin) {
                // Add confirm password field
                const confirmPasswordGroup = document.createElement('div');
                confirmPasswordGroup.className = 'input-group';
                confirmPasswordGroup.innerHTML = '<input type="password" placeholder="Confirm Password" class="gf-input">';

                const passwordGroup = formSection.querySelector('.input-group:nth-child(2)');
                passwordGroup.after(confirmPasswordGroup);

                // Update button text
                formSection.querySelector('.gf-button.primary').textContent = 'Sign Up';

                // Update toggle text
                this.textContent = 'Log In';
                this.previousElementSibling.textContent = 'Already have an account? ';
            } else {
                // Remove confirm password field
                const confirmPasswordGroup = formSection.querySelector('.input-group:nth-child(3)');
                if (confirmPasswordGroup) {
                    confirmPasswordGroup.remove();
                }

                // Update button text
                formSection.querySelector('.gf-button.primary').textContent = 'Log In';

                // Update toggle text
                this.textContent = 'Sign Up';
                this.previousElementSibling.textContent = "Don't have an account? ";
            }
        });
    });

    // Username validation simulation
    const usernameInput = document.querySelector('.username-screen .gf-input');
    if (usernameInput) {
        usernameInput.addEventListener('input', function () {
            const validationIndicator = document.querySelector('.validation-indicator');
            const value = this.value.trim();

            if (value.length === 0) {
                validationIndicator.className = 'validation-indicator';
                validationIndicator.innerHTML = '';
            } else if (value.length < 4) {
                validationIndicator.className = 'validation-indicator unavailable';
                validationIndicator.innerHTML = '<span class="check-icon">✗</span><span>Username too short</span>';
            } else if (value.length > 32) {
                validationIndicator.className = 'validation-indicator unavailable';
                validationIndicator.innerHTML = '<span class="check-icon">✗</span><span>Username too long</span>';
            } else {
                validationIndicator.className = 'validation-indicator available';
                validationIndicator.innerHTML = '<span class="check-icon">✓</span><span>Username is available</span>';
            }
        });
    }
}

function updateDownloadButton() {
    const selectedItems = document.querySelectorAll('.media-item.selected');
    const downloadBtn = document.querySelector('.xbox-actions .gf-button');

    if (downloadBtn) {
        const count = selectedItems.length;
        downloadBtn.textContent = count > 0 ? `Download Selected (${count})` : 'Download Selected';
        downloadBtn.disabled = count === 0;
    }
}

function showToast(message) {
    // Remove existing toast
    const existingToast = document.querySelector('.toast');
    if (existingToast) {
        existingToast.remove();
    }

    // Create new toast
    const toast = document.createElement('div');
    toast.className = 'toast';
    toast.textContent = message;
    toast.style.cssText = `
        position: fixed;
        top: 20px;
        right: 20px;
        background: var(--gf-green);
        color: #000;
        padding: 12px 24px;
        border-radius: 8px;
        font-weight: 600;
        z-index: 1000;
        animation: slideIn 0.3s ease;
    `;

    document.body.appendChild(toast);

    // Remove after 3 seconds
    setTimeout(() => {
        toast.style.animation = 'slideOut 0.3s ease';
        setTimeout(() => toast.remove(), 300);
    }, 3000);
}

// Add CSS animations
const style = document.createElement('style');
style.textContent = `
    @keyframes slideIn {
        from { transform: translateX(100%); opacity: 0; }
        to { transform: translateX(0); opacity: 1; }
    }
    
    @keyframes slideOut {
        from { transform: translateX(0); opacity: 1; }
        to { transform: translateX(100%); opacity: 0; }
    }
    
    .action-btn.liked {
        color: var(--gf-green) !important;
    }
    
    .nav-link.active {
        color: var(--gf-green);
        background: var(--gf-dark-background-40);
    }
`;
document.head.appendChild(style);
